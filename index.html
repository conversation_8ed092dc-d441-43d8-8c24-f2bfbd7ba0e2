<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="CinemaHub - سينما هاب | موقعك الأول لمشاهدة أحدث الأفلام والمسلسلات العربية والأجنبية بجودة عالية ومجاناً">
    <meta name="keywords" content="أفلام, مسلسلات, مشاهدة اونلاين, تحميل أفلام, أفلام عربية, مسلسلات تركية, CinemaHub, سينما هاب">
    <meta name="author" content="CinemaHub">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="CinemaHub - سينما هاب | شاهد أحدث الأفلام والمسلسلات">
    <meta property="og:description" content="موقعك الأول لمشاهدة أحدث الأفلام والمسلسلات العربية والأجنبية بجودة عالية ومجاناً">
    <meta property="og:type" content="website">
    <meta property="og:image" content="https://via.placeholder.com/1200x630/e50914/ffffff?text=CinemaHub">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="CinemaHub - سينما هاب">
    <meta name="twitter:description" content="موقعك الأول لمشاهدة أحدث الأفلام والمسلسلات">

    <title>CinemaHub - سينما هاب | شاهد أحدث الأفلام والمسلسلات مجاناً</title>

    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgcng9IjIwIiBmaWxsPSIjZTUwOTE0Ii8+PHRleHQgeD0iNTAiIHk9IjY1IiBmb250LXNpemU9IjYwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSI+8J+OrDwvdGV4dD48L3N2Zz4=">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#e50914">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="CinemaHub">

    <!-- External CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css" rel="stylesheet">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
    /* ===== CSS Variables ===== */
    :root {
        --primary-color: #e50914;
        --primary-dark: #b20710;
        --secondary-color: #221f1f;
        --dark-color: #141414;
        --darker-color: #0a0a0a;
        --light-color: #ffffff;
        --gray-color: #757575;
        --gray-light: #b3b3b3;
        --gray-dark: #333333;
        --success-color: #28a745;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
        --info-color: #17a2b8;

        --gradient-primary: linear-gradient(135deg, #e50914 0%, #b20710 100%);
        --gradient-dark: linear-gradient(135deg, #141414 0%, #0a0a0a 100%);
        --gradient-overlay: linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.8) 100%);
        --gradient-hero: linear-gradient(45deg, rgba(229,9,20,0.1) 0%, rgba(0,0,0,0.8) 100%);

        --font-family: 'Cairo', sans-serif;
        --font-size-xs: 0.75rem;
        --font-size-sm: 0.875rem;
        --font-size-base: 1rem;
        --font-size-lg: 1.125rem;
        --font-size-xl: 1.25rem;
        --font-size-2xl: 1.5rem;
        --font-size-3xl: 1.875rem;
        --font-size-4xl: 2.25rem;

        --border-radius-sm: 0.375rem;
        --border-radius: 0.5rem;
        --border-radius-lg: 0.75rem;
        --border-radius-xl: 1rem;
        --border-radius-2xl: 1.5rem;

        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

        --transition-fast: all 0.15s ease;
        --transition: all 0.3s ease;
        --transition-slow: all 0.5s ease;

        --z-dropdown: 1000;
        --z-sticky: 1020;
        --z-fixed: 1030;
        --z-modal-backdrop: 1040;
        --z-modal: 1050;
        --z-popover: 1060;
        --z-tooltip: 1070;
        --z-toast: 1080;
    }

    /* ===== Base Styles ===== */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    html {
        scroll-behavior: smooth;
        font-size: 16px;
    }

    body {
        font-family: var(--font-family);
        background: var(--dark-color);
        color: var(--light-color);
        line-height: 1.6;
        direction: rtl;
        overflow-x: hidden;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* ===== Typography ===== */
    h1, h2, h3, h4, h5, h6 {
        font-weight: 600;
        line-height: 1.2;
        margin-bottom: 0.5rem;
    }

    h1 { font-size: var(--font-size-4xl); }
    h2 { font-size: var(--font-size-3xl); }
    h3 { font-size: var(--font-size-2xl); }
    h4 { font-size: var(--font-size-xl); }
    h5 { font-size: var(--font-size-lg); }
    h6 { font-size: var(--font-size-base); }

    p {
        margin-bottom: 1rem;
        color: var(--gray-light);
    }

    a {
        color: var(--primary-color);
        text-decoration: none;
        transition: var(--transition);
    }

    a:hover {
        color: var(--primary-dark);
    }

    /* ===== Loading Screen ===== */
    #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--gradient-dark);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease, visibility 0.5s ease;
    }

    #loading-screen.hidden {
        opacity: 0;
        visibility: hidden;
    }

    .loading-spinner {
        text-align: center;
        animation: fadeInUp 0.8s ease;
    }

    .loading-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .loading-logo-icon {
        position: relative;
        width: 80px;
        height: 80px;
        background: var(--gradient-primary);
        border-radius: var(--border-radius-xl);
        display: flex;
        align-items: center;
        justify-content: center;
        animation: pulse 2s ease-in-out infinite;
        box-shadow: var(--shadow-2xl);
    }

    .loading-logo-icon .fa-film {
        font-size: 2.5rem;
        color: var(--light-color);
    }

    .loading-logo-icon .logo-play {
        position: absolute;
        top: -8px;
        right: -8px;
        font-size: 1.5rem;
        color: #ffd700;
        animation: playPulse 2s ease-in-out infinite;
    }

    .loading-text {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }

    .loading-text .brand-text {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.25rem;
    }

    .loading-text .brand-subtitle {
        font-size: var(--font-size-lg);
        color: var(--gray-color);
        font-weight: 400;
    }

    .spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(229, 9, 20, 0.3);
        border-top: 3px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
    }

    .loading-spinner p {
        color: var(--gray-color);
        font-size: var(--font-size-lg);
        margin: 0;
    }

    /* ===== Animations ===== */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(229, 9, 20, 0.7);
        }
        50% {
            transform: scale(1.05);
            box-shadow: 0 0 0 10px rgba(229, 9, 20, 0);
        }
    }

    @keyframes playPulse {
        0%, 100% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.2);
            opacity: 0.8;
        }
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes slideInLeft {
        from {
            opacity: 0;
            transform: translateX(-100px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes zoomIn {
        from {
            opacity: 0;
            transform: scale(0.8);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    @keyframes float {
        0%, 100% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-10px);
        }
    }

    @keyframes glow {
        0%, 100% {
            box-shadow: 0 0 5px rgba(229, 9, 20, 0.5);
        }
        50% {
            box-shadow: 0 0 20px rgba(229, 9, 20, 0.8), 0 0 30px rgba(229, 9, 20, 0.6);
        }
    }

    /* ===== Header & Navigation ===== */
    .header {
        background: rgba(20, 20, 20, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        position: fixed;
        top: 0;
        width: 100%;
        z-index: var(--z-fixed);
        transition: var(--transition);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .header.scrolled {
        background: rgba(20, 20, 20, 0.98);
        box-shadow: var(--shadow-lg);
    }

    .navbar {
        padding: 0.75rem 0;
    }

    .navbar-brand {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: var(--light-color) !important;
        transition: var(--transition);
    }

    .navbar-brand:hover {
        transform: scale(1.02);
        color: var(--light-color) !important;
    }

    .logo-container {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .logo-icon {
        position: relative;
        width: 50px;
        height: 50px;
        background: var(--gradient-primary);
        border-radius: var(--border-radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        animation: glow 3s ease-in-out infinite;
        transition: var(--transition);
    }

    .logo-icon:hover {
        transform: rotate(5deg) scale(1.1);
    }

    .logo-icon .fa-film {
        font-size: 1.5rem;
        color: var(--light-color);
    }

    .logo-play {
        position: absolute;
        top: -6px;
        right: -6px;
        font-size: 1.2rem;
        color: #ffd700;
        animation: float 3s ease-in-out infinite;
    }

    .logo-text {
        display: flex;
        flex-direction: column;
    }

    .brand-text {
        font-size: var(--font-size-xl);
        font-weight: 700;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        line-height: 1;
    }

    .brand-subtitle {
        font-size: var(--font-size-sm);
        color: var(--gray-color);
        margin-top: -2px;
    }

    /* Navigation Links */
    .navbar-nav .nav-link {
        color: var(--light-color) !important;
        font-weight: 500;
        margin: 0 0.5rem;
        padding: 0.5rem 1rem !important;
        border-radius: var(--border-radius);
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .navbar-nav .nav-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: var(--gradient-primary);
        transition: var(--transition);
        z-index: -1;
    }

    .navbar-nav .nav-link:hover::before,
    .navbar-nav .nav-link.active::before {
        left: 0;
    }

    .navbar-nav .nav-link:hover,
    .navbar-nav .nav-link.active {
        color: var(--light-color) !important;
        transform: translateY(-2px);
    }

    .navbar-nav .nav-link i {
        margin-left: 0.5rem;
        font-size: 0.9rem;
    }

    /* Dropdown Menu */
    .dropdown-menu {
        background: var(--secondary-color);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-xl);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        padding: 0.5rem;
        margin-top: 0.5rem;
    }

    .dropdown-item {
        color: var(--light-color);
        padding: 0.75rem 1rem;
        border-radius: var(--border-radius);
        transition: var(--transition);
        font-weight: 500;
    }

    .dropdown-item:hover {
        background: var(--primary-color);
        color: var(--light-color);
        transform: translateX(-5px);
    }

    .dropdown-item:focus {
        background: var(--primary-color);
        color: var(--light-color);
    }

    /* Search Form */
    .search-form {
        margin-right: 1rem;
    }

    .search-container {
        position: relative;
        display: flex;
        align-items: center;
    }

    .search-input {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: var(--light-color);
        border-radius: var(--border-radius-2xl);
        padding: 0.75rem 3rem 0.75rem 1rem;
        width: 300px;
        transition: var(--transition);
        font-size: var(--font-size-sm);
    }

    .search-input:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: var(--primary-color);
        color: var(--light-color);
        outline: none;
        box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
        width: 350px;
    }

    .search-input::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .search-btn {
        position: absolute;
        left: 8px;
        background: transparent;
        border: none;
        color: var(--light-color);
        padding: 0.5rem;
        border-radius: 50%;
        transition: var(--transition);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .search-btn:hover {
        background: var(--primary-color);
        color: var(--light-color);
        transform: scale(1.1);
    }

    /* Mobile Navigation */
    .navbar-toggler {
        border: none;
        padding: 0.5rem;
        border-radius: var(--border-radius);
        transition: var(--transition);
    }

    .navbar-toggler:focus {
        box-shadow: none;
    }

    .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    }

    @media (max-width: 991.98px) {
        .navbar-collapse {
            background: var(--secondary-color);
            border-radius: var(--border-radius-lg);
            margin-top: 1rem;
            padding: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .search-input {
            width: 100%;
        }

        .search-input:focus {
            width: 100%;
        }

        .search-form {
            margin-right: 0;
            margin-top: 1rem;
        }

        .logo-text .brand-subtitle {
            display: none;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
        }

        .brand-text {
            font-size: var(--font-size-lg);
        }
    }

    /* ===== Hero Section ===== */
    .hero-section {
        height: 80vh;
        position: relative;
        margin-top: 80px;
        overflow: hidden;
        border-radius: 0 0 var(--border-radius-2xl) var(--border-radius-2xl);
    }

    .hero-slider {
        height: 100%;
        width: 100%;
    }

    .hero-slide {
        height: 100%;
        background-size: cover;
        background-position: center;
        position: relative;
        display: flex;
        align-items: center;
    }

    .hero-slide::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--gradient-hero);
        z-index: 1;
    }

    .hero-content {
        position: relative;
        z-index: 2;
        max-width: 600px;
        padding: 0 2rem;
        animation: slideInRight 1s ease;
    }

    .hero-title {
        font-size: var(--font-size-4xl);
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        line-height: 1.1;
    }

    .hero-description {
        font-size: var(--font-size-lg);
        margin-bottom: 2rem;
        opacity: 0.9;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
        line-height: 1.6;
    }

    .hero-meta {
        display: flex;
        gap: 1rem;
        margin-bottom: 2rem;
        flex-wrap: wrap;
    }

    .hero-meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(255, 255, 255, 0.1);
        padding: 0.5rem 1rem;
        border-radius: var(--border-radius-2xl);
        font-size: var(--font-size-sm);
        backdrop-filter: blur(10px);
    }

    .hero-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .btn-hero {
        padding: 1rem 2rem;
        border-radius: var(--border-radius-2xl);
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: var(--transition);
        font-size: var(--font-size-base);
        border: none;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .btn-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: var(--transition-slow);
    }

    .btn-hero:hover::before {
        left: 100%;
    }

    .btn-hero.primary {
        background: var(--gradient-primary);
        color: var(--light-color);
        box-shadow: var(--shadow-lg);
    }

    .btn-hero.secondary {
        background: rgba(255, 255, 255, 0.2);
        color: var(--light-color);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .btn-hero:hover {
        transform: translateY(-3px);
        box-shadow: var(--shadow-2xl);
        color: var(--light-color);
    }

    /* Swiper Navigation */
    .swiper-button-next,
    .swiper-button-prev {
        color: var(--light-color);
        background: rgba(0, 0, 0, 0.5);
        width: 50px;
        height: 50px;
        border-radius: 50%;
        transition: var(--transition);
    }

    .swiper-button-next:hover,
    .swiper-button-prev:hover {
        background: var(--primary-color);
        transform: scale(1.1);
    }

    .swiper-button-next::after,
    .swiper-button-prev::after {
        font-size: 1.2rem;
    }

    .swiper-pagination-bullet {
        background: rgba(255, 255, 255, 0.5);
        opacity: 1;
        transition: var(--transition);
    }

    .swiper-pagination-bullet-active {
        background: var(--primary-color);
        transform: scale(1.2);
    }

    /* ===== Main Content ===== */
    .main-content {
        padding: 4rem 0;
        background: var(--dark-color);
    }

    .content-section {
        margin-bottom: 4rem;
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid rgba(255, 255, 255, 0.1);
        position: relative;
    }

    .section-header::after {
        content: '';
        position: absolute;
        bottom: -2px;
        right: 0;
        width: 100px;
        height: 2px;
        background: var(--gradient-primary);
        border-radius: var(--border-radius);
    }

    .section-title {
        font-size: var(--font-size-2xl);
        font-weight: 600;
        color: var(--light-color);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin: 0;
    }

    .section-title i {
        color: var(--primary-color);
        font-size: 1.5rem;
        animation: float 3s ease-in-out infinite;
    }

    .view-all-btn {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        transition: var(--transition);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: var(--border-radius-lg);
        border: 1px solid transparent;
    }

    .view-all-btn:hover {
        color: var(--light-color);
        background: var(--primary-color);
        border-color: var(--primary-color);
        transform: translateX(-5px);
    }

    /* ===== Movies Grid ===== */
    .movies-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1.5rem;
        margin: 2rem 0;
    }

    .movie-card {
        background: var(--secondary-color);
        border-radius: var(--border-radius-xl);
        overflow: hidden;
        transition: var(--transition);
        cursor: pointer;
        border: 1px solid rgba(255, 255, 255, 0.1);
        position: relative;
    }

    .movie-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: var(--shadow-2xl);
        border-color: var(--primary-color);
    }

    .movie-poster {
        position: relative;
        height: 300px;
        overflow: hidden;
        border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
    }

    .movie-poster img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: var(--transition-slow);
    }

    .movie-card:hover .movie-poster img {
        transform: scale(1.1);
    }

    .movie-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: var(--transition);
        backdrop-filter: blur(5px);
    }

    .movie-card:hover .movie-overlay {
        opacity: 1;
    }

    .play-btn {
        background: var(--gradient-primary);
        color: var(--light-color);
        border: none;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        font-size: 1.5rem;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: var(--transition);
        cursor: pointer;
        box-shadow: var(--shadow-lg);
    }

    .play-btn:hover {
        background: var(--primary-dark);
        transform: scale(1.2);
        box-shadow: var(--shadow-2xl);
    }

    .quality-badge {
        position: absolute;
        top: 0.75rem;
        right: 0.75rem;
        background: var(--gradient-primary);
        color: var(--light-color);
        padding: 0.25rem 0.75rem;
        border-radius: var(--border-radius);
        font-size: var(--font-size-xs);
        font-weight: 600;
        box-shadow: var(--shadow-md);
    }

    .movie-info {
        padding: 1.25rem;
    }

    .movie-title {
        font-size: var(--font-size-base);
        font-weight: 600;
        margin-bottom: 0.75rem;
        color: var(--light-color);
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        min-height: 2.6rem;
    }

    .movie-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: var(--font-size-sm);
        color: var(--gray-color);
    }

    .movie-year {
        background: rgba(255, 255, 255, 0.1);
        padding: 0.25rem 0.5rem;
        border-radius: var(--border-radius);
        font-weight: 500;
    }

    .movie-rating {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-weight: 500;
    }

    .movie-rating i {
        color: #ffd700;
    }

    /* ===== Footer ===== */
    .footer {
        background: var(--gradient-dark);
        padding: 3rem 0 1rem;
        margin-top: 4rem;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .footer-widget {
        margin-bottom: 2rem;
    }

    .footer-logo {
        margin-bottom: 1rem;
    }

    .footer-logo-icon {
        width: 40px;
        height: 40px;
        background: var(--gradient-primary);
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        justify-content: center;
        animation: glow 3s ease-in-out infinite;
    }

    .footer-widget h6 {
        color: var(--light-color);
        font-weight: 600;
        margin-bottom: 1rem;
        font-size: var(--font-size-lg);
    }

    .footer-widget p {
        color: var(--gray-color);
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }

    .footer-links {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .footer-links li {
        margin-bottom: 0.5rem;
    }

    .footer-links a {
        color: var(--gray-color);
        text-decoration: none;
        transition: var(--transition);
        display: flex;
        align-items: center;
        padding: 0.25rem 0;
    }

    .footer-links a:hover {
        color: var(--primary-color);
        transform: translateX(-5px);
    }

    .footer-links a::before {
        content: '▶';
        margin-left: 0.5rem;
        font-size: 0.7rem;
        opacity: 0;
        transition: var(--transition);
    }

    .footer-links a:hover::before {
        opacity: 1;
    }

    .social-links {
        display: flex;
        gap: 1rem;
        margin-top: 1rem;
    }

    .social-link {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.1);
        color: var(--light-color);
        border-radius: 50%;
        text-decoration: none;
        transition: var(--transition);
        font-size: 1.2rem;
    }

    .social-link:hover {
        background: var(--primary-color);
        color: var(--light-color);
        transform: translateY(-3px) scale(1.1);
    }

    .newsletter-form .input-group {
        margin-top: 1rem;
    }

    .newsletter-form .form-control {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: var(--light-color);
        border-radius: var(--border-radius) 0 0 var(--border-radius);
    }

    .newsletter-form .form-control:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: var(--primary-color);
        color: var(--light-color);
        box-shadow: none;
    }

    .newsletter-form .form-control::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .newsletter-form .btn {
        border-radius: 0 var(--border-radius) var(--border-radius) 0;
        background: var(--gradient-primary);
        border: none;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
    }

    .footer-divider {
        border: none;
        height: 1px;
        background: rgba(255, 255, 255, 0.1);
        margin: 2rem 0 1rem;
    }

    .footer-bottom {
        padding-top: 1rem;
    }

    .copyright {
        color: var(--gray-color);
        margin: 0;
        font-size: var(--font-size-sm);
    }

    .footer-bottom-links {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        gap: 1.5rem;
        justify-content: flex-end;
    }

    .footer-bottom-links a {
        color: var(--gray-color);
        text-decoration: none;
        font-size: var(--font-size-sm);
        transition: var(--transition);
    }

    .footer-bottom-links a:hover {
        color: var(--primary-color);
    }

    /* ===== Back to Top Button ===== */
    .back-to-top {
        position: fixed;
        bottom: 2rem;
        left: 2rem;
        width: 50px;
        height: 50px;
        background: var(--gradient-primary);
        color: var(--light-color);
        border: none;
        border-radius: 50%;
        font-size: 1.2rem;
        cursor: pointer;
        transition: var(--transition);
        z-index: var(--z-fixed);
        opacity: 0;
        visibility: hidden;
        transform: translateY(20px);
        box-shadow: var(--shadow-lg);
    }

    .back-to-top.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .back-to-top:hover {
        background: var(--primary-dark);
        transform: translateY(-5px) scale(1.1);
        box-shadow: var(--shadow-2xl);
    }

    /* ===== Font Size Control ===== */
    .font-size-control {
        position: fixed;
        top: 50%;
        left: 1rem;
        transform: translateY(-50%);
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        z-index: var(--z-fixed);
        background: rgba(0, 0, 0, 0.8);
        padding: 0.75rem;
        border-radius: var(--border-radius-lg);
        backdrop-filter: blur(10px);
    }

    .font-size-btn {
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.1);
        color: var(--light-color);
        border: none;
        border-radius: var(--border-radius);
        cursor: pointer;
        transition: var(--transition);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 0.7rem;
        position: relative;
    }

    .font-size-btn:hover,
    .font-size-btn.active {
        background: var(--primary-color);
        transform: scale(1.1);
    }

    .font-size-btn .fa-font {
        font-size: 0.8rem;
        margin-bottom: 0.1rem;
    }

    .size-indicator {
        font-size: 0.6rem;
        font-weight: bold;
    }

    /* ===== Responsive Design ===== */
    @media (max-width: 1200px) {
        .movies-grid {
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 1.25rem;
        }
    }

    @media (max-width: 768px) {
        .hero-section {
            height: 60vh;
            margin-top: 70px;
        }

        .hero-title {
            font-size: var(--font-size-3xl);
        }

        .hero-description {
            font-size: var(--font-size-base);
        }

        .hero-buttons {
            flex-direction: column;
            gap: 0.75rem;
        }

        .btn-hero {
            padding: 0.75rem 1.5rem;
            justify-content: center;
        }

        .movies-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .movie-poster {
            height: 250px;
        }

        .section-title {
            font-size: var(--font-size-xl);
        }

        .footer-bottom-links {
            justify-content: center;
            margin-top: 1rem;
        }

        .font-size-control {
            display: none;
        }

        .back-to-top {
            bottom: 1rem;
            left: 1rem;
            width: 45px;
            height: 45px;
        }
    }

    @media (max-width: 576px) {
        .movies-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 0.75rem;
        }

        .movie-poster {
            height: 200px;
        }

        .hero-content {
            padding: 0 1rem;
        }

        .hero-meta {
            flex-direction: column;
            gap: 0.5rem;
        }

        .main-content {
            padding: 2rem 0;
        }

        .content-section {
            margin-bottom: 2rem;
        }
    }

    /* ===== Utility Classes ===== */
    .text-gradient {
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .bg-gradient {
        background: var(--gradient-primary);
    }

    .shadow-glow {
        box-shadow: 0 0 20px rgba(229, 9, 20, 0.3);
    }

    .border-gradient {
        border: 2px solid;
        border-image: var(--gradient-primary) 1;
    }

    .hover-scale:hover {
        transform: scale(1.05);
    }

    .hover-lift:hover {
        transform: translateY(-5px);
    }

    .fade-in {
        animation: fadeInUp 0.8s ease;
    }

    .slide-in-right {
        animation: slideInRight 0.8s ease;
    }

    .slide-in-left {
        animation: slideInLeft 0.8s ease;
    }

    .zoom-in {
        animation: zoomIn 0.8s ease;
    }

    /* ===== Font Size Classes ===== */
    .responsive-text.font-small {
        font-size: 0.9rem;
    }

    .responsive-text.font-small h1 { font-size: 2rem; }
    .responsive-text.font-small h2 { font-size: 1.75rem; }
    .responsive-text.font-small h3 { font-size: 1.5rem; }
    .responsive-text.font-small .movie-title { font-size: 0.9rem; }
    .responsive-text.font-small .hero-title { font-size: 2.5rem; }

    .responsive-text.font-medium {
        font-size: 1rem;
    }

    .responsive-text.font-large {
        font-size: 1.1rem;
    }

    .responsive-text.font-large h1 { font-size: 2.5rem; }
    .responsive-text.font-large h2 { font-size: 2.25rem; }
    .responsive-text.font-large h3 { font-size: 2rem; }
    .responsive-text.font-large .movie-title { font-size: 1.1rem; }
    .responsive-text.font-large .hero-title { font-size: 3rem; }
    </style>
</head>
<body class="responsive-text font-medium">
    <!-- Loading Screen -->
    <div id="loading-screen">
        <div class="loading-spinner">
            <div class="loading-logo">
                <div class="logo-icon loading-logo-icon">
                    <i class="fas fa-film"></i>
                    <i class="fas fa-play-circle logo-play"></i>
                </div>
                <div class="loading-text">
                    <span class="brand-text">CinemaHub</span>
                    <span class="brand-subtitle">سينما هاب</span>
                </div>
            </div>
            <div class="spinner"></div>
            <p>جاري تحميل أحدث الأفلام والمسلسلات...</p>
        </div>
    </div>

    <!-- Header -->
    <header class="header" id="header">
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container">
                <a class="navbar-brand" href="#" onclick="scrollToTop()">
                    <div class="logo-container">
                        <div class="logo-icon">
                            <i class="fas fa-film"></i>
                            <i class="fas fa-play-circle logo-play"></i>
                        </div>
                        <div class="logo-text">
                            <span class="brand-text">CinemaHub</span>
                            <span class="brand-subtitle">سينما هاب</span>
                        </div>
                    </div>
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link active" href="#home" onclick="scrollToSection('home')">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#movies" onclick="scrollToSection('movies')">
                                <i class="fas fa-film"></i> الأفلام
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#series" onclick="scrollToSection('series')">
                                <i class="fas fa-tv"></i> المسلسلات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#top-rated" onclick="scrollToSection('top-rated')">
                                <i class="fas fa-star"></i> الأعلى تقييماً
                            </a>
                        </li>
                    </ul>

                    <!-- Search Form -->
                    <form class="d-flex search-form" role="search" onsubmit="handleSearch(event)">
                        <div class="search-container">
                            <input class="form-control search-input" type="search" placeholder="ابحث عن فيلم أو مسلسل..." aria-label="Search" id="searchInput">
                            <button class="btn search-btn" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero-section" id="home">
        <div class="hero-slider swiper" id="heroSlider">
            <div class="swiper-wrapper" id="heroWrapper">
                <!-- Hero slides will be dynamically loaded -->
            </div>
            <div class="swiper-pagination"></div>
            <div class="swiper-button-next"></div>
            <div class="swiper-button-prev"></div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Latest Movies Section -->
            <section class="content-section" id="movies" data-aos="fade-up">
                <div class="section-header">
                    <h2 class="section-title">
                        <i class="fas fa-film"></i>
                        أحدث الأفلام
                    </h2>
                    <a href="#" class="view-all-btn" onclick="showAllMovies()">
                        عرض الكل <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
                <div class="movies-grid" id="latest-movies">
                    <!-- Movies will be dynamically loaded -->
                </div>
            </section>

            <!-- Latest Series Section -->
            <section class="content-section" id="series" data-aos="fade-up">
                <div class="section-header">
                    <h2 class="section-title">
                        <i class="fas fa-tv"></i>
                        أحدث المسلسلات
                    </h2>
                    <a href="#" class="view-all-btn" onclick="showAllSeries()">
                        عرض الكل <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
                <div class="movies-grid" id="latest-series">
                    <!-- Series will be dynamically loaded -->
                </div>
            </section>

            <!-- Top Rated Section -->
            <section class="content-section" id="top-rated" data-aos="fade-up">
                <div class="section-header">
                    <h2 class="section-title">
                        <i class="fas fa-star"></i>
                        الأعلى تقييماً
                    </h2>
                    <a href="#" class="view-all-btn" onclick="showTopRated()">
                        عرض الكل <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
                <div class="movies-grid" id="top-rated-content">
                    <!-- Top rated content will be dynamically loaded -->
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6">
                    <div class="footer-widget">
                        <div class="footer-logo d-flex align-items-center mb-3">
                            <div class="footer-logo-icon">
                                <i class="fas fa-film" style="color: white; font-size: 1.2rem;"></i>
                            </div>
                            <div class="ms-3">
                                <h5 class="text-gradient mb-0">CinemaHub</h5>
                                <small class="text-muted">سينما هاب</small>
                            </div>
                        </div>
                        <p>موقعك الأول لمشاهدة أحدث الأفلام والمسلسلات العربية والأجنبية بجودة عالية ومجاناً. استمتع بتجربة مشاهدة لا تُنسى مع مكتبة ضخمة من المحتوى المتنوع.</p>
                        <div class="social-links">
                            <a href="#" class="social-link" title="فيسبوك">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="social-link" title="تويتر">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="social-link" title="إنستغرام">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="social-link" title="يوتيوب">
                                <i class="fab fa-youtube"></i>
                            </a>
                            <a href="#" class="social-link" title="تيليجرام">
                                <i class="fab fa-telegram"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6">
                    <div class="footer-widget">
                        <h6>الأقسام</h6>
                        <ul class="footer-links">
                            <li><a href="#movies" onclick="scrollToSection('movies')">الأفلام</a></li>
                            <li><a href="#series" onclick="scrollToSection('series')">المسلسلات</a></li>
                            <li><a href="#top-rated" onclick="scrollToSection('top-rated')">الأعلى تقييماً</a></li>
                            <li><a href="#" onclick="showAllMovies()">أحدث الأفلام</a></li>
                            <li><a href="#" onclick="showAllSeries()">أحدث المسلسلات</a></li>
                        </ul>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6">
                    <div class="footer-widget">
                        <h6>الأنواع</h6>
                        <ul class="footer-links">
                            <li><a href="#">أفلام أكشن</a></li>
                            <li><a href="#">أفلام كوميديا</a></li>
                            <li><a href="#">أفلام رعب</a></li>
                            <li><a href="#">أفلام رومانسية</a></li>
                            <li><a href="#">أفلام خيال علمي</a></li>
                        </ul>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6">
                    <div class="footer-widget">
                        <h6>روابط مفيدة</h6>
                        <ul class="footer-links">
                            <li><a href="#">من نحن</a></li>
                            <li><a href="#">اتصل بنا</a></li>
                            <li><a href="#">سياسة الخصوصية</a></li>
                            <li><a href="#">شروط الاستخدام</a></li>
                            <li><a href="#">الأسئلة الشائعة</a></li>
                        </ul>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6">
                    <div class="footer-widget">
                        <h6>النشرة الإخبارية</h6>
                        <p>اشترك للحصول على أحدث الأفلام والمسلسلات</p>
                        <form class="newsletter-form" onsubmit="subscribeNewsletter(event)">
                            <div class="input-group">
                                <input type="email" class="form-control" placeholder="بريدك الإلكتروني" required>
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <hr class="footer-divider">

            <div class="footer-bottom">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="copyright">
                            &copy; 2024 CinemaHub - سينما هاب. جميع الحقوق محفوظة.
                        </p>
                    </div>
                    <div class="col-md-6">
                        <ul class="footer-bottom-links">
                            <li><a href="#">الخصوصية</a></li>
                            <li><a href="#">الشروط</a></li>
                            <li><a href="#">ملفات تعريف الارتباط</a></li>
                            <li><a href="#">إعلانات</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="backToTop" onclick="scrollToTop()" title="العودة للأعلى">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Font Size Control -->
    <div class="font-size-control">
        <button class="font-size-btn" onclick="changeFontSize('small')" title="خط صغير">
            <i class="fas fa-font"></i>
            <span class="size-indicator">ص</span>
        </button>
        <button class="font-size-btn active" onclick="changeFontSize('medium')" title="خط متوسط">
            <i class="fas fa-font"></i>
            <span class="size-indicator">م</span>
        </button>
        <button class="font-size-btn" onclick="changeFontSize('large')" title="خط كبير">
            <i class="fas fa-font"></i>
            <span class="size-indicator">ك</span>
        </button>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6">
                    <div class="footer-widget">
                        <div class="footer-logo">
                            <div class="logo-container">
                                <div class="logo-icon footer-logo-icon">
                                    <i class="fas fa-film"></i>
                                    <i class="fas fa-play-circle logo-play"></i>
                                </div>
                                <div class="logo-text">
                                    <span class="brand-text">CinemaHub</span>
                                    <span class="brand-subtitle">سينما هاب</span>
                                </div>
                            </div>
                        </div>
                        <p>موقعك الأول لمشاهدة أحدث الأفلام والمسلسلات العربية والأجنبية بجودة عالية ومجاناً</p>
                        <div class="social-links">
                            <a href="#" class="social-link"><i class="fab fa-facebook"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6">
                    <div class="footer-widget">
                        <h6>روابط سريعة</h6>
                        <ul class="footer-links">
                            <li><a href="index.html">الرئيسية</a></li>
                            <li><a href="movies.html">الأفلام</a></li>
                            <li><a href="series.html">المسلسلات</a></li>
                            <li><a href="latest.html">أحدث الإضافات</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6">
                    <div class="footer-widget">
                        <h6>التصنيفات</h6>
                        <ul class="footer-links">
                            <li><a href="#">أكشن</a></li>
                            <li><a href="#">كوميديا</a></li>
                            <li><a href="#">دراما</a></li>
                            <li><a href="#">رعب</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="footer-widget">
                        <h6>اشترك في النشرة الإخبارية</h6>
                        <p>احصل على آخر الأخبار والإضافات الجديدة</p>
                        <form class="newsletter-form">
                            <div class="input-group">
                                <input type="email" class="form-control" placeholder="البريد الإلكتروني">
                                <button class="btn btn-primary" type="submit">اشتراك</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <hr class="footer-divider">
            <div class="footer-bottom">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="copyright">&copy; 2024 موقع الأفلام. جميع الحقوق محفوظة.</p>
                    </div>
                    <div class="col-md-6">
                        <ul class="footer-bottom-links">
                            <li><a href="privacy.html">سياسة الخصوصية</a></li>
                            <li><a href="terms.html">شروط الاستخدام</a></li>
                            <li><a href="contact.html">اتصل بنا</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Font Size Control -->
    <div class="font-size-control" id="fontSizeControl">
        <button class="font-size-btn" onclick="changeFontSize('small')" title="خط صغير">
            <i class="fas fa-font"></i>
            <span class="size-indicator">ص</span>
        </button>
        <button class="font-size-btn" onclick="changeFontSize('medium')" title="خط متوسط">
            <i class="fas fa-font"></i>
            <span class="size-indicator">م</span>
        </button>
        <button class="font-size-btn active" onclick="changeFontSize('large')" title="خط كبير">
            <i class="fas fa-font"></i>
            <span class="size-indicator">ك</span>
        </button>
    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script>
        // ===== Configuration =====
        const CONFIG = {
            TMDB_API_KEY: 'demo_key',
            TMDB_BASE_URL: 'https://api.themoviedb.org/3',
            TMDB_IMAGE_BASE: 'https://image.tmdb.org/t/p/w500',
            TMDB_BACKDROP_BASE: 'https://image.tmdb.org/t/p/w1280',
            LOADING_DELAY: 2000,
            ANIMATION_DURATION: 300
        };

        // ===== Demo Data =====
        const demoMovies = [
            {
                id: 1,
                title: 'أفاتار: طريق الماء',
                original_title: 'Avatar: The Way of Water',
                poster_path: 'https://image.tmdb.org/t/p/w500/s16H6tpK2utvwDtzZ8Qy4qm5Emw.jpg',
                backdrop_path: 'https://image.tmdb.org/t/p/w1280/s16H6tpK2utvwDtzZ8Qy4qm5Emw.jpg',
                release_date: '2022-12-16',
                vote_average: 7.7,
                overview: 'بعد أكثر من عقد من الأحداث الأولى، تعلم عائلة سولي المشاكل التي تتبعهم والأطوال التي سيذهبون إليها للحفاظ على سلامة بعضهم البعض.',
                genre_ids: [878, 12, 28],
                adult: false,
                video: false,
                popularity: 2547.711
            },
            {
                id: 2,
                title: 'الرجل العنكبوت: لا طريق للعودة',
                original_title: 'Spider-Man: No Way Home',
                poster_path: 'https://image.tmdb.org/t/p/w500/1g0dhYtq4irTY1GPXvft6k4YLjm.jpg',
                backdrop_path: 'https://image.tmdb.org/t/p/w1280/1g0dhYtq4irTY1GPXvft6k4YLjm.jpg',
                release_date: '2021-12-17',
                vote_average: 8.4,
                overview: 'بيتر باركر يكشف هويته كرجل عنكبوت ولا يعود قادراً على فصل حياته الطبيعية عن المخاطر العالية لكونه بطلاً خارقاً.',
                genre_ids: [28, 12, 878],
                adult: false,
                video: false,
                popularity: 1854.598
            },
            {
                id: 3,
                title: 'توب غان: مافريك',
                original_title: 'Top Gun: Maverick',
                poster_path: 'https://image.tmdb.org/t/p/w500/62HCnUTziyWcpDaBO2i1DX17ljH.jpg',
                backdrop_path: 'https://image.tmdb.org/t/p/w1280/62HCnUTziyWcpDaBO2i1DX17ljH.jpg',
                release_date: '2022-05-27',
                vote_average: 8.3,
                overview: 'بعد أكثر من ثلاثين عاماً من الخدمة كواحد من أفضل الطيارين البحريين، يعود بيت "مافريك" ميتشل إلى المكان الذي ينتمي إليه.',
                genre_ids: [28, 18],
                adult: false,
                video: false,
                popularity: 1456.789
            },
            {
                id: 4,
                title: 'دكتور سترينج في الكون المتعدد',
                original_title: 'Doctor Strange in the Multiverse of Madness',
                poster_path: 'https://image.tmdb.org/t/p/w500/9Gtg2DzBhmYamXBS1hKAhiwbBKS.jpg',
                backdrop_path: 'https://image.tmdb.org/t/p/w1280/9Gtg2DzBhmYamXBS1hKAhiwbBKS.jpg',
                release_date: '2022-05-06',
                vote_average: 7.3,
                overview: 'يسافر الدكتور سترينج عبر الكون المتعدد المجنون والخطير مع مساعدة حلفاء صوفيين جدد وآخرين مألوفين.',
                genre_ids: [14, 28, 12],
                adult: false,
                video: false,
                popularity: 1234.567
            },
            {
                id: 5,
                title: 'مينيونز: صعود جرو',
                original_title: 'Minions: The Rise of Gru',
                poster_path: 'https://image.tmdb.org/t/p/w500/wKiOkZTN9lUUUNZLmtnwubZYONg.jpg',
                backdrop_path: 'https://image.tmdb.org/t/p/w1280/wKiOkZTN9lUUUNZLmtnwubZYONg.jpg',
                release_date: '2022-07-01',
                vote_average: 7.3,
                overview: 'في السبعينيات، يكبر جرو في الضواحي ويصبح من أشد المعجبين بمجموعة من الأشرار الخارقين المعروفين باسم فيشوس 6.',
                genre_ids: [16, 35, 10751],
                adult: false,
                video: false,
                popularity: 987.654
            },
            {
                id: 6,
                title: 'ثور: الحب والرعد',
                original_title: 'Thor: Love and Thunder',
                poster_path: 'https://image.tmdb.org/t/p/w500/pIkRyD18kl4FhoCNQuWxWu5cBLM.jpg',
                backdrop_path: 'https://image.tmdb.org/t/p/w1280/pIkRyD18kl4FhoCNQuWxWu5cBLM.jpg',
                release_date: '2022-07-08',
                vote_average: 6.8,
                overview: 'يشرع ثور في رحلة لا تشبه أي شيء واجهه من قبل - البحث عن السلام الداخلي. لكن تقاعده يتعطل بسبب قاتل مجرة يُعرف باسم جور إله الجزار.',
                genre_ids: [28, 35, 14],
                adult: false,
                video: false,
                popularity: 876.543
            },
            {
                id: 7,
                title: 'الوحوش الرائعة: أسرار دمبلدور',
                original_title: 'Fantastic Beasts: The Secrets of Dumbledore',
                poster_path: 'https://image.tmdb.org/t/p/w500/jrgifaYeUtTnaH7NF5Drkgjg2MB.jpg',
                backdrop_path: 'https://image.tmdb.org/t/p/w1280/jrgifaYeUtTnaH7NF5Drkgjg2MB.jpg',
                release_date: '2022-04-15',
                vote_average: 6.7,
                overview: 'يقود البروفيسور ألبوس دمبلدور فريقاً من السحرة والساحرات وخباز واحد في مهمة خطيرة، حيث يواجهون وحوش جريندلوالد المتنامية.',
                genre_ids: [14, 12, 10751],
                adult: false,
                video: false,
                popularity: 765.432
            },
            {
                id: 8,
                title: 'جوراسيك وورلد دومينيون',
                original_title: 'Jurassic World Dominion',
                poster_path: 'https://image.tmdb.org/t/p/w500/kAVRgw7GgK1CfYEJq8ME6EvRIgU.jpg',
                backdrop_path: 'https://image.tmdb.org/t/p/w1280/kAVRgw7GgK1CfYEJq8ME6EvRIgU.jpg',
                release_date: '2022-06-10',
                vote_average: 7.0,
                overview: 'بعد أربع سنوات من تدمير إيسلا نوبلار، تعيش الديناصورات الآن - وتصطاد - جنباً إلى جنب مع البشر في جميع أنحاء العالم.',
                genre_ids: [28, 12, 878],
                adult: false,
                video: false,
                popularity: 654.321
            }
        ];

        const demoSeries = [
            {
                id: 1,
                name: 'بيت التنين',
                original_name: 'House of the Dragon',
                poster_path: 'https://image.tmdb.org/t/p/w500/z2yahl2uefxDCl0nogcRBstwruJ.jpg',
                backdrop_path: 'https://image.tmdb.org/t/p/w1280/z2yahl2uefxDCl0nogcRBstwruJ.jpg',
                first_air_date: '2022-08-21',
                vote_average: 8.5,
                overview: 'تدور أحداث المسلسل قبل 200 عام من أحداث صراع العروش، ويحكي قصة آل تارغاريان.',
                genre_ids: [18, 10765, 10759],
                adult: false,
                popularity: 2345.678
            },
            {
                id: 2,
                name: 'حلقات القوة',
                original_name: 'The Lord of the Rings: The Rings of Power',
                poster_path: 'https://image.tmdb.org/t/p/w500/mYLOqiStMxDK3fYZFirgrMt8z5d.jpg',
                backdrop_path: 'https://image.tmdb.org/t/p/w1280/mYLOqiStMxDK3fYZFirgrMt8z5d.jpg',
                first_air_date: '2022-09-02',
                vote_average: 7.3,
                overview: 'ملحمة تدور أحداثها في الأرض الوسطى، تستكشف فترة العصر الثاني من تاريخ الأرض الوسطى.',
                genre_ids: [10765, 18, 10759],
                adult: false,
                popularity: 1987.654
            },
            {
                id: 3,
                name: 'أشياء غريبة',
                original_name: 'Stranger Things',
                poster_path: 'https://image.tmdb.org/t/p/w500/49WJfeN0moxb9IPfGn8AIqMGskD.jpg',
                backdrop_path: 'https://image.tmdb.org/t/p/w1280/49WJfeN0moxb9IPfGn8AIqMGskD.jpg',
                first_air_date: '2016-07-15',
                vote_average: 8.7,
                overview: 'عندما يختفي صبي صغير، تكشف مدينته الصغيرة عن مؤامرة تشمل تجارب سرية وقوى خارقة للطبيعة مرعبة.',
                genre_ids: [18, 10765, 9648],
                adult: false,
                popularity: 1765.432
            },
            {
                id: 4,
                name: 'الدب',
                original_name: 'The Bear',
                poster_path: 'https://image.tmdb.org/t/p/w500/zPIug5giU8oug6Xes5K1sTfQJxY.jpg',
                backdrop_path: 'https://image.tmdb.org/t/p/w1280/zPIug5giU8oug6Xes5K1sTfQJxY.jpg',
                first_air_date: '2022-06-23',
                vote_average: 8.3,
                overview: 'طاهٍ شاب من المطاعم الراقية يعود إلى شيكاغو لإدارة مطعم ساندويتش العائلة.',
                genre_ids: [35, 18],
                adult: false,
                popularity: 1543.210
            },
            {
                id: 5,
                name: 'أوبي وان كينوبي',
                original_name: 'Obi-Wan Kenobi',
                poster_path: 'https://image.tmdb.org/t/p/w500/qJRB789ceLryrLvOKrZqLKr2CGf.jpg',
                backdrop_path: 'https://image.tmdb.org/t/p/w1280/qJRB789ceLryrLvOKrZqLKr2CGf.jpg',
                first_air_date: '2022-05-27',
                vote_average: 7.2,
                overview: 'خلال عهد الإمبراطورية الغالاكتية، يواجه أوبي وان كينوبي السابق أعظم هزيمة له.',
                genre_ids: [10765, 18, 10759],
                adult: false,
                popularity: 1321.098
            },
            {
                id: 6,
                name: 'مون نايت',
                original_name: 'Moon Knight',
                poster_path: 'https://image.tmdb.org/t/p/w500/x6FsYvt33846IQnDSLxSCS3ZXNy.jpg',
                backdrop_path: 'https://image.tmdb.org/t/p/w1280/x6FsYvt33846IQnDSLxSCS3ZXNy.jpg',
                first_air_date: '2022-03-30',
                vote_average: 7.3,
                overview: 'ستيفن غرانت، موظف متجر هدايا لطيف ومهذب، يعاني من فقدان الذاكرة ولديه ذكريات من حياة أخرى.',
                genre_ids: [10759, 18, 10765],
                adult: false,
                popularity: 1198.765
            },
            {
                id: 7,
                name: 'يوفوريا',
                original_name: 'Euphoria',
                poster_path: 'https://image.tmdb.org/t/p/w500/jtnfNzqZwN4E32FGGxx1YZaBWWf.jpg',
                backdrop_path: 'https://image.tmdb.org/t/p/w1280/jtnfNzqZwN4E32FGGxx1YZaBWWf.jpg',
                first_air_date: '2019-06-16',
                vote_average: 8.4,
                overview: 'مجموعة من طلاب المدارس الثانوية يتنقلون عبر الحب والصداقات أثناء التعامل مع الهوية والصدمات والمخدرات.',
                genre_ids: [18],
                adult: false,
                popularity: 1076.543
            },
            {
                id: 8,
                name: 'ذا بويز',
                original_name: 'The Boys',
                poster_path: 'https://image.tmdb.org/t/p/w500/mY7SeH4HFFxW1hiI6cWuwCRKptN.jpg',
                backdrop_path: 'https://image.tmdb.org/t/p/w1280/mY7SeH4HFFxW1hiI6cWuwCRKptN.jpg',
                first_air_date: '2019-07-26',
                vote_average: 8.7,
                overview: 'في عالم يتم فيه التلاعب بالأبطال الخارقين من قبل المشاهير والشركات، تنطلق مجموعة من اليقظين لفضح الحقيقة.',
                genre_ids: [10765, 35, 18],
                adult: false,
                popularity: 954.321
            }
        ];

        // ===== Global Variables =====
        let heroSwiper = null;
        let isLoading = false;
        let currentFontSize = 'medium';

        // ===== Utility Functions =====
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} position-fixed`;
            notification.style.cssText = `
                top: 100px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                animation: slideInRight 0.3s ease;
            `;
            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                    <span>${message}</span>
                    <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.getFullYear();
        }

        function truncateText(text, maxLength = 150) {
            if (text.length <= maxLength) return text;
            return text.substr(0, maxLength) + '...';
        }

        function generateStars(rating) {
            const fullStars = Math.floor(rating / 2);
            const halfStar = (rating / 2) % 1 >= 0.5;
            const emptyStars = 5 - fullStars - (halfStar ? 1 : 0);

            let stars = '';
            for (let i = 0; i < fullStars; i++) {
                stars += '<i class="fas fa-star"></i>';
            }
            if (halfStar) {
                stars += '<i class="fas fa-star-half-alt"></i>';
            }
            for (let i = 0; i < emptyStars; i++) {
                stars += '<i class="far fa-star"></i>';
            }
            return stars;
        }

        // ===== Content Creation Functions =====
        function createMovieCard(item, type = 'movie') {
            const title = item.title || item.name;
            const releaseDate = item.release_date || item.first_air_date;
            const year = releaseDate ? formatDate(releaseDate) : 'غير محدد';
            const rating = item.vote_average ? item.vote_average.toFixed(1) : 'N/A';
            const qualityBadge = type === 'movie' ? 'HD' : 'مسلسل';
            const poster = item.poster_path || 'https://via.placeholder.com/300x450/333/fff?text=No+Image';

            return `
                <div class="movie-card fade-in" data-aos="fade-up" onclick="openMovieDetails(${item.id}, '${title}', '${type}')">
                    <div class="movie-poster">
                        <img src="${poster}" alt="${title}" loading="lazy" onerror="this.src='https://via.placeholder.com/300x450/333/fff?text=No+Image'">
                        <div class="quality-badge">${qualityBadge}</div>
                        <div class="movie-overlay">
                            <button class="play-btn" onclick="event.stopPropagation(); playMovie(${item.id}, '${title}', '${type}')">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="movie-info">
                        <h3 class="movie-title">${title}</h3>
                        <div class="movie-meta">
                            <span class="movie-year">${year}</span>
                            <div class="movie-rating">
                                <i class="fas fa-star"></i>
                                <span>${rating}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function createHeroSlide(item, type = 'movie') {
            const title = item.title || item.name;
            const overview = truncateText(item.overview || 'لا يوجد وصف متاح', 200);
            const backdrop = item.backdrop_path || item.poster_path || 'https://via.placeholder.com/1280x720/333/fff?text=No+Image';
            const year = formatDate(item.release_date || item.first_air_date);
            const rating = item.vote_average ? item.vote_average.toFixed(1) : 'N/A';

            return `
                <div class="swiper-slide hero-slide" style="background-image: url('${backdrop}')">
                    <div class="container">
                        <div class="hero-content">
                            <h1 class="hero-title">${title}</h1>
                            <div class="hero-meta">
                                <div class="hero-meta-item">
                                    <i class="fas fa-calendar"></i>
                                    <span>${year}</span>
                                </div>
                                <div class="hero-meta-item">
                                    <i class="fas fa-star"></i>
                                    <span>${rating}/10</span>
                                </div>
                                <div class="hero-meta-item">
                                    <i class="fas fa-${type === 'movie' ? 'film' : 'tv'}"></i>
                                    <span>${type === 'movie' ? 'فيلم' : 'مسلسل'}</span>
                                </div>
                            </div>
                            <p class="hero-description">${overview}</p>
                            <div class="hero-buttons">
                                <button class="btn-hero primary" onclick="playMovie(${item.id}, '${title}', '${type}')">
                                    <i class="fas fa-play"></i>
                                    مشاهدة الآن
                                </button>
                                <button class="btn-hero secondary" onclick="openMovieDetails(${item.id}, '${title}', '${type}')">
                                    <i class="fas fa-info-circle"></i>
                                    المزيد من المعلومات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // ===== Content Loading Functions =====
        function loadHeroSlider() {
            const heroWrapper = document.getElementById('heroWrapper');
            if (!heroWrapper) return;

            const featuredContent = [
                ...demoMovies.slice(0, 3),
                ...demoSeries.slice(0, 2)
            ];

            heroWrapper.innerHTML = featuredContent.map((item, index) => {
                const type = item.title ? 'movie' : 'tv';
                return createHeroSlide(item, type);
            }).join('');

            // Initialize Swiper
            if (heroSwiper) {
                heroSwiper.destroy(true, true);
            }

            heroSwiper = new Swiper('#heroSlider', {
                loop: true,
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                effect: 'fade',
                fadeEffect: {
                    crossFade: true
                },
                speed: 1000,
            });
        }

        function loadMovies() {
            const container = document.getElementById('latest-movies');
            if (!container) return;

            container.innerHTML = demoMovies.map(movie => createMovieCard(movie, 'movie')).join('');
        }

        function loadSeries() {
            const container = document.getElementById('latest-series');
            if (!container) return;

            container.innerHTML = demoSeries.map(series => createMovieCard(series, 'tv')).join('');
        }

        function loadTopRated() {
            const container = document.getElementById('top-rated-content');
            if (!container) return;

            const topRated = [...demoMovies, ...demoSeries]
                .sort((a, b) => b.vote_average - a.vote_average)
                .slice(0, 8);

            container.innerHTML = topRated.map(item => {
                const type = item.title ? 'movie' : 'tv';
                return createMovieCard(item, type);
            }).join('');
        }

        // ===== Event Handlers =====
        function playMovie(id, title, type) {
            showNotification(`بدء تشغيل: ${title}`, 'success');
            // Redirect to movie details page
            window.location.href = `movie-details.html?id=${id}&title=${encodeURIComponent(title)}&type=${type}`;
        }

        function openMovieDetails(id, title, type) {
            showNotification(`فتح تفاصيل: ${title}`, 'info');
            // Redirect to movie details page
            window.location.href = `movie-details.html?id=${id}&title=${encodeURIComponent(title)}&type=${type}`;
        }

        function handleSearch(event) {
            event.preventDefault();
            const query = document.getElementById('searchInput').value.trim();

            if (!query) {
                showNotification('يرجى إدخال كلمة البحث', 'warning');
                return;
            }

            showNotification(`البحث عن: ${query}`, 'info');

            // Filter content based on search query
            const searchResults = [
                ...demoMovies.filter(movie =>
                    movie.title.toLowerCase().includes(query.toLowerCase()) ||
                    movie.original_title.toLowerCase().includes(query.toLowerCase())
                ),
                ...demoSeries.filter(series =>
                    series.name.toLowerCase().includes(query.toLowerCase()) ||
                    series.original_name.toLowerCase().includes(query.toLowerCase())
                )
            ];

            if (searchResults.length > 0) {
                displaySearchResults(searchResults, query);
            } else {
                showNotification('لم يتم العثور على نتائج', 'warning');
            }
        }

        function displaySearchResults(results, query) {
            // Create search results section
            const searchSection = document.createElement('section');
            searchSection.className = 'content-section';
            searchSection.id = 'search-results';
            searchSection.innerHTML = `
                <div class="section-header">
                    <h2 class="section-title">
                        <i class="fas fa-search"></i>
                        نتائج البحث عن: "${query}"
                    </h2>
                    <button class="btn btn-outline-light btn-sm" onclick="clearSearchResults()">
                        <i class="fas fa-times"></i>
                        إغلاق
                    </button>
                </div>
                <div class="movies-grid">
                    ${results.map(item => {
                        const type = item.title ? 'movie' : 'tv';
                        return createMovieCard(item, type);
                    }).join('')}
                </div>
            `;

            // Remove existing search results
            const existingResults = document.getElementById('search-results');
            if (existingResults) {
                existingResults.remove();
            }

            // Insert search results at the top of main content
            const mainContent = document.querySelector('.main-content .container');
            mainContent.insertBefore(searchSection, mainContent.firstChild);

            // Scroll to search results
            searchSection.scrollIntoView({ behavior: 'smooth' });
        }

        function clearSearchResults() {
            const searchResults = document.getElementById('search-results');
            if (searchResults) {
                searchResults.remove();
            }
            document.getElementById('searchInput').value = '';
        }

        function scrollToSection(sectionId) {
            const section = document.getElementById(sectionId);
            if (section) {
                section.scrollIntoView({ behavior: 'smooth' });
            }
        }

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        function showAllMovies() {
            showNotification('عرض جميع الأفلام', 'info');
            scrollToSection('movies');
        }

        function showAllSeries() {
            showNotification('عرض جميع المسلسلات', 'info');
            scrollToSection('series');
        }

        function showTopRated() {
            showNotification('عرض الأعلى تقييماً', 'info');
            scrollToSection('top-rated');
        }

        function changeFontSize(size) {
            const body = document.body;
            const buttons = document.querySelectorAll('.font-size-btn');

            // Remove all font size classes
            body.classList.remove('font-small', 'font-medium', 'font-large');
            buttons.forEach(btn => btn.classList.remove('active'));

            // Add new font size class
            body.classList.add(`font-${size}`);
            document.querySelector(`[onclick="changeFontSize('${size}')"]`).classList.add('active');

            currentFontSize = size;
            localStorage.setItem('fontSize', size);

            showNotification(`تم تغيير حجم الخط إلى: ${size === 'small' ? 'صغير' : size === 'medium' ? 'متوسط' : 'كبير'}`, 'success');
        }

        // ===== Initialization =====
        function initializeApp() {
            // Hide loading screen
            setTimeout(() => {
                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen) {
                    loadingScreen.classList.add('hidden');
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                    }, 500);
                }
            }, CONFIG.LOADING_DELAY);

            // Initialize AOS
            if (typeof AOS !== 'undefined') {
                AOS.init({
                    duration: 800,
                    easing: 'ease-in-out',
                    once: true,
                    offset: 100,
                    delay: 100
                });
            }

            // Load content
            loadHeroSlider();
            loadMovies();
            loadSeries();
            loadTopRated();

            // Initialize header scroll effect
            initializeHeaderScroll();

            // Initialize back to top button
            initializeBackToTop();

            // Load saved font size
            const savedFontSize = localStorage.getItem('fontSize') || 'medium';
            changeFontSize(savedFontSize);

            // Initialize navigation
            initializeNavigation();

            console.log('CinemaHub initialized successfully!');
        }

        function initializeHeaderScroll() {
            const header = document.getElementById('header');
            if (!header) return;

            window.addEventListener('scroll', () => {
                if (window.scrollY > 100) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            });
        }

        function initializeBackToTop() {
            const backToTopBtn = document.getElementById('backToTop');
            if (!backToTopBtn) return;

            window.addEventListener('scroll', () => {
                if (window.scrollY > 300) {
                    backToTopBtn.classList.add('show');
                } else {
                    backToTopBtn.classList.remove('show');
                }
            });

            backToTopBtn.addEventListener('click', scrollToTop);
        }

        function initializeNavigation() {
            // Update active nav link based on scroll position
            const navLinks = document.querySelectorAll('.nav-link[href^="#"]');
            const sections = document.querySelectorAll('section[id]');

            window.addEventListener('scroll', () => {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if (window.scrollY >= (sectionTop - 200)) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === `#${current}`) {
                        link.classList.add('active');
                    }
                });
            });
        }

        // ===== Error Handling =====
        function handleError(error, context = 'Unknown') {
            console.error(`Error in ${context}:`, error);
            showNotification('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.', 'error');
        }

        // ===== Performance Optimization =====
        function lazyLoadImages() {
            const images = document.querySelectorAll('img[data-src]');
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        }

        // ===== Keyboard Shortcuts =====
        function initializeKeyboardShortcuts() {
            document.addEventListener('keydown', (e) => {
                // Ctrl/Cmd + K for search
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    document.getElementById('searchInput').focus();
                }

                // Escape to clear search
                if (e.key === 'Escape') {
                    clearSearchResults();
                    document.getElementById('searchInput').blur();
                }

                // Home key to scroll to top
                if (e.key === 'Home' && !e.target.matches('input, textarea')) {
                    e.preventDefault();
                    scrollToTop();
                }
            });
        }

        // ===== Service Worker Registration =====
        function registerServiceWorker() {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('Service Worker registered successfully:', registration);
                    })
                    .catch(error => {
                        console.log('Service Worker registration failed:', error);
                    });
            }
        }

        // ===== Event Listeners =====
        document.addEventListener('DOMContentLoaded', () => {
            try {
                initializeApp();
                initializeKeyboardShortcuts();
                lazyLoadImages();
                // registerServiceWorker(); // Uncomment when you have a service worker
            } catch (error) {
                handleError(error, 'DOMContentLoaded');
            }
        });

        // Handle page visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // Pause autoplay when page is hidden
                if (heroSwiper && heroSwiper.autoplay) {
                    heroSwiper.autoplay.stop();
                }
            } else {
                // Resume autoplay when page is visible
                if (heroSwiper && heroSwiper.autoplay) {
                    heroSwiper.autoplay.start();
                }
            }
        });

        // Handle online/offline status
        window.addEventListener('online', () => {
            showNotification('تم استعادة الاتصال بالإنترنت', 'success');
        });

        window.addEventListener('offline', () => {
            showNotification('تم فقدان الاتصال بالإنترنت', 'warning');
        });

        // Handle resize for responsive adjustments
        window.addEventListener('resize', () => {
            if (heroSwiper) {
                heroSwiper.update();
            }
        });

        // Prevent right-click context menu (optional)
        // document.addEventListener('contextmenu', e => e.preventDefault());

        // ===== Global Error Handler =====
        window.addEventListener('error', (e) => {
            handleError(e.error, 'Global Error Handler');
        });

        window.addEventListener('unhandledrejection', (e) => {
            handleError(e.reason, 'Unhandled Promise Rejection');
        });

        // ===== Expose functions to global scope =====
        window.CinemaHub = {
            playMovie,
            openMovieDetails,
            handleSearch,
            scrollToSection,
            scrollToTop,
            changeFontSize,
            showNotification,
            clearSearchResults,
            showAllMovies,
            showAllSeries,
            showTopRated
        };

        console.log('CinemaHub script loaded successfully!');
    </script>
</body>
</html>
</body>
</html>
