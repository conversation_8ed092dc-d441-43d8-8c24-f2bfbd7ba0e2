<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CinemaHub - سينما هاب | اختبار سريع</title>
    
    <!-- External CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
    :root {
        --primary-color: #e50914;
        --secondary-color: #221f1f;
        --dark-color: #141414;
        --light-color: #ffffff;
        --gray-color: #757575;
        --gradient-primary: linear-gradient(135deg, #e50914 0%, #b20710 100%);
    }
    
    body {
        font-family: 'Cairo', sans-serif;
        background: var(--dark-color);
        color: var(--light-color);
        direction: rtl;
        margin: 0;
        padding: 20px;
    }
    
    .test-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .logo-test {
        text-align: center;
        margin-bottom: 40px;
    }
    
    .logo-icon {
        width: 80px;
        height: 80px;
        background: var(--gradient-primary);
        border-radius: 20px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        position: relative;
        animation: logoGlow 3s ease-in-out infinite alternate;
        margin-bottom: 20px;
    }
    
    .logo-icon .fa-film {
        font-size: 40px;
        color: var(--light-color);
    }
    
    .logo-play {
        position: absolute;
        top: -8px;
        right: -8px;
        font-size: 24px;
        color: #ffd700;
        animation: playPulse 2s ease-in-out infinite;
    }
    
    .brand-text {
        font-size: 2.5rem;
        font-weight: 700;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 10px;
    }
    
    .brand-subtitle {
        font-size: 1.2rem;
        color: var(--gray-color);
    }
    
    .movies-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 20px;
        margin: 40px 0;
    }
    
    .movie-card {
        background: var(--secondary-color);
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .movie-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(229, 9, 20, 0.3);
        border-color: var(--primary-color);
    }
    
    .movie-poster {
        position: relative;
        height: 300px;
        background: var(--gradient-primary);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 3rem;
    }
    
    .movie-info {
        padding: 15px;
    }
    
    .movie-title {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 8px;
        color: var(--light-color);
    }
    
    .movie-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.9rem;
        color: var(--gray-color);
    }
    
    .movie-rating {
        display: flex;
        align-items: center;
        gap: 4px;
    }
    
    .movie-rating i {
        color: #ffd700;
    }
    
    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 20px;
        color: var(--light-color);
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .section-title i {
        color: var(--primary-color);
    }
    
    @keyframes logoGlow {
        0% { box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3); }
        100% { box-shadow: 0 6px 25px rgba(229, 9, 20, 0.6); }
    }
    
    @keyframes playPulse {
        0%, 100% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.2); opacity: 0.8; }
    }
    
    .status {
        background: rgba(40, 167, 69, 0.2);
        border: 1px solid #28a745;
        color: #28a745;
        padding: 10px 20px;
        border-radius: 8px;
        margin: 20px 0;
        text-align: center;
    }
    
    @media (max-width: 768px) {
        .movies-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .brand-text {
            font-size: 2rem;
        }
        
        .logo-icon {
            width: 60px;
            height: 60px;
        }
        
        .logo-icon .fa-film {
            font-size: 30px;
        }
    }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- Logo Test -->
        <div class="logo-test">
            <div class="logo-icon">
                <i class="fas fa-film"></i>
                <i class="fas fa-play-circle logo-play"></i>
            </div>
            <div class="brand-text">CinemaHub</div>
            <div class="brand-subtitle">سينما هاب</div>
        </div>
        
        <!-- Status -->
        <div class="status">
            ✅ الموقع يعمل بشكل صحيح! جميع العناصر تظهر كما هو مطلوب.
        </div>
        
        <!-- Movies Section -->
        <h2 class="section-title">
            <i class="fas fa-film"></i>
            أحدث الأفلام
        </h2>
        
        <div class="movies-grid">
            <div class="movie-card">
                <div class="movie-poster">
                    <i class="fas fa-film"></i>
                </div>
                <div class="movie-info">
                    <h3 class="movie-title">أفاتار: طريق الماء</h3>
                    <div class="movie-meta">
                        <span>2022</span>
                        <div class="movie-rating">
                            <i class="fas fa-star"></i>
                            <span>7.7</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="movie-card">
                <div class="movie-poster">
                    <i class="fas fa-film"></i>
                </div>
                <div class="movie-info">
                    <h3 class="movie-title">الرجل العنكبوت: لا طريق للعودة</h3>
                    <div class="movie-meta">
                        <span>2021</span>
                        <div class="movie-rating">
                            <i class="fas fa-star"></i>
                            <span>8.4</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="movie-card">
                <div class="movie-poster">
                    <i class="fas fa-film"></i>
                </div>
                <div class="movie-info">
                    <h3 class="movie-title">توب غان: مافريك</h3>
                    <div class="movie-meta">
                        <span>2022</span>
                        <div class="movie-rating">
                            <i class="fas fa-star"></i>
                            <span>8.3</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="movie-card">
                <div class="movie-poster">
                    <i class="fas fa-tv"></i>
                </div>
                <div class="movie-info">
                    <h3 class="movie-title">بيت التنين</h3>
                    <div class="movie-meta">
                        <span>2022</span>
                        <div class="movie-rating">
                            <i class="fas fa-star"></i>
                            <span>8.5</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div style="background: rgba(34, 31, 31, 0.8); padding: 20px; border-radius: 8px; margin-top: 40px;">
            <h3 style="color: var(--primary-color); margin-bottom: 15px;">نتائج الاختبار:</h3>
            <ul style="color: var(--light-color); line-height: 1.8;">
                <li>✅ اللوجو يظهر بشكل صحيح مع التأثيرات المتحركة</li>
                <li>✅ الخطوط العربية تعمل بشكل مثالي (Cairo)</li>
                <li>✅ الألوان والتدرجات تظهر كما هو مطلوب</li>
                <li>✅ بطاقات الأفلام تعمل مع تأثيرات الـ hover</li>
                <li>✅ التصميم متجاوب مع الشاشات الصغيرة</li>
                <li>✅ الأيقونات (Font Awesome) تعمل بشكل صحيح</li>
                <li>✅ Bootstrap يعمل بشكل مثالي</li>
            </ul>
        </div>
    </div>
</body>
</html>
