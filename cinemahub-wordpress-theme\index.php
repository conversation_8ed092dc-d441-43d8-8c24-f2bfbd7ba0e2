<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php bloginfo('name'); ?> - <?php bloginfo('description'); ?></title>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: white;
            direction: rtl;
            min-height: 100vh;
        }

        .header {
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(20px);
            padding: 1rem 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            border-bottom: 2px solid #dc2626;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            text-decoration: none;
            color: white;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.4);
        }

        .logo-text h1 {
            font-size: 1.8rem;
            font-weight: 800;
            margin: 0;
        }

        .logo-text p {
            font-size: 0.9rem;
            color: #dc2626;
            margin: 0;
        }

        .nav-menu {
            display: flex;
            gap: 1rem;
            list-style: none;
        }

        .nav-menu a {
            color: white;
            text-decoration: none;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .nav-menu a:hover {
            background: rgba(220, 38, 38, 0.2);
            transform: translateY(-2px);
        }

        .main-content {
            margin-top: 100px;
            padding: 3rem 1rem;
            text-align: center;
            min-height: calc(100vh - 100px);
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .welcome-section {
            max-width: 1000px;
            margin: 0 auto;
        }

        .main-title {
            font-size: 4rem;
            font-weight: 900;
            margin-bottom: 2rem;
            background: linear-gradient(135deg, #dc2626 0%, #fbbf24 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 20px rgba(220, 38, 38, 0.3);
        }

        .subtitle {
            font-size: 1.5rem;
            color: #cbd5e1;
            margin-bottom: 3rem;
            line-height: 1.6;
        }

        .action-buttons {
            display: flex;
            gap: 2rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 4rem;
        }

        .btn {
            padding: 1.2rem 2.5rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 700;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .btn-primary {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
        }

        .btn-primary:hover {
            box-shadow: 0 15px 35px rgba(220, 38, 38, 0.4);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
            max-width: 1000px;
            margin-left: auto;
            margin-right: auto;
        }

        .feature-card {
            background: rgba(30, 41, 59, 0.8);
            padding: 2.5rem;
            border-radius: 20px;
            border: 2px solid rgba(148, 163, 184, 0.1);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .feature-card:hover {
            border-color: #dc2626;
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .feature-icon {
            font-size: 3rem;
            color: #dc2626;
            margin-bottom: 1.5rem;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: white;
        }

        .feature-desc {
            color: #cbd5e1;
            line-height: 1.6;
        }

        .footer {
            background: rgba(15, 23, 42, 0.95);
            padding: 2rem 0;
            text-align: center;
            border-top: 2px solid #dc2626;
            margin-top: 4rem;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .footer-text {
            color: #94a3b8;
            margin-bottom: 1rem;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .footer-links a {
            color: #cbd5e1;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: #dc2626;
        }

        @media (max-width: 768px) {
            .main-title {
                font-size: 2.5rem;
            }

            .subtitle {
                font-size: 1.2rem;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .nav-menu {
                display: none;
            }
        }
    </style>

    <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>

<!-- Header -->
<header class="header">
    <div class="header-content">
        <a href="<?php echo home_url(); ?>" class="logo">
            <div class="logo-icon">
                <i class="fas fa-film"></i>
            </div>
            <div class="logo-text">
                <h1><?php bloginfo('name'); ?></h1>
                <p><?php bloginfo('description'); ?></p>
            </div>
        </a>

        <nav>
            <ul class="nav-menu">
                <li><a href="<?php echo home_url(); ?>"><i class="fas fa-home"></i> الرئيسية</a></li>
                <li><a href="<?php echo get_post_type_archive_link('movie'); ?>"><i class="fas fa-film"></i> الأفلام</a></li>
                <li><a href="<?php echo get_post_type_archive_link('series'); ?>"><i class="fas fa-tv"></i> المسلسلات</a></li>
                <li><a href="<?php echo admin_url(); ?>"><i class="fas fa-cog"></i> لوحة التحكم</a></li>
            </ul>
        </nav>
    </div>
</header>

<!-- Main Content -->
<main class="main-content">
    <div class="welcome-section">
        <h1 class="main-title">مرحباً بك في CinemaHub Pro</h1>

        <p class="subtitle">
            منصة احترافية لإدارة وعرض الأفلام والمسلسلات العربية والعالمية<br>
            بتصميم عصري ومتجاوب مع جميع الأجهزة
        </p>

        <div class="action-buttons">
            <a href="<?php echo admin_url('post-new.php?post_type=movie'); ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة فيلم جديد
            </a>

            <a href="<?php echo admin_url('post-new.php?post_type=series'); ?>" class="btn btn-secondary">
                <i class="fas fa-plus"></i>
                إضافة مسلسل جديد
            </a>
        </div>

        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-film"></i>
                </div>
                <h3 class="feature-title">إدارة الأفلام</h3>
                <p class="feature-desc">
                    أضف وأدر مجموعة أفلامك بسهولة مع تفاصيل شاملة وصور عالية الجودة
                </p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-tv"></i>
                </div>
                <h3 class="feature-title">إدارة المسلسلات</h3>
                <p class="feature-desc">
                    نظم مسلسلاتك مع تفاصيل المواسم والحلقات ومتابعة حالة العرض
                </p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h3 class="feature-title">بحث متقدم</h3>
                <p class="feature-desc">
                    ابحث في المحتوى بسهولة وسرعة مع فلاتر متقدمة وتصنيفات ذكية
                </p>
            </div>
        </div>
    </div>
</main>

<!-- Footer -->
<footer class="footer">
    <div class="footer-content">
        <p class="footer-text">
            © 2024 CinemaHub Pro - قالب احترافي لمواقع الأفلام والمسلسلات
        </p>

        <div class="footer-links">
            <a href="<?php echo home_url(); ?>">الرئيسية</a>
            <a href="<?php echo get_post_type_archive_link('movie'); ?>">الأفلام</a>
            <a href="<?php echo get_post_type_archive_link('series'); ?>">المسلسلات</a>
            <a href="<?php echo admin_url(); ?>">لوحة التحكم</a>
        </div>
    </div>
</footer>

<?php wp_footer(); ?>
</body>
</html>