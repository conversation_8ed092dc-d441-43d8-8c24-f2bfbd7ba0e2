/*
 * CinemaHub Pro Desktop Application Styles
 * Version: 1.0.0
 * Author: CinemaHub Team
 */

/* ===== CSS Variables ===== */
:root {
    /* ===== Professional Color Palette ===== */
    --primary-color: #dc2626;
    --primary-dark: #b91c1c;
    --primary-light: #ef4444;
    --primary-hover: #f87171;
    --primary-50: #fef2f2;
    --primary-100: #fee2e2;
    --primary-500: #ef4444;
    --primary-600: #dc2626;
    --primary-700: #b91c1c;
    --primary-800: #991b1b;
    --primary-900: #7f1d1d;

    --secondary-color: #1e293b;
    --secondary-dark: #0f172a;
    --secondary-light: #334155;
    --accent-color: #f59e0b;
    --accent-light: #fbbf24;
    --accent-dark: #d97706;

    --dark-color: #0f172a;
    --darker-color: #020617;
    --light-color: #ffffff;
    --surface-color: #1e293b;
    --surface-light: #334155;

    /* ===== Gray Scale ===== */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* ===== Semantic Colors ===== */
    --success-color: #059669;
    --success-light: #10b981;
    --success-dark: #047857;
    --warning-color: #d97706;
    --warning-light: #f59e0b;
    --warning-dark: #b45309;
    --danger-color: #dc2626;
    --danger-light: #ef4444;
    --danger-dark: #b91c1c;
    --info-color: #0ea5e9;
    --info-light: #38bdf8;
    --info-dark: #0284c7;

    /* ===== Professional Gradients ===== */
    --gradient-primary: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
    --gradient-primary-light: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
    --gradient-dark: linear-gradient(135deg, var(--dark-color) 0%, var(--darker-color) 100%);
    --gradient-surface: linear-gradient(135deg, var(--surface-color) 0%, var(--surface-light) 100%);
    --gradient-overlay: linear-gradient(180deg, rgba(15,23,42,0) 0%, rgba(15,23,42,0.95) 100%);
    --gradient-hero: linear-gradient(45deg, rgba(220,38,38,0.15) 0%, rgba(15,23,42,0.85) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);

    --font-family: 'Cairo', sans-serif;
    --font-size-xs: 0.65rem;
    --font-size-sm: 0.75rem;
    --font-size-base: 0.85rem;
    --font-size-lg: 0.95rem;
    --font-size-xl: 1.05rem;
    --font-size-2xl: 1.25rem;
    --font-size-3xl: 1.5rem;
    --font-size-4xl: 1.75rem;
    --font-size-5xl: 2.25rem;

    --border-radius-sm: 0.375rem;
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-2xl: 1.5rem;
    --border-radius-3xl: 2rem;

    /* ===== Professional Shadows ===== */
    --shadow-sm: 0 1px 2px 0 rgba(15, 23, 42, 0.05);
    --shadow: 0 1px 3px 0 rgba(15, 23, 42, 0.1), 0 1px 2px 0 rgba(15, 23, 42, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(15, 23, 42, 0.1), 0 2px 4px -1px rgba(15, 23, 42, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(15, 23, 42, 0.15), 0 4px 6px -2px rgba(15, 23, 42, 0.08);
    --shadow-xl: 0 20px 25px -5px rgba(15, 23, 42, 0.2), 0 10px 10px -5px rgba(15, 23, 42, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(15, 23, 42, 0.35);
    --shadow-glow: 0 0 20px rgba(220, 38, 38, 0.25);
    --shadow-glow-accent: 0 0 20px rgba(245, 158, 11, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(15, 23, 42, 0.06);

    --transition-fast: all 0.15s ease;
    --transition: all 0.3s ease;
    --transition-slow: all 0.5s ease;

    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal: 1050;
    --z-tooltip: 1070;
    --z-toast: 1080;

    /* ===== Desktop App Specific ===== */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --header-height: 70px;
}

/* ===== Base Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
    height: 100%;
}

body {
    font-family: var(--font-family);
    background: var(--dark-color);
    color: var(--light-color);
    line-height: 1.6;
    direction: rtl;
    overflow: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    height: 100vh;
    user-select: none;
    -webkit-user-select: none;
}

/* ===== Typography ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 0.5rem;
}

h1 { font-size: var(--font-size-4xl); font-weight: 700; }
h2 { font-size: var(--font-size-3xl); font-weight: 600; }
h3 { font-size: var(--font-size-2xl); font-weight: 600; }
h4 { font-size: var(--font-size-xl); font-weight: 500; }
h5 { font-size: var(--font-size-lg); font-weight: 500; }
h6 { font-size: var(--font-size-base); font-weight: 500; }

p {
    margin-bottom: 1rem;
    color: var(--gray-300);
    font-weight: 400;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-light);
}

/* ===== Loading Screen ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-dark);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    animation: fadeInUp 0.8s ease;
}

.loading-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.loading-logo i {
    font-size: 3rem;
    color: var(--primary-color);
    animation: pulse 2s infinite;
}

.loading-logo h2 {
    color: var(--light-color);
    font-size: var(--font-size-3xl);
    font-weight: 800;
    margin: 0;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(220, 38, 38, 0.3);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1.5rem;
}

.loading-content p {
    color: var(--gray-400);
    font-size: var(--font-size-lg);
    margin: 0;
    font-weight: 500;
}

/* ===== Animations ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(220, 38, 38, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(220, 38, 38, 0.8), 0 0 30px rgba(220, 38, 38, 0.6);
    }
}

/* ===== App Container ===== */
.app-container {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

/* ===== Sidebar ===== */
.sidebar {
    width: var(--sidebar-width);
    background: var(--gradient-secondary);
    border-left: 1px solid rgba(148, 163, 184, 0.1);
    display: flex;
    flex-direction: column;
    transition: var(--transition);
    z-index: var(--z-sticky);
    box-shadow: var(--shadow-lg);
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(148, 163, 184, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(220, 38, 38, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--light-color);
    font-weight: 800;
    font-size: var(--font-size-xl);
    transition: var(--transition);
}

.logo i {
    font-size: 1.8rem;
    color: var(--primary-color);
    animation: pulse 3s infinite;
}

.sidebar.collapsed .logo span {
    display: none;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--gray-400);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.sidebar-toggle:hover {
    color: var(--primary-color);
    background: rgba(220, 38, 38, 0.1);
}

/* ===== Sidebar Navigation ===== */
.sidebar-nav {
    flex: 1;
    padding: 1rem 0;
    overflow-y: auto;
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1.5rem;
    color: var(--gray-300);
    text-decoration: none;
    transition: var(--transition);
    font-weight: 500;
    font-size: var(--font-size-sm);
    border-radius: 0;
    position: relative;
}

.nav-link::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: var(--primary-color);
    transform: scaleY(0);
    transition: var(--transition);
}

.nav-link:hover {
    color: var(--light-color);
    background: rgba(220, 38, 38, 0.1);
    transform: translateX(-5px);
}

.nav-item.active .nav-link {
    color: var(--light-color);
    background: var(--gradient-primary);
    box-shadow: var(--shadow-md);
}

.nav-item.active .nav-link::before {
    transform: scaleY(1);
}

.nav-link i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
    color: var(--primary-color);
}

.sidebar.collapsed .nav-link span {
    display: none;
}

.sidebar.collapsed .nav-link {
    justify-content: center;
    padding: 0.875rem 0.5rem;
}

/* ===== Sidebar Footer ===== */
.sidebar-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(148, 163, 184, 0.1);
    background: rgba(0, 0, 0, 0.2);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--light-color);
    font-size: 1.2rem;
    box-shadow: var(--shadow-md);
}

.user-details {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.user-name {
    color: var(--light-color);
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.user-role {
    color: var(--gray-400);
    font-size: var(--font-size-xs);
}

.sidebar.collapsed .user-details {
    display: none;
}

/* ===== Main Content ===== */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: var(--dark-color);
}

/* ===== Main Header ===== */
.main-header {
    height: var(--header-height);
    background: var(--surface-color);
    border-bottom: 1px solid rgba(148, 163, 184, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    box-shadow: var(--shadow-sm);
    z-index: var(--z-sticky);
}

.header-left {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.page-title {
    color: var(--light-color);
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin: 0;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--gray-400);
    font-size: var(--font-size-xs);
}

.breadcrumb i {
    font-size: 0.7rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.header-actions {
    display: flex;
    gap: 0.75rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-sm);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.online {
    background: var(--success-color);
}

.status-dot.offline {
    background: var(--danger-color);
}

.status-dot.syncing {
    background: var(--warning-color);
}

/* ===== Content Area ===== */
.content-area {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
    background: var(--darker-color);
}

.page-content {
    display: none;
    animation: fadeInUp 0.5s ease;
}

.page-content.active {
    display: block;
}

.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.page-header h2 {
    color: var(--light-color);
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* ===== Cards ===== */
.content-card {
    background: var(--surface-color);
    border-radius: var(--border-radius-2xl);
    border: 1px solid rgba(148, 163, 184, 0.1);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    margin-bottom: 2rem;
    transition: var(--transition);
}

.content-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.card {
    background: var(--surface-color);
    border-radius: var(--border-radius-2xl);
    border: 1px solid rgba(148, 163, 184, 0.1);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(148, 163, 184, 0.1);
    background: rgba(255, 255, 255, 0.02);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.card-header h5 {
    color: var(--light-color);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-header i {
    color: var(--primary-color);
}

.card-body {
    padding: 1.5rem;
}

/* ===== Stat Cards ===== */
.stat-card {
    background: var(--gradient-surface);
    border-radius: var(--border-radius-2xl);
    padding: 1.5rem;
    border: 1px solid rgba(148, 163, 184, 0.1);
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(220, 38, 38, 0.1), transparent);
    transition: left 0.6s ease;
}

.stat-card:hover::before {
    left: 100%;
}

.stat-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: var(--shadow-2xl), var(--shadow-glow);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--light-color);
    box-shadow: var(--shadow-md);
}

.stat-icon.movies {
    background: var(--gradient-primary);
}

.stat-icon.series {
    background: var(--gradient-accent);
}

.stat-icon.categories {
    background: linear-gradient(135deg, var(--info-color) 0%, var(--info-dark) 100%);
}

.stat-icon.sync {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%);
}

.stat-info h3 {
    color: var(--light-color);
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin: 0 0 0.25rem 0;
}

.stat-info p {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
    margin: 0;
    font-weight: 500;
}

/* ===== Activity List ===== */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.02);
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(148, 163, 184, 0.1);
    transition: var(--transition);
}

.activity-item:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateX(-3px);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-content p {
    color: var(--light-color);
    font-size: var(--font-size-sm);
    font-weight: 500;
    margin: 0 0 0.25rem 0;
}

.activity-content small {
    color: var(--gray-400);
    font-size: var(--font-size-xs);
}

/* ===== Quick Stats ===== */
.quick-stats {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.quick-stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.02);
    border-radius: var(--border-radius);
    border: 1px solid rgba(148, 163, 184, 0.1);
}

.quick-stat-item .label {
    color: var(--gray-300);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.quick-stat-item .value {
    color: var(--primary-color);
    font-size: var(--font-size-sm);
    font-weight: 700;
}

/* ===== Quick Actions ===== */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.btn-block {
    width: 100%;
    justify-content: center;
}

/* ===== Search Bar ===== */
.search-bar {
    display: flex;
    gap: 0.5rem;
    flex: 1;
    max-width: 400px;
}

.search-bar input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid rgba(148, 163, 184, 0.3);
    border-radius: var(--border-radius-lg);
    background: rgba(255, 255, 255, 0.05);
    color: var(--light-color);
    font-size: var(--font-size-sm);
    transition: var(--transition);
}

.search-bar input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.search-bar input::placeholder {
    color: var(--gray-400);
}

.search-bar button {
    padding: 0.75rem 1rem;
    border: 1px solid rgba(148, 163, 184, 0.3);
    border-radius: var(--border-radius-lg);
    background: rgba(255, 255, 255, 0.05);
    color: var(--gray-400);
    cursor: pointer;
    transition: var(--transition);
}

.search-bar button:hover {
    background: var(--gradient-primary);
    color: var(--light-color);
    border-color: var(--primary-color);
}

/* ===== Filter Options ===== */
.filter-options {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.filter-options select {
    padding: 0.75rem 1rem;
    border: 1px solid rgba(148, 163, 184, 0.3);
    border-radius: var(--border-radius-lg);
    background: rgba(255, 255, 255, 0.05);
    color: var(--light-color);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: var(--transition);
}

.filter-options select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
}

.filter-options select option {
    background: var(--surface-color);
    color: var(--light-color);
}

/* ===== Movies/Series Grid ===== */
.movies-grid,
.series-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.movie-card,
.series-card {
    background: var(--surface-color);
    border-radius: var(--border-radius-2xl);
    overflow: hidden;
    transition: var(--transition);
    cursor: pointer;
    border: 1px solid rgba(148, 163, 184, 0.1);
    box-shadow: var(--shadow-lg);
    position: relative;
}

.movie-card:hover,
.series-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-2xl), var(--shadow-glow);
}

.movie-poster,
.series-poster {
    position: relative;
    height: 280px;
    overflow: hidden;
}

.movie-poster img,
.series-poster img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.movie-card:hover .movie-poster img,
.series-card:hover .series-poster img {
    transform: scale(1.1);
}

.movie-overlay,
.series-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-overlay);
    opacity: 0;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 1rem;
}

.movie-card:hover .movie-overlay,
.series-card:hover .series-overlay {
    opacity: 1;
}

.play-btn {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--light-color);
    font-size: 1.2rem;
    transition: var(--transition);
    box-shadow: var(--shadow-lg);
    border: none;
    cursor: pointer;
}

.play-btn:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-glow);
}

.overlay-actions {
    display: flex;
    gap: 0.5rem;
}

.overlay-btn {
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: var(--border-radius);
    color: var(--light-color);
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.overlay-btn:hover {
    background: var(--gradient-primary);
    transform: scale(1.1);
}

.movie-info,
.series-info {
    padding: 1rem;
}

.movie-title,
.series-title {
    color: var(--light-color);
    font-size: var(--font-size-sm);
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.movie-meta,
.series-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: var(--font-size-xs);
    color: var(--gray-400);
    background: rgba(148, 163, 184, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
}

.meta-item i {
    color: var(--primary-color);
}

.movie-rating,
.series-rating {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: var(--accent-color);
    font-weight: 600;
    font-size: var(--font-size-xs);
}

/* ===== Editor Container ===== */
.editor-container {
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    background: var(--darker-color);
}

.editor-toolbar {
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.02);
    border-bottom: 1px solid rgba(148, 163, 184, 0.1);
    display: flex;
    gap: 0.5rem;
}

.code-editor {
    width: 100%;
    height: 400px;
    padding: 1rem;
    border: none;
    background: var(--darker-color);
    color: var(--light-color);
    font-family: 'Courier New', monospace;
    font-size: var(--font-size-sm);
    line-height: 1.5;
    resize: vertical;
    outline: none;
}

.code-editor::placeholder {
    color: var(--gray-500);
}

/* ===== File Tree ===== */
.file-tree {
    max-height: 300px;
    overflow-y: auto;
}

.file-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    cursor: pointer;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-size: var(--font-size-sm);
}

.file-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.file-item.active {
    background: var(--gradient-primary);
    color: var(--light-color);
}

.file-item i {
    color: var(--primary-color);
    width: 16px;
    text-align: center;
}

.file-item.active i {
    color: var(--light-color);
}

/* ===== File Info ===== */
.file-info {
    font-size: var(--font-size-sm);
    color: var(--gray-300);
}

.file-info p {
    margin-bottom: 0.5rem;
}

.file-info strong {
    color: var(--light-color);
}

/* ===== Sync Status ===== */
.sync-status {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.02);
    border-radius: var(--border-radius);
    border: 1px solid rgba(148, 163, 184, 0.1);
}

.status-item .label {
    color: var(--gray-300);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.status-item .value {
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.status-item .value.ready {
    color: var(--success-color);
}

.status-item .value.syncing {
    color: var(--warning-color);
}

.status-item .value.error {
    color: var(--danger-color);
}

/* ===== Progress Bar ===== */
.progress {
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
    border-radius: var(--border-radius);
}

/* ===== Forms ===== */
.form-label {
    color: var(--light-color);
    font-size: var(--font-size-sm);
    font-weight: 500;
    margin-bottom: 0.5rem;
    display: block;
}

.form-control,
.form-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid rgba(148, 163, 184, 0.3);
    border-radius: var(--border-radius-lg);
    background: rgba(255, 255, 255, 0.05);
    color: var(--light-color);
    font-size: var(--font-size-sm);
    transition: var(--transition);
}

.form-control:focus,
.form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.form-control::placeholder {
    color: var(--gray-400);
}

.form-select option {
    background: var(--surface-color);
    color: var(--light-color);
}

textarea.form-control {
    resize: vertical;
    min-height: 100px;
}

.form-check {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-check-input {
    width: 18px;
    height: 18px;
    border: 2px solid rgba(148, 163, 184, 0.3);
    border-radius: var(--border-radius-sm);
    background: rgba(255, 255, 255, 0.05);
    cursor: pointer;
    transition: var(--transition);
}

.form-check-input:checked {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
}

.form-check-label {
    color: var(--gray-300);
    font-size: var(--font-size-sm);
    cursor: pointer;
}

/* ===== Buttons ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
    user-select: none;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--light-color);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
    background: var(--gradient-primary-light);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), var(--shadow-glow);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%);
    color: var(--light-color);
    box-shadow: var(--shadow-md);
}

.btn-success:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--success-light) 0%, var(--success-color) 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), 0 0 20px rgba(5, 150, 105, 0.25);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, var(--info-dark) 100%);
    color: var(--light-color);
    box-shadow: var(--shadow-md);
}

.btn-info:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--info-light) 0%, var(--info-color) 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), 0 0 20px rgba(14, 165, 233, 0.25);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-dark) 100%);
    color: var(--light-color);
    box-shadow: var(--shadow-md);
}

.btn-warning:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--warning-light) 0%, var(--warning-color) 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), 0 0 20px rgba(217, 119, 6, 0.25);
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline-primary:hover:not(:disabled) {
    background: var(--gradient-primary);
    color: var(--light-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline-secondary {
    background: transparent;
    color: var(--gray-400);
    border: 2px solid rgba(148, 163, 184, 0.3);
}

.btn-outline-secondary:hover:not(:disabled) {
    background: rgba(148, 163, 184, 0.1);
    color: var(--light-color);
    border-color: var(--gray-400);
    transform: translateY(-2px);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: var(--font-size-xs);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: var(--font-size-lg);
}

/* ===== App Info ===== */
.app-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.app-info p {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--gray-300);
}

.app-info strong {
    color: var(--light-color);
}

/* ===== Toast Notifications ===== */
.toast {
    background: var(--surface-color);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    color: var(--light-color);
}

.toast-header {
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(148, 163, 184, 0.1);
    color: var(--light-color);
}

.toast-body {
    color: var(--gray-300);
}

/* ===== Scrollbar Styling ===== */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.3);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(148, 163, 184, 0.5);
}

::-webkit-scrollbar-corner {
    background: transparent;
}

/* ===== Responsive Design ===== */
@media (max-width: 1200px) {
    .sidebar {
        width: 250px;
    }

    .content-area {
        padding: 1.5rem;
    }

    .movies-grid,
    .series-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 1rem;
    }
}

@media (max-width: 992px) {
    .sidebar {
        position: fixed;
        left: -280px;
        z-index: var(--z-modal);
        height: 100vh;
        transition: left 0.3s ease;
    }

    .sidebar.open {
        left: 0;
    }

    .main-content {
        margin-right: 0;
    }

    .header-right {
        gap: 1rem;
    }

    .header-actions {
        gap: 0.5rem;
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .movies-grid,
    .series-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }
}

@media (max-width: 768px) {
    .main-header {
        padding: 0 1rem;
    }

    .content-area {
        padding: 1rem;
    }

    .page-title {
        font-size: var(--font-size-xl);
    }

    .breadcrumb {
        display: none;
    }

    .header-actions {
        flex-direction: column;
        width: 100%;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .search-bar {
        max-width: none;
        width: 100%;
    }

    .filter-options {
        width: 100%;
        justify-content: stretch;
    }

    .filter-options select {
        flex: 1;
    }

    .movies-grid,
    .series-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .movie-poster,
    .series-poster {
        height: 200px;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .activity-item {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .status-indicator {
        display: none;
    }
}

@media (max-width: 576px) {
    .sidebar {
        width: 100%;
        left: -100%;
    }

    .main-header {
        padding: 0 0.75rem;
    }

    .content-area {
        padding: 0.75rem;
    }

    .page-title {
        font-size: var(--font-size-lg);
    }

    .movies-grid,
    .series-grid {
        grid-template-columns: 1fr;
    }

    .movie-poster,
    .series-poster {
        height: 300px;
    }

    .card-header,
    .card-body {
        padding: 1rem;
    }

    .btn {
        padding: 0.875rem 1rem;
        font-size: var(--font-size-sm);
    }

    .form-control,
    .form-select {
        padding: 0.875rem 1rem;
    }
}

/* ===== Dark Mode Enhancements ===== */
.dark-mode-toggle {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border: none;
    border-radius: 50%;
    color: var(--light-color);
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
    z-index: var(--z-tooltip);
    box-shadow: var(--shadow-lg);
}

.dark-mode-toggle:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-glow);
}

/* ===== Custom Animations ===== */
@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.animate-slide-in-right {
    animation: slideInFromRight 0.5s ease;
}

.animate-slide-in-left {
    animation: slideInFromLeft 0.5s ease;
}

.animate-bounce-in {
    animation: bounceIn 0.6s ease;
}

/* ===== Utility Classes ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-info { color: var(--info-color); }
.text-light { color: var(--light-color); }
.text-muted { color: var(--gray-400); }