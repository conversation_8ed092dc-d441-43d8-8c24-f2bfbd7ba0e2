// ===== PWA Manager =====

class PWAManager {
    constructor() {
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.notificationPermission = 'default';
        
        this.init();
    }

    init() {
        this.checkInstallation();
        this.setupInstallPrompt();
        this.setupNotifications();
        this.handleAppInstalled();
        this.setupUpdateCheck();
    }

    // ===== Installation Management =====
    checkInstallation() {
        // Check if app is installed
        if (window.matchMedia('(display-mode: standalone)').matches || 
            window.navigator.standalone === true) {
            this.isInstalled = true;
            this.hideInstallButton();
        }

        // Check if running as PWA
        if (window.matchMedia('(display-mode: standalone)').matches) {
            document.body.classList.add('pwa-mode');
        }
    }

    setupInstallPrompt() {
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('PWA: Install prompt available');
            e.preventDefault();
            this.deferredPrompt = e;
            this.showInstallButton();
        });
    }

    showInstallButton() {
        const installButton = this.createInstallButton();
        document.body.appendChild(installButton);
        
        // Auto-show install prompt after 30 seconds
        setTimeout(() => {
            if (!this.isInstalled && this.deferredPrompt) {
                this.showInstallPrompt();
            }
        }, 30000);
    }

    createInstallButton() {
        const button = document.createElement('button');
        button.id = 'pwa-install-btn';
        button.className = 'pwa-install-button';
        button.innerHTML = `
            <i class="fas fa-download"></i>
            <span>تثبيت التطبيق</span>
        `;
        
        button.addEventListener('click', () => {
            this.showInstallPrompt();
        });

        // Add styles
        const styles = `
            .pwa-install-button {
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: var(--gradient-primary);
                color: white;
                border: none;
                border-radius: 25px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: 600;
                cursor: pointer;
                box-shadow: 0 4px 20px rgba(229, 9, 20, 0.3);
                transition: all 0.3s ease;
                z-index: 1000;
                display: flex;
                align-items: center;
                gap: 8px;
            }
            
            .pwa-install-button:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 25px rgba(229, 9, 20, 0.4);
            }
            
            .pwa-install-button i {
                font-size: 16px;
            }
            
            @media (max-width: 768px) {
                .pwa-install-button {
                    bottom: 80px;
                    right: 15px;
                    padding: 10px 16px;
                    font-size: 13px;
                }
            }
        `;
        
        if (!document.getElementById('pwa-styles')) {
            const styleSheet = document.createElement('style');
            styleSheet.id = 'pwa-styles';
            styleSheet.textContent = styles;
            document.head.appendChild(styleSheet);
        }

        return button;
    }

    async showInstallPrompt() {
        if (!this.deferredPrompt) {
            console.log('PWA: No install prompt available');
            return;
        }

        try {
            const result = await this.deferredPrompt.prompt();
            console.log('PWA: Install prompt result:', result.outcome);
            
            if (result.outcome === 'accepted') {
                console.log('PWA: User accepted install prompt');
                this.hideInstallButton();
            }
            
            this.deferredPrompt = null;
        } catch (error) {
            console.error('PWA: Install prompt error:', error);
        }
    }

    hideInstallButton() {
        const button = document.getElementById('pwa-install-btn');
        if (button) {
            button.remove();
        }
    }

    handleAppInstalled() {
        window.addEventListener('appinstalled', (e) => {
            console.log('PWA: App installed successfully');
            this.isInstalled = true;
            this.hideInstallButton();
            this.showNotification('تم تثبيت التطبيق بنجاح!', 'success');
            
            // Track installation
            this.trackEvent('pwa_installed');
        });
    }

    // ===== Notifications Management =====
    async setupNotifications() {
        if ('Notification' in window) {
            this.notificationPermission = Notification.permission;
            
            if (this.notificationPermission === 'default') {
                // Show notification permission request after user interaction
                setTimeout(() => {
                    this.showNotificationPermissionPrompt();
                }, 60000); // After 1 minute
            }
        }
    }

    showNotificationPermissionPrompt() {
        const prompt = document.createElement('div');
        prompt.className = 'notification-permission-prompt';
        prompt.innerHTML = `
            <div class="prompt-content">
                <i class="fas fa-bell"></i>
                <h4>تفعيل الإشعارات</h4>
                <p>احصل على إشعارات عند إضافة أفلام ومسلسلات جديدة</p>
                <div class="prompt-actions">
                    <button class="btn btn-primary" onclick="pwaManager.requestNotificationPermission()">
                        تفعيل الإشعارات
                    </button>
                    <button class="btn btn-secondary" onclick="pwaManager.dismissNotificationPrompt()">
                        لاحقاً
                    </button>
                </div>
            </div>
        `;

        // Add styles for notification prompt
        const styles = `
            .notification-permission-prompt {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                backdrop-filter: blur(5px);
            }
            
            .prompt-content {
                background: var(--secondary-color);
                padding: 30px;
                border-radius: var(--border-radius);
                text-align: center;
                max-width: 400px;
                margin: 20px;
                box-shadow: var(--box-shadow);
            }
            
            .prompt-content i {
                font-size: 3rem;
                color: var(--primary-color);
                margin-bottom: 20px;
            }
            
            .prompt-content h4 {
                color: var(--light-color);
                margin-bottom: 15px;
            }
            
            .prompt-content p {
                color: var(--gray-color);
                margin-bottom: 25px;
                line-height: 1.6;
            }
            
            .prompt-actions {
                display: flex;
                gap: 15px;
                justify-content: center;
            }
            
            .prompt-actions .btn {
                padding: 10px 20px;
                border-radius: 25px;
                font-weight: 600;
                border: none;
                cursor: pointer;
                transition: var(--transition);
            }
            
            .prompt-actions .btn-primary {
                background: var(--gradient-primary);
                color: white;
            }
            
            .prompt-actions .btn-secondary {
                background: rgba(255, 255, 255, 0.1);
                color: var(--light-color);
            }
        `;

        if (!document.getElementById('notification-prompt-styles')) {
            const styleSheet = document.createElement('style');
            styleSheet.id = 'notification-prompt-styles';
            styleSheet.textContent = styles;
            document.head.appendChild(styleSheet);
        }

        document.body.appendChild(prompt);
    }

    async requestNotificationPermission() {
        try {
            const permission = await Notification.requestPermission();
            this.notificationPermission = permission;
            
            if (permission === 'granted') {
                console.log('PWA: Notification permission granted');
                this.showNotification('تم تفعيل الإشعارات بنجاح!', 'success');
                this.trackEvent('notifications_enabled');
            } else {
                console.log('PWA: Notification permission denied');
            }
            
            this.dismissNotificationPrompt();
        } catch (error) {
            console.error('PWA: Notification permission error:', error);
            this.dismissNotificationPrompt();
        }
    }

    dismissNotificationPrompt() {
        const prompt = document.querySelector('.notification-permission-prompt');
        if (prompt) {
            prompt.remove();
        }
    }

    showNotification(title, options = {}) {
        if (this.notificationPermission !== 'granted') {
            return;
        }

        const defaultOptions = {
            icon: '/assets/images/icon-192x192.png',
            badge: '/assets/images/badge-72x72.png',
            vibrate: [100, 50, 100],
            tag: 'movie-notification',
            renotify: true,
            requireInteraction: false,
            silent: false
        };

        const notificationOptions = { ...defaultOptions, ...options };

        if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
            // Send notification through service worker
            navigator.serviceWorker.controller.postMessage({
                type: 'SHOW_NOTIFICATION',
                title: title,
                options: notificationOptions
            });
        } else {
            // Show notification directly
            new Notification(title, notificationOptions);
        }
    }

    // ===== Update Management =====
    setupUpdateCheck() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('controllerchange', () => {
                console.log('PWA: New service worker activated');
                this.showUpdateNotification();
            });

            // Check for updates periodically
            setInterval(() => {
                this.checkForUpdates();
            }, 60000); // Check every minute
        }
    }

    async checkForUpdates() {
        if ('serviceWorker' in navigator) {
            const registration = await navigator.serviceWorker.getRegistration();
            if (registration) {
                registration.update();
            }
        }
    }

    showUpdateNotification() {
        const notification = document.createElement('div');
        notification.className = 'update-notification';
        notification.innerHTML = `
            <div class="update-content">
                <i class="fas fa-sync-alt"></i>
                <span>تحديث جديد متوفر</span>
                <button onclick="pwaManager.applyUpdate()">تحديث الآن</button>
                <button onclick="this.parentElement.parentElement.remove()">لاحقاً</button>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 10000);
    }

    applyUpdate() {
        window.location.reload();
    }

    // ===== Analytics =====
    trackEvent(eventName, eventData = {}) {
        if (CONFIG.ANALYTICS.CUSTOM_ANALYTICS.ENABLED) {
            // Send to custom analytics endpoint
            fetch(CONFIG.ANALYTICS.CUSTOM_ANALYTICS.ENDPOINT, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    event: eventName,
                    data: eventData,
                    timestamp: new Date().toISOString(),
                    userAgent: navigator.userAgent,
                    url: window.location.href
                })
            }).catch(error => {
                console.error('PWA: Analytics error:', error);
            });
        }

        // Google Analytics
        if (CONFIG.ANALYTICS.GOOGLE_ANALYTICS.ENABLED && typeof gtag !== 'undefined') {
            gtag('event', eventName, eventData);
        }
    }

    // ===== Utility Methods =====
    isOnline() {
        return navigator.onLine;
    }

    getInstallationStatus() {
        return {
            isInstalled: this.isInstalled,
            canInstall: !!this.deferredPrompt,
            notificationPermission: this.notificationPermission
        };
    }
}

// Initialize PWA Manager
let pwaManager;
document.addEventListener('DOMContentLoaded', () => {
    if (CONFIG.PERFORMANCE.SERVICE_WORKER) {
        pwaManager = new PWAManager();
        window.pwaManager = pwaManager;
    }
});

// Handle online/offline status
window.addEventListener('online', () => {
    console.log('PWA: Back online');
    document.body.classList.remove('offline');
    if (pwaManager) {
        pwaManager.showNotification('عاد الاتصال بالإنترنت', {
            body: 'يمكنك الآن تصفح المحتوى الجديد'
        });
    }
});

window.addEventListener('offline', () => {
    console.log('PWA: Gone offline');
    document.body.classList.add('offline');
    if (pwaManager) {
        pwaManager.showNotification('انقطع الاتصال بالإنترنت', {
            body: 'يمكنك الاستمرار في تصفح المحتوى المحفوظ'
        });
    }
});
