<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?php bloginfo('description'); ?>">
    
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<!-- Modern Header -->
<header class="modern-header" id="header">
    <div class="header-container">
        <!-- Logo -->
        <a href="<?php echo esc_url(home_url('/')); ?>" class="header-logo">
            <div class="logo-icon">
                <i class="fas fa-film"></i>
            </div>
            <div class="logo-text">
                <h1><?php bloginfo('name'); ?></h1>
                <p><?php bloginfo('description'); ?></p>
            </div>
        </a>

        <!-- Navigation -->
        <nav class="header-nav">
            <?php
            // Movies dropdown
            $movie_genres = get_terms(array(
                'taxonomy' => 'movie_genre',
                'hide_empty' => false,
                'number' => 6
            ));
            ?>
            <div class="nav-item dropdown">
                <a href="<?php echo get_post_type_archive_link('movie'); ?>" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-film"></i>
                    الأفلام
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="<?php echo get_post_type_archive_link('movie'); ?>"><i class="fas fa-fire"></i> جميع الأفلام</a></li>
                    <?php if (!empty($movie_genres)) : ?>
                        <?php foreach ($movie_genres as $genre) : ?>
                            <li><a class="dropdown-item" href="<?php echo get_term_link($genre); ?>"><i class="fas fa-tag"></i> <?php echo esc_html($genre->name); ?></a></li>
                        <?php endforeach; ?>
                    <?php else : ?>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-star"></i> الأعلى تقييماً</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-heart"></i> الأكثر مشاهدة</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-skull-crossbones"></i> أفلام الرعب</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-fist-raised"></i> أفلام الأكشن</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-laugh"></i> أفلام الكوميديا</a></li>
                    <?php endif; ?>
                </ul>
            </div>

            <?php
            // Series dropdown
            $series_genres = get_terms(array(
                'taxonomy' => 'series_genre',
                'hide_empty' => false,
                'number' => 6
            ));
            ?>
            <div class="nav-item dropdown">
                <a href="<?php echo get_post_type_archive_link('series'); ?>" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-tv"></i>
                    المسلسلات
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="<?php echo get_post_type_archive_link('series'); ?>"><i class="fas fa-fire"></i> جميع المسلسلات</a></li>
                    <?php if (!empty($series_genres)) : ?>
                        <?php foreach ($series_genres as $genre) : ?>
                            <li><a class="dropdown-item" href="<?php echo get_term_link($genre); ?>"><i class="fas fa-tag"></i> <?php echo esc_html($genre->name); ?></a></li>
                        <?php endforeach; ?>
                    <?php else : ?>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-star"></i> الأعلى تقييماً</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-flag"></i> مسلسلات تركية</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-yin-yang"></i> مسلسلات كورية</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-globe-americas"></i> مسلسلات أجنبية</a></li>
                    <?php endif; ?>
                </ul>
            </div>

            <a href="<?php echo esc_url(home_url('/top-rated')); ?>" class="nav-link">
                <i class="fas fa-crown"></i>
                الأعلى تقييماً
            </a>

            <!-- Search Form -->
            <div class="nav-item">
                <form class="search-form" method="get" action="<?php echo esc_url(home_url('/')); ?>" style="display: flex; align-items: center; gap: 0.5rem;">
                    <input type="search" name="s" placeholder="البحث..." value="<?php echo get_search_query(); ?>" style="padding: 0.5rem; border: 1px solid rgba(148, 163, 184, 0.3); border-radius: 8px; background: rgba(255, 255, 255, 0.1); color: white; font-size: 0.85rem; width: 200px;">
                    <button type="submit" style="padding: 0.5rem 0.75rem; background: var(--gradient-primary); border: none; border-radius: 8px; color: white; cursor: pointer;">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
        </nav>

        <!-- Mobile Menu Toggle -->
        <button class="navbar-toggler d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#mobileNav" style="background: rgba(255, 255, 255, 0.1); border: none; padding: 0.75rem; border-radius: 8px; color: white;">
            <i class="fas fa-bars"></i>
        </button>
    </div>

    <!-- Mobile Navigation -->
    <div class="collapse navbar-collapse d-lg-none" id="mobileNav" style="background: rgba(15, 23, 42, 0.98); margin-top: 1rem; padding: 1rem;">
        <div class="container">
            <ul class="navbar-nav" style="list-style: none; padding: 0;">
                <li class="nav-item" style="margin-bottom: 0.5rem;">
                    <a class="nav-link" href="<?php echo get_post_type_archive_link('movie'); ?>" style="color: white; padding: 0.75rem; display: block; border-radius: 8px; transition: all 0.3s ease;">
                        <i class="fas fa-film"></i> الأفلام
                    </a>
                </li>
                <li class="nav-item" style="margin-bottom: 0.5rem;">
                    <a class="nav-link" href="<?php echo get_post_type_archive_link('series'); ?>" style="color: white; padding: 0.75rem; display: block; border-radius: 8px; transition: all 0.3s ease;">
                        <i class="fas fa-tv"></i> المسلسلات
                    </a>
                </li>
                <li class="nav-item" style="margin-bottom: 0.5rem;">
                    <a class="nav-link" href="<?php echo esc_url(home_url('/top-rated')); ?>" style="color: white; padding: 0.75rem; display: block; border-radius: 8px; transition: all 0.3s ease;">
                        <i class="fas fa-crown"></i> الأعلى تقييماً
                    </a>
                </li>
                <li class="nav-item">
                    <form class="search-form" method="get" action="<?php echo esc_url(home_url('/')); ?>" style="padding: 0.75rem;">
                        <div style="display: flex; gap: 0.5rem;">
                            <input type="search" name="s" placeholder="البحث..." value="<?php echo get_search_query(); ?>" style="flex: 1; padding: 0.75rem; border: 1px solid rgba(148, 163, 184, 0.3); border-radius: 8px; background: rgba(255, 255, 255, 0.1); color: white;">
                            <button type="submit" style="padding: 0.75rem 1rem; background: var(--gradient-primary); border: none; border-radius: 8px; color: white; cursor: pointer;">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </li>
            </ul>
        </div>
    </div>
</header>

<style>
/* Mobile Navigation Styles */
@media (max-width: 991px) {
    .header-nav {
        display: none;
    }
    
    .navbar-toggler:focus {
        box-shadow: none;
    }
    
    #mobileNav .nav-link:hover {
        background: rgba(220, 38, 38, 0.15);
        transform: translateX(-5px);
    }
}

/* Search Form Styles */
.search-form input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
}

.search-form button:hover {
    background: var(--gradient-primary-light);
    transform: scale(1.05);
}

/* Header Scroll Effect */
.modern-header.scrolled .logo-text h1 {
    font-size: 1.3rem;
}

.modern-header.scrolled .logo-icon {
    width: 40px;
    height: 40px;
    font-size: 1.1rem;
}

/* Navigation Hover Effects */
.nav-link:hover {
    background: rgba(220, 38, 38, 0.15) !important;
    transform: translateY(-2px);
}

.dropdown-menu {
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Logo Animation */
.header-logo:hover .logo-icon {
    animation: logoGlow 0.6s ease;
}

@keyframes logoGlow {
    0%, 100% {
        box-shadow: 0 4px 15px rgba(220, 38, 38, 0.4);
    }
    50% {
        box-shadow: 0 6px 25px rgba(220, 38, 38, 0.6), 0 0 30px rgba(245, 158, 11, 0.3);
    }
}
</style>
