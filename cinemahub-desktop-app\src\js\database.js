/**
 * CinemaHub Pro Desktop Application
 * Database Management
 * 
 * @version 1.0.0
 * <AUTHOR> Team
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs-extra');
const { app } = require('electron');

class DatabaseManager {
    constructor() {
        this.db = null;
        this.dbPath = null;
        this.isInitialized = false;
    }

    /**
     * Initialize database
     */
    async init() {
        try {
            // Get user data path
            const userDataPath = app.getPath('userData');
            const dbDir = path.join(userDataPath, 'database');
            
            // Ensure directory exists
            await fs.ensureDir(dbDir);
            
            this.dbPath = path.join(dbDir, 'cinemahub.db');
            
            // Open database connection
            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    console.error('Error opening database:', err);
                    throw err;
                }
                console.log('Connected to SQLite database');
            });

            // Create tables
            await this.createTables();
            
            this.isInitialized = true;
            console.log('Database initialized successfully');
        } catch (error) {
            console.error('Failed to initialize database:', error);
            throw error;
        }
    }

    /**
     * Create database tables
     */
    async createTables() {
        const tables = [
            // Movies table
            `CREATE TABLE IF NOT EXISTS movies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT,
                poster TEXT,
                year INTEGER,
                duration INTEGER,
                rating REAL,
                genre TEXT,
                country TEXT,
                director TEXT,
                cast TEXT,
                trailer_url TEXT,
                watch_url TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Series table
            `CREATE TABLE IF NOT EXISTS series (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT,
                poster TEXT,
                year INTEGER,
                seasons INTEGER,
                episodes INTEGER,
                rating REAL,
                genre TEXT,
                country TEXT,
                status TEXT DEFAULT 'ongoing',
                trailer_url TEXT,
                watch_url TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Categories table
            `CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                slug TEXT NOT NULL UNIQUE,
                description TEXT,
                icon TEXT,
                color TEXT,
                type TEXT DEFAULT 'movie',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Movie categories junction table
            `CREATE TABLE IF NOT EXISTS movie_categories (
                movie_id INTEGER,
                category_id INTEGER,
                PRIMARY KEY (movie_id, category_id),
                FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
                FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
            )`,

            // Series categories junction table
            `CREATE TABLE IF NOT EXISTS series_categories (
                series_id INTEGER,
                category_id INTEGER,
                PRIMARY KEY (series_id, category_id),
                FOREIGN KEY (series_id) REFERENCES series(id) ON DELETE CASCADE,
                FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
            )`,

            // Settings table
            `CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT,
                type TEXT DEFAULT 'string',
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Activity log table
            `CREATE TABLE IF NOT EXISTS activity_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                action TEXT NOT NULL,
                entity_type TEXT,
                entity_id INTEGER,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Sync log table
            `CREATE TABLE IF NOT EXISTS sync_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                status TEXT NOT NULL,
                message TEXT,
                files_synced INTEGER DEFAULT 0,
                errors TEXT,
                started_at DATETIME,
                completed_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];

        for (const tableSQL of tables) {
            await this.run(tableSQL);
        }

        // Insert default categories
        await this.insertDefaultCategories();
        
        // Insert default settings
        await this.insertDefaultSettings();
    }

    /**
     * Insert default categories
     */
    async insertDefaultCategories() {
        const defaultCategories = [
            { name: 'أفلام الأكشن', slug: 'action', icon: 'fas fa-fist-raised', color: '#dc2626', type: 'movie' },
            { name: 'أفلام الكوميديا', slug: 'comedy', icon: 'fas fa-laugh', color: '#f59e0b', type: 'movie' },
            { name: 'أفلام الرعب', slug: 'horror', icon: 'fas fa-skull-crossbones', color: '#7c3aed', type: 'movie' },
            { name: 'أفلام الدراما', slug: 'drama', icon: 'fas fa-theater-masks', color: '#059669', type: 'movie' },
            { name: 'أفلام رومانسية', slug: 'romance', icon: 'fas fa-heart', color: '#ec4899', type: 'movie' },
            { name: 'أفلام خيال علمي', slug: 'sci-fi', icon: 'fas fa-rocket', color: '#0ea5e9', type: 'movie' },
            { name: 'مسلسلات تركية', slug: 'turkish', icon: 'fas fa-flag', color: '#dc2626', type: 'series' },
            { name: 'مسلسلات كورية', slug: 'korean', icon: 'fas fa-yin-yang', color: '#f59e0b', type: 'series' },
            { name: 'مسلسلات أمريكية', slug: 'american', icon: 'fas fa-flag-usa', color: '#0ea5e9', type: 'series' },
            { name: 'مسلسلات عربية', slug: 'arabic', icon: 'fas fa-mosque', color: '#059669', type: 'series' }
        ];

        for (const category of defaultCategories) {
            try {
                await this.run(
                    `INSERT OR IGNORE INTO categories (name, slug, icon, color, type) VALUES (?, ?, ?, ?, ?)`,
                    [category.name, category.slug, category.icon, category.color, category.type]
                );
            } catch (error) {
                console.error('Error inserting category:', category.name, error);
            }
        }
    }

    /**
     * Insert default settings
     */
    async insertDefaultSettings() {
        const defaultSettings = [
            { key: 'site_name', value: 'CinemaHub Pro', type: 'string' },
            { key: 'site_description', value: 'أفضل موقع لمشاهدة الأفلام والمسلسلات', type: 'string' },
            { key: 'site_language', value: 'ar', type: 'string' },
            { key: 'auto_sync', value: 'true', type: 'boolean' },
            { key: 'sync_interval', value: '30', type: 'number' },
            { key: 'server_url', value: '', type: 'string' },
            { key: 'server_username', value: '', type: 'string' },
            { key: 'server_password', value: '', type: 'string' },
            { key: 'last_sync', value: '', type: 'string' },
            { key: 'theme', value: 'dark', type: 'string' }
        ];

        for (const setting of defaultSettings) {
            try {
                await this.run(
                    `INSERT OR IGNORE INTO settings (key, value, type) VALUES (?, ?, ?)`,
                    [setting.key, setting.value, setting.type]
                );
            } catch (error) {
                console.error('Error inserting setting:', setting.key, error);
            }
        }
    }

    /**
     * Run SQL query
     */
    run(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID, changes: this.changes });
                }
            });
        });
    }

    /**
     * Get single row
     */
    get(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    /**
     * Get all rows
     */
    all(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    /**
     * Get movies
     */
    async getMovies(filters = {}) {
        let sql = `
            SELECT m.*, GROUP_CONCAT(c.name) as categories
            FROM movies m
            LEFT JOIN movie_categories mc ON m.id = mc.movie_id
            LEFT JOIN categories c ON mc.category_id = c.id
        `;
        
        const params = [];
        const conditions = [];

        if (filters.search) {
            conditions.push('m.title LIKE ?');
            params.push(`%${filters.search}%`);
        }

        if (filters.genre) {
            conditions.push('c.slug = ?');
            params.push(filters.genre);
        }

        if (filters.year) {
            conditions.push('m.year = ?');
            params.push(filters.year);
        }

        if (conditions.length > 0) {
            sql += ' WHERE ' + conditions.join(' AND ');
        }

        sql += ' GROUP BY m.id ORDER BY m.created_at DESC';

        if (filters.limit) {
            sql += ' LIMIT ?';
            params.push(filters.limit);
        }

        return await this.all(sql, params);
    }

    /**
     * Get series
     */
    async getSeries(filters = {}) {
        let sql = `
            SELECT s.*, GROUP_CONCAT(c.name) as categories
            FROM series s
            LEFT JOIN series_categories sc ON s.id = sc.series_id
            LEFT JOIN categories c ON sc.category_id = c.id
        `;
        
        const params = [];
        const conditions = [];

        if (filters.search) {
            conditions.push('s.title LIKE ?');
            params.push(`%${filters.search}%`);
        }

        if (filters.genre) {
            conditions.push('c.slug = ?');
            params.push(filters.genre);
        }

        if (filters.status) {
            conditions.push('s.status = ?');
            params.push(filters.status);
        }

        if (conditions.length > 0) {
            sql += ' WHERE ' + conditions.join(' AND ');
        }

        sql += ' GROUP BY s.id ORDER BY s.created_at DESC';

        if (filters.limit) {
            sql += ' LIMIT ?';
            params.push(filters.limit);
        }

        return await this.all(sql, params);
    }

    /**
     * Add movie
     */
    async addMovie(movieData) {
        const sql = `
            INSERT INTO movies (title, description, poster, year, duration, rating, genre, country, director, cast, trailer_url, watch_url)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        
        const params = [
            movieData.title,
            movieData.description || '',
            movieData.poster || '',
            movieData.year || null,
            movieData.duration || null,
            movieData.rating || null,
            movieData.genre || '',
            movieData.country || '',
            movieData.director || '',
            movieData.cast || '',
            movieData.trailer_url || '',
            movieData.watch_url || ''
        ];

        const result = await this.run(sql, params);
        
        // Add to activity log
        await this.logActivity('create', 'movie', result.id, `تم إضافة فيلم جديد: ${movieData.title}`);
        
        return result;
    }

    /**
     * Add series
     */
    async addSeries(seriesData) {
        const sql = `
            INSERT INTO series (title, description, poster, year, seasons, episodes, rating, genre, country, status, trailer_url, watch_url)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        
        const params = [
            seriesData.title,
            seriesData.description || '',
            seriesData.poster || '',
            seriesData.year || null,
            seriesData.seasons || null,
            seriesData.episodes || null,
            seriesData.rating || null,
            seriesData.genre || '',
            seriesData.country || '',
            seriesData.status || 'ongoing',
            seriesData.trailer_url || '',
            seriesData.watch_url || ''
        ];

        const result = await this.run(sql, params);
        
        // Add to activity log
        await this.logActivity('create', 'series', result.id, `تم إضافة مسلسل جديد: ${seriesData.title}`);
        
        return result;
    }

    /**
     * Get categories
     */
    async getCategories(type = null) {
        let sql = 'SELECT * FROM categories';
        const params = [];

        if (type) {
            sql += ' WHERE type = ?';
            params.push(type);
        }

        sql += ' ORDER BY name';

        return await this.all(sql, params);
    }

    /**
     * Get statistics
     */
    async getStats() {
        const [moviesCount] = await this.all('SELECT COUNT(*) as count FROM movies');
        const [seriesCount] = await this.all('SELECT COUNT(*) as count FROM series');
        const [categoriesCount] = await this.all('SELECT COUNT(*) as count FROM categories');
        
        const lastSync = await this.getSetting('last_sync');

        return {
            movies: moviesCount.count,
            series: seriesCount.count,
            categories: categoriesCount.count,
            lastSync: lastSync || 'لم يتم بعد'
        };
    }

    /**
     * Get setting
     */
    async getSetting(key) {
        const row = await this.get('SELECT value FROM settings WHERE key = ?', [key]);
        return row ? row.value : null;
    }

    /**
     * Set setting
     */
    async setSetting(key, value, type = 'string') {
        await this.run(
            'INSERT OR REPLACE INTO settings (key, value, type, updated_at) VALUES (?, ?, ?, CURRENT_TIMESTAMP)',
            [key, value, type]
        );
    }

    /**
     * Log activity
     */
    async logActivity(action, entityType, entityId, description) {
        await this.run(
            'INSERT INTO activity_log (action, entity_type, entity_id, description) VALUES (?, ?, ?, ?)',
            [action, entityType, entityId, description]
        );
    }

    /**
     * Get recent activity
     */
    async getRecentActivity(limit = 10) {
        return await this.all(
            'SELECT * FROM activity_log ORDER BY created_at DESC LIMIT ?',
            [limit]
        );
    }

    /**
     * Close database connection
     */
    close() {
        if (this.db) {
            this.db.close((err) => {
                if (err) {
                    console.error('Error closing database:', err);
                } else {
                    console.log('Database connection closed');
                }
            });
        }
    }

    /**
     * Backup database
     */
    async backup(backupPath) {
        try {
            await fs.copy(this.dbPath, backupPath);
            console.log('Database backup created:', backupPath);
            return true;
        } catch (error) {
            console.error('Failed to backup database:', error);
            return false;
        }
    }

    /**
     * Restore database
     */
    async restore(backupPath) {
        try {
            // Close current connection
            this.close();
            
            // Copy backup to current location
            await fs.copy(backupPath, this.dbPath);
            
            // Reinitialize
            await this.init();
            
            console.log('Database restored from:', backupPath);
            return true;
        } catch (error) {
            console.error('Failed to restore database:', error);
            return false;
        }
    }
}

// Export singleton instance
const dbManager = new DatabaseManager();
module.exports = dbManager;
