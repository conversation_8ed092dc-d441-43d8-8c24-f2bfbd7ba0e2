# دليل التثبيت والإعداد - موقع الأفلام

## 📋 المتطلبات الأساسية

### 1. متطلبات النظام
- متصفح ويب حديث (Chrome 80+, Firefox 75+, Safari 13+, Edge 80+)
- خ<PERSON>م ويب (Apache, Nginx, أو أي خادم HTTP)
- اتصال بالإنترنت لجلب البيانات من TMDB API

### 2. الحسابات المطلوبة
- حساب مجاني في [The Movie Database (TMDB)](https://www.themoviedb.org/)
- (اختياري) حساب Google Analytics للإحصائيات
- (اختياري) حساب Facebook للبكسل

## 🚀 خطوات التثبيت

### الخطوة 1: تحميل الملفات
```bash
# استنساخ المشروع
git clone https://github.com/yourusername/movie-website.git
cd movie-website

# أو تحميل الملفات مباشرة
# وفك الضغط في مجلد المشروع
```

### الخطوة 2: الحصول على مفتاح TMDB API

1. **إنشاء حساب TMDB:**
   - اذهب إلى [https://www.themoviedb.org/](https://www.themoviedb.org/)
   - انقر على "Join TMDB"
   - املأ البيانات المطلوبة وأكد البريد الإلكتروني

2. **طلب مفتاح API:**
   - سجل دخول إلى حسابك
   - اذهب إلى Settings > API
   - انقر على "Create" تحت "Request an API Key"
   - اختر "Developer" واملأ النموذج
   - انسخ مفتاح API الذي ستحصل عليه

### الخطوة 3: إعداد المشروع

1. **تحرير ملف الإعدادات:**
   ```javascript
   // افتح ملف config.js
   const CONFIG = {
       API: {
           TMDB_API_KEY: 'ضع_مفتاح_API_هنا', // استبدل هذا بمفتاحك الفعلي
           // باقي الإعدادات...
       }
   };
   ```

2. **تخصيص معلومات الموقع:**
   ```javascript
   SITE: {
       NAME: 'اسم موقعك',
       DESCRIPTION: 'وصف موقعك',
       URL: 'https://yoursite.com',
       // باقي الإعدادات...
   }
   ```

### الخطوة 4: رفع الملفات

#### للاستضافة العادية:
1. ارفع جميع الملفات إلى مجلد الجذر للموقع
2. تأكد من الحفاظ على هيكل المجلدات
3. تأكد من أن ملف `index.html` في المجلد الرئيسي

#### لمنصة بلوجر:
1. **رفع الملفات الثابتة:**
   - ارفع ملفات CSS و JS إلى خدمة استضافة ملفات (مثل GitHub Pages)
   - أو استخدم CDN مثل jsDelivr

2. **تحرير قالب بلوجر:**
   - اذهب إلى لوحة تحكم بلوجر
   - Theme > Edit HTML
   - انسخ محتوى `index.html` واستبدل القالب الحالي
   - عدل روابط الملفات لتشير إلى مواقعها الجديدة

### الخطوة 5: اختبار الموقع

1. **افتح الموقع في المتصفح**
2. **تحقق من:**
   - تحميل الصفحة الرئيسية بشكل صحيح
   - ظهور الأفلام والمسلسلات
   - عمل البحث
   - عمل النوافذ المنبثقة للأفلام

## ⚙️ الإعدادات المتقدمة

### تفعيل HTTPS
```apache
# في ملف .htaccess
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

### تحسين الأداء
```apache
# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تخزين مؤقت للملفات
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
</IfModule>
```

### إعداد Content Security Policy
```html
<meta http-equiv="Content-Security-Policy" content="
    default-src 'self';
    script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com;
    style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net;
    img-src 'self' data: https://image.tmdb.org;
    font-src 'self' https://fonts.gstatic.com;
    connect-src 'self' https://api.themoviedb.org;
">
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. لا تظهر الأفلام
**السبب المحتمل:** مفتاح API غير صحيح أو منتهي الصلاحية
**الحل:**
- تحقق من صحة مفتاح API في `config.js`
- تأكد من عدم وجود مسافات إضافية
- تحقق من حالة مفتاح API في لوحة تحكم TMDB

#### 2. خطأ CORS
**السبب المحتمل:** تشغيل الموقع محلياً بدون خادم
**الحل:**
```bash
# استخدم خادم محلي
npx http-server . -p 3000 --cors
# أو
python -m http.server 3000
```

#### 3. الصور لا تظهر
**السبب المحتمل:** مشكلة في روابط الصور أو حجب المحتوى
**الحل:**
- تحقق من إعدادات `IMAGE_BASE_URL` في `config.js`
- تأكد من وجود صورة افتراضية في `assets/images/no-poster.jpg`

#### 4. البحث لا يعمل
**السبب المحتمل:** خطأ في JavaScript أو مفتاح API
**الحل:**
- افتح Developer Tools وتحقق من الأخطاء
- تأكد من تحميل جميع ملفات JavaScript
- تحقق من إعدادات البحث في `config.js`

### تفعيل وضع التطوير
```javascript
// في ملف config.js
DEVELOPMENT: {
    DEBUG_MODE: true,
    CONSOLE_LOGS: true,
    SHOW_ERROR_DETAILS: true
}
```

## 📊 مراقبة الأداء

### استخدام Lighthouse
```bash
# تثبيت Lighthouse
npm install -g lighthouse

# تشغيل تقرير الأداء
lighthouse http://yoursite.com --output html --output-path ./report.html
```

### مراقبة الأخطاء
- افتح Developer Tools > Console
- راقب Network tab للطلبات الفاشلة
- استخدم Performance tab لتحليل الأداء

## 🔄 التحديثات

### تحديث المشروع
1. احتفظ بنسخة احتياطية من `config.js`
2. حمل الإصدار الجديد
3. انسخ إعداداتك المخصصة
4. اختبر الموقع قبل النشر

### تحديث مفتاح API
1. اذهب إلى لوحة تحكم TMDB
2. أنشئ مفتاح API جديد إذا لزم الأمر
3. حدث `config.js` بالمفتاح الجديد

## 🛡️ الأمان

### حماية مفتاح API
- لا تشارك مفتاح API علناً
- استخدم متغيرات البيئة في الإنتاج
- راقب استخدام API بانتظام

### تحديثات الأمان
- حدث المتصفحات بانتظام
- راقب تحديثات المكتبات المستخدمة
- فعل HTTPS دائماً

## 📞 الحصول على المساعدة

### الموارد المفيدة
- [وثائق TMDB API](https://developers.themoviedb.org/3)
- [دليل PWA](https://web.dev/progressive-web-apps/)
- [Bootstrap Documentation](https://getbootstrap.com/docs/)

### الدعم الفني
- افتح issue في GitHub
- راجع الأسئلة الشائعة في README.md
- انضم لمجتمع المطورين

---

**ملاحظة مهمة:** تأكد من احترام شروط استخدام TMDB API وعدم تجاوز حدود الاستخدام المسموحة.
