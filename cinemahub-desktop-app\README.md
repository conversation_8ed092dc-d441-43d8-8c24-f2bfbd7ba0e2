# CinemaHub Pro Desktop Application

برنامج سطح مكتب احترافي لإدارة مواقع الأفلام والمسلسلات مع مزامنة تلقائية مع الكود.

## 🌟 المميزات الرئيسية

### 🎯 **إدارة شاملة**
- **لوحة تحكم متقدمة** - إحصائيات وتحليلات شاملة
- **إدارة الأفلام** - إضافة وتحرير وحذف الأفلام
- **إدارة المسلسلات** - إدارة كاملة للمسلسلات والحلقات
- **إدارة التصنيفات** - تنظيم المحتوى بتصنيفات مخصصة
- **قاعدة بيانات محلية** - SQLite لتخزين البيانات

### 🔄 **المزامنة التلقائية**
- **مراقبة الملفات** - مراقبة تلقائية لتغييرات الكود
- **مزامنة فورية** - تحديث الموقع عند أي تغيير
- **نسخ احتياطية** - حفظ تلقائي للبيانات
- **تتبع التغييرات** - سجل كامل للتعديلات

### 🎨 **واجهة احترافية**
- **تصميم حديث** - واجهة مستخدم عصرية ومتجاوبة
- **شريط جانبي قابل للطي** - تنقل سهل ومرن
- **إشعارات ذكية** - تنبيهات فورية للأحداث
- **اختصارات لوحة المفاتيح** - تحكم سريع

### 🛠️ **أدوات متقدمة**
- **محرر كود مدمج** - تحرير ملفات الموقع مباشرة
- **معاينة مباشرة** - عرض الموقع قبل النشر
- **تصدير البيانات** - نسخ احتياطية شاملة
- **إعدادات مرنة** - تخصيص كامل للبرنامج

## 📋 متطلبات التشغيل

### الحد الأدنى
- **نظام التشغيل:** Windows 10, macOS 10.14, Ubuntu 18.04
- **الذاكرة:** 4 GB RAM
- **التخزين:** 500 MB مساحة فارغة
- **المعالج:** Intel i3 أو AMD Ryzen 3

### المستحسن
- **نظام التشغيل:** Windows 11, macOS 12+, Ubuntu 20.04+
- **الذاكرة:** 8 GB RAM أو أكثر
- **التخزين:** 2 GB مساحة فارغة
- **المعالج:** Intel i5 أو AMD Ryzen 5
- **الشبكة:** اتصال إنترنت مستقر

## 🚀 التثبيت والتشغيل

### التثبيت من المصدر

#### 1. تحميل المتطلبات
```bash
# تأكد من وجود Node.js (الإصدار 16 أو أحدث)
node --version

# تأكد من وجود npm
npm --version
```

#### 2. تحميل المشروع
```bash
# انتقل إلى مجلد البرنامج
cd cinemahub-desktop-app

# تثبيت المتطلبات
npm install
```

#### 3. تشغيل البرنامج
```bash
# تشغيل في وضع التطوير
npm run dev

# أو تشغيل عادي
npm start
```

### بناء البرنامج للتوزيع

#### بناء لجميع المنصات
```bash
npm run build
```

#### بناء لمنصة محددة
```bash
# Windows
npm run build-win

# macOS
npm run build-mac

# Linux
npm run build-linux
```

## 🎯 دليل الاستخدام

### الإعداد الأولي

#### 1. تشغيل البرنامج لأول مرة
- افتح البرنامج
- انتظر انتهاء التحميل
- ستظهر لوحة التحكم الرئيسية

#### 2. ربط مجلد الموقع
1. اذهب إلى **الإعدادات**
2. اختر **إعدادات المزامنة**
3. حدد مجلد الموقع (المجلد الذي يحتوي على ملفات HTML/WordPress)
4. اضغط **حفظ**

#### 3. إعداد الخادم (اختياري)
1. في **إعدادات المزامنة**
2. أدخل عنوان الخادم
3. أدخل بيانات الدخول
4. اختبر الاتصال

### إدارة الأفلام

#### إضافة فيلم جديد
1. اذهب إلى **إدارة الأفلام**
2. اضغط **إضافة فيلم جديد**
3. املأ البيانات المطلوبة:
   - العنوان (مطلوب)
   - الوصف
   - سنة الإنتاج
   - المدة
   - التقييم
   - التصنيف
   - البلد
   - المخرج
   - طاقم التمثيل
   - روابط الإعلان والمشاهدة
4. ارفع صورة البوستر
5. اضغط **حفظ**

#### تحرير فيلم موجود
1. في قائمة الأفلام، اضغط على الفيلم
2. اضغط أيقونة **التحرير**
3. عدل البيانات المطلوبة
4. اضغط **حفظ التغييرات**

#### حذف فيلم
1. اضغط على الفيلم المراد حذفه
2. اضغط أيقونة **الحذف**
3. أكد الحذف في النافذة المنبثقة

### إدارة المسلسلات

#### إضافة مسلسل جديد
1. اذهب إلى **إدارة المسلسلات**
2. اضغط **إضافة مسلسل جديد**
3. املأ البيانات:
   - العنوان (مطلوب)
   - الوصف
   - سنة الإنتاج
   - عدد المواسم
   - عدد الحلقات
   - الحالة (مستمر/مكتمل/ملغي)
   - التصنيف
   - البلد
4. ارفع صورة البوستر
5. اضغط **حفظ**

### المزامنة

#### تفعيل المزامنة التلقائية
1. اذهب إلى **المزامنة**
2. فعل **المزامنة التلقائية**
3. حدد فترة المزامنة
4. اضغط **حفظ الإعدادات**

#### مزامنة يدوية
1. اذهب إلى **المزامنة**
2. اضغط **بدء المزامنة**
3. انتظر انتهاء العملية

### إدارة الموقع

#### تحرير ملفات الموقع
1. اذهب إلى **إدارة الموقع**
2. اختر الملف من شجرة الملفات
3. عدل الكود في المحرر
4. اضغط **حفظ** (Ctrl+S)

#### معاينة الموقع
1. اضغط **معاينة الموقع** في الهيدر
2. سيفتح الموقع في المتصفح الافتراضي

## ⚙️ الإعدادات المتقدمة

### إعدادات عامة
- **اسم الموقع:** اسم موقعك
- **وصف الموقع:** وصف مختصر
- **لغة الموقع:** العربية/الإنجليزية
- **المزامنة التلقائية:** تفعيل/إلغاء

### إعدادات المزامنة
- **مجلد الموقع:** مسار ملفات الموقع
- **عنوان الخادم:** رابط الخادم
- **بيانات الدخول:** اسم المستخدم وكلمة المرور
- **فترة المزامنة:** كل كم دقيقة

### إعدادات قاعدة البيانات
- **مسار قاعدة البيانات:** موقع ملف البيانات
- **نسخ احتياطية تلقائية:** تفعيل/إلغاء
- **عدد النسخ المحفوظة:** كم نسخة احتياطية

## 🔧 استكشاف الأخطاء

### مشاكل شائعة

#### البرنامج لا يبدأ
**الحل:**
1. تأكد من تثبيت Node.js
2. تأكد من تثبيت المتطلبات: `npm install`
3. جرب إعادة تشغيل الكمبيوتر

#### المزامنة لا تعمل
**الحل:**
1. تحقق من مسار مجلد الموقع
2. تأكد من صلاحيات الكتابة
3. تحقق من اتصال الإنترنت
4. تأكد من صحة بيانات الخادم

#### قاعدة البيانات تالفة
**الحل:**
1. اذهب إلى مجلد البيانات
2. احذف ملف `cinemahub.db`
3. أعد تشغيل البرنامج
4. استورد نسخة احتياطية إن وجدت

#### الواجهة لا تظهر بشكل صحيح
**الحل:**
1. اضغط Ctrl+Shift+R لإعادة تحميل
2. تحقق من دقة الشاشة
3. جرب تغيير حجم النافذة
4. أعد تشغيل البرنامج

## 📁 هيكل المشروع

```
cinemahub-desktop-app/
├── main.js                 # الملف الرئيسي
├── package.json           # إعدادات المشروع
├── src/                   # ملفات المصدر
│   ├── index.html        # الواجهة الرئيسية
│   ├── css/
│   │   └── style.css     # أنماط التصميم
│   └── js/
│       ├── app.js        # منطق التطبيق
│       ├── database.js   # إدارة قاعدة البيانات
│       ├── sync.js       # إدارة المزامنة
│       └── modals.js     # النوافذ المنبثقة
├── assets/               # الأصول
│   ├── icon.png         # أيقونة البرنامج
│   ├── icon.ico         # أيقونة Windows
│   └── icon.icns        # أيقونة macOS
└── dist/                # ملفات البناء
```

## 🔄 التحديثات

### التحقق من التحديثات
1. اذهب إلى **الإعدادات**
2. اضغط **التحقق من التحديثات**
3. اتبع التعليمات إذا توفر تحديث

### تحديث يدوي
1. حمل الإصدار الجديد
2. أغلق البرنامج الحالي
3. ثبت الإصدار الجديد
4. شغل البرنامج

## 🛡️ الأمان والخصوصية

### حماية البيانات
- جميع البيانات محفوظة محلياً
- لا يتم إرسال بيانات شخصية
- كلمات المرور مشفرة
- النسخ الاحتياطية آمنة

### الصلاحيات المطلوبة
- قراءة وكتابة الملفات
- الوصول للشبكة (للمزامنة)
- إنشاء مجلدات
- تشغيل خدمة محلية

## 📞 الدعم والمساعدة

### الحصول على المساعدة
- **الدليل:** راجع هذا الملف
- **الأسئلة الشائعة:** في قسم استكشاف الأخطاء
- **الدعم الفني:** تواصل مع فريق التطوير

### الإبلاغ عن مشاكل
1. اجمع معلومات المشكلة
2. أرفق لقطات شاشة
3. اذكر خطوات إعادة المشكلة
4. أرسل تقرير مفصل

## 📄 الترخيص

هذا البرنامج مرخص تحت رخصة MIT. يمكنك استخدامه وتعديله بحرية.

## 🙏 شكر وتقدير

تم تطوير هذا البرنامج بواسطة فريق CinemaHub Pro لخدمة مجتمع المطورين العرب.

---

**CinemaHub Pro Desktop v1.0.0**  
© 2024 CinemaHub Team. جميع الحقوق محفوظة.
