<?php
/**
 * Debug template for troubleshooting
 *
 * @package CinemaHub Pro
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CinemaHub Pro - Debug</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0f172a;
            color: white;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .debug-section {
            background: #1e293b;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            border: 2px solid #334155;
        }
        .debug-title {
            color: #dc2626;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .status-ok {
            color: #10b981;
        }
        .status-error {
            color: #ef4444;
        }
        .status-warning {
            color: #f59e0b;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        .info-item {
            background: #334155;
            padding: 1rem;
            border-radius: 8px;
        }
        .info-label {
            font-weight: 600;
            color: #94a3b8;
            margin-bottom: 0.5rem;
        }
        .info-value {
            color: white;
            font-family: monospace;
        }
        .test-result {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            margin: 0.5rem 0;
            font-weight: 600;
        }
        .test-pass {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid #10b981;
        }
        .test-fail {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid #ef4444;
        }
        .test-warning {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
            border: 1px solid #f59e0b;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #dc2626; margin-bottom: 3rem;">
            <i class="fas fa-bug"></i>
            CinemaHub Pro - تشخيص المشاكل
        </h1>

        <!-- WordPress Status -->
        <div class="debug-section">
            <h2 class="debug-title">
                <i class="fas fa-wordpress"></i>
                حالة WordPress
            </h2>
            
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">إصدار WordPress:</div>
                    <div class="info-value"><?php echo get_bloginfo('version'); ?></div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">إصدار PHP:</div>
                    <div class="info-value"><?php echo PHP_VERSION; ?></div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">القالب النشط:</div>
                    <div class="info-value"><?php echo get_template(); ?></div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">URL الموقع:</div>
                    <div class="info-value"><?php echo home_url(); ?></div>
                </div>
            </div>
        </div>

        <!-- Theme Files Check -->
        <div class="debug-section">
            <h2 class="debug-title">
                <i class="fas fa-file-code"></i>
                فحص ملفات القالب
            </h2>
            
            <?php
            $required_files = array(
                'style.css' => 'ملف الأنماط الرئيسي',
                'index.php' => 'الصفحة الرئيسية',
                'header.php' => 'رأس الصفحة',
                'footer.php' => 'تذييل الصفحة',
                'functions.php' => 'وظائف القالب',
                'single-movie.php' => 'صفحة الفيلم',
                'single-series.php' => 'صفحة المسلسل',
                'archive-movie.php' => 'أرشيف الأفلام',
                'archive-series.php' => 'أرشيف المسلسلات',
                'search.php' => 'صفحة البحث',
                '404.php' => 'صفحة الخطأ 404'
            );
            
            foreach ($required_files as $file => $description) {
                $file_path = get_template_directory() . '/' . $file;
                $exists = file_exists($file_path);
                $class = $exists ? 'test-pass' : 'test-fail';
                $icon = $exists ? '✅' : '❌';
                $status = $exists ? 'موجود' : 'مفقود';
                
                echo "<div class='test-result {$class}'>";
                echo "{$icon} {$description} ({$file}): {$status}";
                echo "</div>";
            }
            ?>
        </div>

        <!-- Custom Post Types Check -->
        <div class="debug-section">
            <h2 class="debug-title">
                <i class="fas fa-database"></i>
                فحص أنواع المحتوى المخصص
            </h2>
            
            <?php
            $post_types = array('movie', 'series');
            
            foreach ($post_types as $post_type) {
                $exists = post_type_exists($post_type);
                $class = $exists ? 'test-pass' : 'test-fail';
                $icon = $exists ? '✅' : '❌';
                $status = $exists ? 'مسجل' : 'غير مسجل';
                
                echo "<div class='test-result {$class}'>";
                echo "{$icon} نوع المحتوى '{$post_type}': {$status}";
                echo "</div>";
                
                if ($exists) {
                    $count = wp_count_posts($post_type);
                    $published = $count->publish ?? 0;
                    echo "<div class='info-item'>";
                    echo "<div class='info-label'>عدد المنشورات المنشورة:</div>";
                    echo "<div class='info-value'>{$published}</div>";
                    echo "</div>";
                }
            }
            ?>
        </div>

        <!-- Taxonomies Check -->
        <div class="debug-section">
            <h2 class="debug-title">
                <i class="fas fa-tags"></i>
                فحص التصنيفات
            </h2>
            
            <?php
            $taxonomies = array(
                'movie_genre' => 'تصنيفات الأفلام',
                'series_genre' => 'تصنيفات المسلسلات',
                'country' => 'البلدان'
            );
            
            foreach ($taxonomies as $taxonomy => $description) {
                $exists = taxonomy_exists($taxonomy);
                $class = $exists ? 'test-pass' : 'test-fail';
                $icon = $exists ? '✅' : '❌';
                $status = $exists ? 'مسجل' : 'غير مسجل';
                
                echo "<div class='test-result {$class}'>";
                echo "{$icon} {$description} ({$taxonomy}): {$status}";
                echo "</div>";
                
                if ($exists) {
                    $terms = get_terms(array('taxonomy' => $taxonomy, 'hide_empty' => false));
                    $count = is_array($terms) ? count($terms) : 0;
                    echo "<div class='info-item'>";
                    echo "<div class='info-label'>عدد المصطلحات:</div>";
                    echo "<div class='info-value'>{$count}</div>";
                    echo "</div>";
                }
            }
            ?>
        </div>

        <!-- Memory and Performance -->
        <div class="debug-section">
            <h2 class="debug-title">
                <i class="fas fa-tachometer-alt"></i>
                الأداء والذاكرة
            </h2>
            
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">حد الذاكرة:</div>
                    <div class="info-value"><?php echo ini_get('memory_limit'); ?></div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">الذاكرة المستخدمة:</div>
                    <div class="info-value"><?php echo round(memory_get_usage() / 1024 / 1024, 2); ?> MB</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">أقصى وقت تنفيذ:</div>
                    <div class="info-value"><?php echo ini_get('max_execution_time'); ?> ثانية</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">حجم الرفع الأقصى:</div>
                    <div class="info-value"><?php echo ini_get('upload_max_filesize'); ?></div>
                </div>
            </div>
        </div>

        <!-- Quick Fixes -->
        <div class="debug-section">
            <h2 class="debug-title">
                <i class="fas fa-tools"></i>
                إصلاحات سريعة
            </h2>
            
            <div style="background: #334155; padding: 1.5rem; border-radius: 10px; margin-top: 1rem;">
                <h3 style="color: #f59e0b; margin-bottom: 1rem;">إذا كان الموقع عالق على شاشة التحميل:</h3>
                <ol style="color: #cbd5e1; line-height: 1.8;">
                    <li>تأكد من تفعيل القالب من لوحة التحكم</li>
                    <li>امسح الكاش إن وجد</li>
                    <li>تحقق من وجود أخطاء JavaScript في المتصفح (F12)</li>
                    <li>تأكد من تحميل ملفات CSS و JavaScript</li>
                    <li>جرب إلغاء تفعيل الإضافات مؤقتاً</li>
                </ol>
            </div>
            
            <div style="background: #334155; padding: 1.5rem; border-radius: 10px; margin-top: 1rem;">
                <h3 style="color: #10b981; margin-bottom: 1rem;">لإضافة محتوى تجريبي:</h3>
                <ol style="color: #cbd5e1; line-height: 1.8;">
                    <li>اذهب إلى "الأفلام" ← "إضافة جديد"</li>
                    <li>أدخل عنوان الفيلم ووصف</li>
                    <li>ارفع صورة البوستر</li>
                    <li>املأ التفاصيل (التقييم، السنة، المدة)</li>
                    <li>انشر الفيلم</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        console.log('CinemaHub Pro Debug Page Loaded');
        console.log('WordPress Version: <?php echo get_bloginfo("version"); ?>');
        console.log('PHP Version: <?php echo PHP_VERSION; ?>');
        console.log('Theme: <?php echo get_template(); ?>');
    </script>
</body>
</html>
