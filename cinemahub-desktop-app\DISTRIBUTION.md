# 📦 دليل التوزيع - إنشاء ملفات التثبيت للمستخدمين

دليل شامل لإنشاء ملفات تثبيت جاهزة للمستخدمين العاديين بدون الحاجة لـ Node.js

## 🎯 **الهدف**

إنشاء ملفات تثبيت قابلة للتنفيذ يمكن للمستخدمين العاديين تحميلها وتثبيتها مباشرة:
- **Windows**: ملف `.exe` للتثبيت + نسخة محمولة
- **macOS**: ملف `.dmg` للتثبيت + ملف `.zip`
- **Linux**: ملفات `.AppImage`, `.deb`, `.rpm`

## 🛠️ **المتطلبات للمطور**

### البرامج المطلوبة
```bash
# Node.js (للمطور فقط)
node --version  # يجب أن يكون >= 16.0.0

# npm
npm --version

# Git (اختياري)
git --version
```

### تثبيت أدوات البناء
```bash
# انتقل لمجلد المشروع
cd cinemahub-desktop-app

# تثبيت التبعيات
npm install

# تثبيت electron-builder عالمياً (اختياري)
npm install -g electron-builder
```

## 📁 **إعداد الأصول المطلوبة**

### 1. **إنشاء الأيقونات**

يجب إنشاء الأيقونات التالية في مجلد `assets/`:

#### **icon.png** (512x512 بكسل)
```bash
# يمكن استخدام أي أداة تصميم لإنشاء أيقونة 512x512
# مثل: Photoshop, GIMP, Canva, Figma
```

#### **icon.ico** (Windows)
```bash
# تحويل PNG إلى ICO باستخدام ImageMagick
convert assets/icon.png -resize 256x256 assets/icon.ico

# أو استخدام أدوات أونلاين:
# https://convertio.co/png-ico/
# https://icoconvert.com/
```

#### **icon.icns** (macOS)
```bash
# على macOS
mkdir icon.iconset
sips -z 16 16 assets/icon.png --out icon.iconset/icon_16x16.png
sips -z 32 32 assets/icon.png --out icon.iconset/<EMAIL>
sips -z 32 32 assets/icon.png --out icon.iconset/icon_32x32.png
sips -z 64 64 assets/icon.png --out icon.iconset/<EMAIL>
sips -z 128 128 assets/icon.png --out icon.iconset/icon_128x128.png
sips -z 256 256 assets/icon.png --out icon.iconset/<EMAIL>
sips -z 256 256 assets/icon.png --out icon.iconset/icon_256x256.png
sips -z 512 512 assets/icon.png --out icon.iconset/<EMAIL>
sips -z 512 512 assets/icon.png --out icon.iconset/icon_512x512.png
sips -z 1024 1024 assets/icon.png --out icon.iconset/<EMAIL>
iconutil -c icns icon.iconset
mv icon.icns assets/
rm -rf icon.iconset
```

### 2. **ملفات إضافية (اختيارية)**
```bash
# صورة خلفية لـ DMG على macOS
assets/dmg-background.png (540x380 بكسل)

# صورة ترحيب للـ installer على Windows
assets/installer-welcome.bmp (164x314 بكسل)
```

## 🚀 **إنشاء ملفات التثبيت**

### **البناء لجميع المنصات**
```bash
# بناء شامل (يتطلب وقت أطول)
npm run build

# سيتم إنشاء الملفات في مجلد dist/
```

### **البناء لمنصة محددة**

#### **Windows**
```bash
# إنشاء installer + نسخة محمولة
npm run build-win

# الملفات المُنتجة:
# dist/CinemaHub Pro Desktop-Setup-1.0.0.exe (installer)
# dist/CinemaHub Pro Desktop-1.0.0-Portable.exe (محمول)
```

#### **macOS**
```bash
# إنشاء DMG + ZIP
npm run build-mac

# الملفات المُنتجة:
# dist/CinemaHub Pro Desktop-1.0.0.dmg (installer)
# dist/CinemaHub Pro Desktop-1.0.0-mac.zip (أرشيف)
```

#### **Linux**
```bash
# إنشاء AppImage + DEB + RPM
npm run build-linux

# الملفات المُنتجة:
# dist/CinemaHub Pro Desktop-1.0.0-x86_64.AppImage (محمول)
# dist/CinemaHub Pro Desktop-1.0.0-amd64.deb (Ubuntu/Debian)
# dist/CinemaHub Pro Desktop-1.0.0-x86_64.rpm (RedHat/CentOS)
```

## 📋 **أنواع ملفات التوزيع**

### **Windows**

#### **1. NSIS Installer (.exe)**
- **الوصف**: ملف تثبيت تقليدي مع معالج التثبيت
- **المميزات**: 
  - اختيار مجلد التثبيت
  - إنشاء اختصارات سطح المكتب وقائمة ابدأ
  - إلغاء تثبيت نظيف
  - تسجيل في Windows Registry
- **الحجم**: ~150-200 MB
- **الاستخدام**: للمستخدمين الذين يريدون تثبيت دائم

#### **2. Portable (.exe)**
- **الوصف**: نسخة محمولة لا تحتاج تثبيت
- **المميزات**:
  - تشغيل مباشر بدون تثبيت
  - يمكن نقلها على USB
  - لا تترك آثار في النظام
- **الحجم**: ~150-200 MB
- **الاستخدام**: للاختبار أو الاستخدام المؤقت

### **macOS**

#### **1. DMG Installer (.dmg)**
- **الوصف**: ملف صورة قرص مع واجهة تثبيت جميلة
- **المميزات**:
  - سحب وإفلات للتثبيت
  - واجهة مخصصة مع خلفية
  - تكامل مع مجلد Applications
- **الحجم**: ~150-200 MB
- **الاستخدام**: الطريقة المعتادة لتوزيع تطبيقات macOS

#### **2. ZIP Archive (.zip)**
- **الوصف**: أرشيف مضغوط يحتوي على التطبيق
- **المميزات**:
  - حجم أصغر
  - استخراج وتشغيل مباشر
- **الحجم**: ~120-150 MB
- **الاستخدام**: للتوزيع السريع أو التحديثات

### **Linux**

#### **1. AppImage (.AppImage)**
- **الوصف**: تطبيق محمول يعمل على جميع توزيعات Linux
- **المميزات**:
  - لا يحتاج تثبيت أو صلاحيات root
  - يعمل على جميع التوزيعات
  - ملف واحد قابل للتنفيذ
- **الحجم**: ~150-200 MB
- **الاستخدام**: الأسهل للمستخدمين

#### **2. DEB Package (.deb)**
- **الوصف**: حزمة Debian للتوزيعات المبنية على Debian
- **المميزات**:
  - تكامل مع مدير الحزم
  - تثبيت وإلغاء تثبيت نظيف
  - إدارة التبعيات
- **الحجم**: ~120-150 MB
- **الاستخدام**: Ubuntu, Debian, Linux Mint

#### **3. RPM Package (.rpm)**
- **الوصف**: حزمة RedHat للتوزيعات المبنية على RedHat
- **المميزات**:
  - تكامل مع مدير الحزم
  - تثبيت وإلغاء تثبيت نظيف
- **الحجم**: ~120-150 MB
- **الاستخدام**: CentOS, RHEL, Fedora, openSUSE

## 📤 **توزيع الملفات**

### **1. رفع على GitHub Releases**
```bash
# إنشاء release جديد
git tag v1.0.0
git push origin v1.0.0

# رفع الملفات يدوياً على GitHub Releases
# أو استخدام GitHub CLI
gh release create v1.0.0 dist/* --title "CinemaHub Pro Desktop v1.0.0"
```

### **2. رفع على خدمات التخزين السحابي**
- **Google Drive**: مجاني، سهل المشاركة
- **Dropbox**: مجاني حتى 2GB
- **OneDrive**: مجاني حتى 5GB
- **MediaFire**: مجاني، مخصص للملفات الكبيرة

### **3. إنشاء موقع تحميل**
```html
<!-- صفحة تحميل بسيطة -->
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <title>تحميل CinemaHub Pro Desktop</title>
</head>
<body>
    <h1>تحميل CinemaHub Pro Desktop</h1>
    
    <h2>Windows</h2>
    <a href="CinemaHub-Pro-Desktop-Setup-1.0.0.exe">تحميل Installer (مستحسن)</a><br>
    <a href="CinemaHub-Pro-Desktop-1.0.0-Portable.exe">تحميل النسخة المحمولة</a>
    
    <h2>macOS</h2>
    <a href="CinemaHub-Pro-Desktop-1.0.0.dmg">تحميل DMG</a><br>
    <a href="CinemaHub-Pro-Desktop-1.0.0-mac.zip">تحميل ZIP</a>
    
    <h2>Linux</h2>
    <a href="CinemaHub-Pro-Desktop-1.0.0-x86_64.AppImage">تحميل AppImage (مستحسن)</a><br>
    <a href="CinemaHub-Pro-Desktop-1.0.0-amd64.deb">تحميل DEB (Ubuntu/Debian)</a><br>
    <a href="CinemaHub-Pro-Desktop-1.0.0-x86_64.rpm">تحميل RPM (CentOS/RHEL)</a>
</body>
</html>
```

## 📋 **تعليمات للمستخدمين**

### **Windows**

#### **طريقة 1: Installer (مستحسن)**
1. حمل ملف `CinemaHub-Pro-Desktop-Setup-1.0.0.exe`
2. انقر نقراً مزدوجاً على الملف
3. اتبع تعليمات معالج التثبيت
4. اختر مجلد التثبيت (اختياري)
5. انتظر انتهاء التثبيت
6. شغل البرنامج من سطح المكتب أو قائمة ابدأ

#### **طريقة 2: النسخة المحمولة**
1. حمل ملف `CinemaHub-Pro-Desktop-1.0.0-Portable.exe`
2. انقر نقراً مزدوجاً على الملف لتشغيله مباشرة
3. لا يحتاج تثبيت

### **macOS**

#### **طريقة 1: DMG (مستحسن)**
1. حمل ملف `CinemaHub-Pro-Desktop-1.0.0.dmg`
2. انقر نقراً مزدوجاً لفتح الملف
3. اسحب أيقونة التطبيق إلى مجلد Applications
4. شغل التطبيق من مجلد Applications

#### **طريقة 2: ZIP**
1. حمل ملف `CinemaHub-Pro-Desktop-1.0.0-mac.zip`
2. انقر نقراً مزدوجاً لاستخراج الملف
3. انقل التطبيق إلى مجلد Applications
4. شغل التطبيق

### **Linux**

#### **طريقة 1: AppImage (مستحسن)**
1. حمل ملف `CinemaHub-Pro-Desktop-1.0.0-x86_64.AppImage`
2. اجعل الملف قابل للتنفيذ: `chmod +x CinemaHub-Pro-Desktop-1.0.0-x86_64.AppImage`
3. انقر نقراً مزدوجاً أو شغل من Terminal

#### **طريقة 2: DEB (Ubuntu/Debian)**
```bash
# تثبيت باستخدام dpkg
sudo dpkg -i CinemaHub-Pro-Desktop-1.0.0-amd64.deb

# أو باستخدام apt
sudo apt install ./CinemaHub-Pro-Desktop-1.0.0-amd64.deb
```

#### **طريقة 3: RPM (CentOS/RHEL)**
```bash
# تثبيت باستخدام rpm
sudo rpm -i CinemaHub-Pro-Desktop-1.0.0-x86_64.rpm

# أو باستخدام yum/dnf
sudo yum install CinemaHub-Pro-Desktop-1.0.0-x86_64.rpm
```

## 🔧 **استكشاف الأخطاء**

### **مشاكل البناء**

#### **خطأ: الأيقونات مفقودة**
```bash
# تأكد من وجود الأيقونات
ls -la assets/
# يجب أن تجد: icon.png, icon.ico, icon.icns
```

#### **خطأ: نفاد المساحة**
```bash
# تنظيف ملفات البناء السابقة
rm -rf dist/
rm -rf node_modules/.cache/
```

#### **خطأ: صلاحيات على macOS**
```bash
# إعطاء صلاحيات للملفات
chmod +x node_modules/.bin/electron-builder
```

### **مشاكل التوزيع**

#### **حجم الملفات كبير**
- استخدم ضغط maximum في إعدادات البناء
- استبعد ملفات غير ضرورية
- استخدم asar packing

#### **مشاكل الأمان على Windows**
- وقع الملفات رقمياً (يتطلب شهادة)
- أضف استثناءات لبرامج مكافحة الفيروسات

## 📊 **إحصائيات الملفات**

| المنصة | النوع | الحجم التقريبي | وقت البناء |
|---------|--------|----------------|-------------|
| Windows | NSIS Installer | 180 MB | 3-5 دقائق |
| Windows | Portable | 175 MB | 2-3 دقائق |
| macOS | DMG | 185 MB | 4-6 دقائق |
| macOS | ZIP | 140 MB | 2-3 دقائق |
| Linux | AppImage | 190 MB | 3-4 دقائق |
| Linux | DEB | 145 MB | 2-3 دقائق |
| Linux | RPM | 145 MB | 2-3 دقائق |

---

**ملاحظة مهمة**: بعد إنشاء ملفات التوزيع، اختبرها على أجهزة مختلفة للتأكد من عملها بشكل صحيح قبل توزيعها للمستخدمين.
