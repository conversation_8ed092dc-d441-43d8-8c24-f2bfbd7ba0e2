/**
 * CinemaHub Pro WordPress Theme JavaScript
 * 
 * @package CinemaHub Pro
 * @version 1.0
 */

(function($) {
    'use strict';

    // Document Ready
    $(document).ready(function() {
        initializeTheme();
    });

    // Window Load
    $(window).on('load', function() {
        hideLoadingScreen();
        initializeAOS();
    });

    /**
     * Initialize Theme
     */
    function initializeTheme() {
        initializeHeaderScroll();
        initializeBackToTop();
        initializeSearch();
        initializeMobileMenu();
        initializeMovieCards();
        initializeCategoryCards();
        initializeTooltips();
        
        console.log('CinemaHub Pro WordPress Theme initialized successfully!');
    }

    /**
     * Hide Loading Screen
     */
    function hideLoadingScreen() {
        const loadingScreen = $('#loadingScreen');
        if (loadingScreen.length) {
            setTimeout(function() {
                loadingScreen.addClass('hidden');
                setTimeout(function() {
                    loadingScreen.remove();
                }, 500);
            }, 1500);
        }
    }

    /**
     * Initialize AOS Animations
     */
    function initializeAOS() {
        if (typeof AOS !== 'undefined') {
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true,
                offset: 100,
                disable: function() {
                    return window.innerWidth < 768;
                }
            });
        }
    }

    /**
     * Header Scroll Effect
     */
    function initializeHeaderScroll() {
        const header = $('#header');
        if (!header.length) return;

        $(window).on('scroll', function() {
            if ($(window).scrollTop() > 50) {
                header.addClass('scrolled');
            } else {
                header.removeClass('scrolled');
            }
        });
    }

    /**
     * Back to Top Button
     */
    function initializeBackToTop() {
        const backToTopBtn = $('#backToTop');
        if (!backToTopBtn.length) return;

        // Show/hide button based on scroll position
        $(window).on('scroll', function() {
            if ($(window).scrollTop() > 300) {
                backToTopBtn.addClass('show');
            } else {
                backToTopBtn.removeClass('show');
            }
        });

        // Smooth scroll to top when clicked
        backToTopBtn.on('click', function(e) {
            e.preventDefault();
            $('html, body').animate({
                scrollTop: 0
            }, 800, 'easeInOutCubic');
        });
    }

    /**
     * Enhanced Search Functionality
     */
    function initializeSearch() {
        const searchForms = $('.search-form');
        
        searchForms.each(function() {
            const form = $(this);
            const input = form.find('input[type="search"]');
            const button = form.find('button[type="submit"]');

            // Add loading state to search button
            form.on('submit', function() {
                button.html('<i class="fas fa-spinner fa-spin"></i>');
                button.prop('disabled', true);
            });

            // Auto-complete functionality (if needed)
            input.on('input', function() {
                const query = $(this).val();
                if (query.length > 2) {
                    // Add auto-complete logic here if needed
                    console.log('Searching for:', query);
                }
            });

            // Clear search
            input.on('keyup', function(e) {
                if (e.keyCode === 27) { // Escape key
                    $(this).val('');
                }
            });
        });
    }

    /**
     * Mobile Menu Enhancement
     */
    function initializeMobileMenu() {
        const mobileToggle = $('.navbar-toggler');
        const mobileNav = $('#mobileNav');

        if (!mobileToggle.length || !mobileNav.length) return;

        // Add animation classes
        mobileNav.on('show.bs.collapse', function() {
            $(this).addClass('showing');
        });

        mobileNav.on('shown.bs.collapse', function() {
            $(this).removeClass('showing').addClass('show');
        });

        mobileNav.on('hide.bs.collapse', function() {
            $(this).addClass('hiding');
        });

        mobileNav.on('hidden.bs.collapse', function() {
            $(this).removeClass('hiding show');
        });

        // Close mobile menu when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.navbar-toggler, #mobileNav').length) {
                if (mobileNav.hasClass('show')) {
                    mobileToggle.click();
                }
            }
        });
    }

    /**
     * Movie Cards Enhancement
     */
    function initializeMovieCards() {
        const movieCards = $('.movie-card');

        movieCards.each(function() {
            const card = $(this);
            const overlay = card.find('.movie-overlay');
            const playBtn = card.find('.play-btn');

            // Add hover effects
            card.on('mouseenter', function() {
                $(this).addClass('hovered');
                overlay.fadeIn(300);
            });

            card.on('mouseleave', function() {
                $(this).removeClass('hovered');
                overlay.fadeOut(300);
            });

            // Play button click
            playBtn.on('click', function(e) {
                e.stopPropagation();
                const movieUrl = card.find('.movie-title a').attr('href');
                if (movieUrl) {
                    window.location.href = movieUrl;
                }
            });

            // Add loading state for images
            const img = card.find('img');
            if (img.length) {
                img.on('load', function() {
                    $(this).addClass('loaded');
                });

                img.on('error', function() {
                    $(this).attr('src', 'https://via.placeholder.com/300x400/1e293b/ffffff?text=No+Image');
                });
            }
        });
    }

    /**
     * Category Cards Enhancement
     */
    function initializeCategoryCards() {
        const categoryCards = $('.category-card');

        categoryCards.each(function() {
            const card = $(this);
            const icon = card.find('.category-icon');

            card.on('mouseenter', function() {
                icon.addClass('animated');
                $(this).addClass('hovered');
            });

            card.on('mouseleave', function() {
                icon.removeClass('animated');
                $(this).removeClass('hovered');
            });

            // Add click ripple effect
            card.on('click', function(e) {
                const ripple = $('<span class="ripple"></span>');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.css({
                    width: size,
                    height: size,
                    left: x,
                    top: y
                });

                $(this).append(ripple);

                setTimeout(function() {
                    ripple.remove();
                }, 600);
            });
        });
    }

    /**
     * Initialize Tooltips
     */
    function initializeTooltips() {
        // Initialize Bootstrap tooltips if available
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }

    /**
     * AJAX Load More Functionality
     */
    function initializeLoadMore() {
        const loadMoreBtn = $('.load-more-btn');
        
        loadMoreBtn.on('click', function(e) {
            e.preventDefault();
            
            const button = $(this);
            const page = button.data('page') || 2;
            const postType = button.data('post-type') || 'post';
            
            button.html('<i class="fas fa-spinner fa-spin"></i> جاري التحميل...');
            button.prop('disabled', true);
            
            $.ajax({
                url: cinemahub_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'load_more_posts',
                    page: page,
                    post_type: postType,
                    nonce: cinemahub_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        const container = button.closest('section').find('.movies-grid, .blog-posts');
                        container.append(response.data.html);
                        
                        button.data('page', page + 1);
                        button.html('تحميل المزيد <i class="fas fa-arrow-down"></i>');
                        button.prop('disabled', false);
                        
                        // Re-initialize movie cards for new content
                        initializeMovieCards();
                        
                        // Initialize AOS for new elements
                        if (typeof AOS !== 'undefined') {
                            AOS.refresh();
                        }
                        
                        if (!response.data.has_more) {
                            button.hide();
                        }
                    } else {
                        button.html('حدث خطأ');
                        setTimeout(function() {
                            button.html('تحميل المزيد <i class="fas fa-arrow-down"></i>');
                            button.prop('disabled', false);
                        }, 2000);
                    }
                },
                error: function() {
                    button.html('حدث خطأ');
                    setTimeout(function() {
                        button.html('تحميل المزيد <i class="fas fa-arrow-down"></i>');
                        button.prop('disabled', false);
                    }, 2000);
                }
            });
        });
    }

    /**
     * Smooth Scrolling for Anchor Links
     */
    function initializeSmoothScrolling() {
        $('a[href*="#"]:not([href="#"])').on('click', function(e) {
            const target = $(this.hash);
            if (target.length) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: target.offset().top - 80
                }, 800, 'easeInOutCubic');
            }
        });
    }

    /**
     * Lazy Loading for Images
     */
    function initializeLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver(function(entries, observer) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        img.classList.add('loaded');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(function(img) {
                imageObserver.observe(img);
            });
        }
    }

    /**
     * Initialize Swiper Sliders
     */
    function initializeSwiper() {
        if (typeof Swiper !== 'undefined') {
            // Hero Slider
            const heroSwiper = new Swiper('.hero-swiper', {
                loop: true,
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },
                effect: 'fade',
                fadeEffect: {
                    crossFade: true
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
            });

            // Movies Slider
            const moviesSwiper = new Swiper('.movies-swiper', {
                slidesPerView: 1,
                spaceBetween: 20,
                loop: true,
                autoplay: {
                    delay: 3000,
                    disableOnInteraction: false,
                },
                breakpoints: {
                    576: {
                        slidesPerView: 2,
                    },
                    768: {
                        slidesPerView: 3,
                    },
                    992: {
                        slidesPerView: 4,
                    },
                    1200: {
                        slidesPerView: 5,
                    }
                },
                navigation: {
                    nextEl: '.movies-swiper-next',
                    prevEl: '.movies-swiper-prev',
                },
            });
        }
    }

    // Initialize additional features
    initializeLoadMore();
    initializeSmoothScrolling();
    initializeLazyLoading();
    initializeSwiper();

    // Custom easing function
    $.easing.easeInOutCubic = function(x, t, b, c, d) {
        if ((t /= d / 2) < 1) return c / 2 * t * t * t + b;
        return c / 2 * ((t -= 2) * t * t + 2) + b;
    };

})(jQuery);

// Vanilla JavaScript for performance-critical features
document.addEventListener('DOMContentLoaded', function() {
    // Performance monitoring
    if ('performance' in window) {
        window.addEventListener('load', function() {
            setTimeout(function() {
                const perfData = performance.getEntriesByType('navigation')[0];
                console.log('Page Load Time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
            }, 0);
        });
    }

    // Service Worker registration (if needed)
    if ('serviceWorker' in navigator) {
        // Uncomment if you want to add PWA features
        // navigator.serviceWorker.register('/sw.js');
    }
});
