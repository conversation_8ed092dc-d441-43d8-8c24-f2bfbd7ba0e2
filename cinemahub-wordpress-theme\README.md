# CinemaHub Pro - قالب ووردبريس احترافي للأفلام والمسلسلات

## 🎬 نظرة عامة

CinemaHub Pro هو قالب ووردبريس احترافي مصمم خصيصاً لمواقع الأفلام والمسلسلات العربية. يتميز بتصميم عصري ومتجاوب مع خاصية فريدة لاستعادة المظهر الأصلي.

## ✨ المميزات الرئيسية

### 🎨 التصميم
- تصميم عصري بألوان أحمر وأسود احترافية
- متجاوب مع جميع الأجهزة (هاتف، تابلت، كمبيوتر)
- خطوط عربية جميلة (Cairo Font)
- تأثيرات بصرية متقدمة وانيميشن

### 🎭 إدارة المحتوى
- نوع محتوى مخصص للأفلام (Movies)
- نوع محتوى مخصص للمسلسلات (Series)
- تصنيفات متقدمة (النوع، البلد، السنة)
- حقول مخصصة (التقييم، المدة، روابط المشاهدة)

### 🔧 خاصية استعادة المظهر الأصلي
- استعادة التصميم الأصلي بضغطة زر واحدة
- إنشاء نسخ احتياطية تلقائية
- إدارة النسخ الاحتياطية
- حماية من فقدان التخصيصات

### 🚀 الأداء والتحسين
- كود محسن وسريع التحميل
- متوافق مع SEO
- دعم Schema Markup
- تحميل كسول للصور

## 📦 التثبيت

### الطريقة الأولى: رفع عبر لوحة التحكم
1. اذهب إلى `المظهر` ← `قوالب`
2. اضغط `إضافة جديد`
3. اضغط `رفع قالب`
4. اختر ملف `cinemahub-wordpress-theme.zip`
5. اضغط `تثبيت الآن`
6. اضغط `تفعيل`

### الطريقة الثانية: رفع عبر FTP
1. فك ضغط ملف `cinemahub-wordpress-theme.zip`
2. ارفع مجلد `cinemahub-wordpress-theme` إلى `/wp-content/themes/`
3. اذهب إلى `المظهر` ← `قوالب`
4. فعل "CinemaHub Pro"

## ⚙️ الإعداد الأولي

### 1. إعداد القوائم
- اذهب إلى `المظهر` ← `قوائم`
- أنشئ قائمة جديدة وأضف الروابط المطلوبة
- اختر موقع القائمة: "القائمة الرئيسية"

### 2. إضافة المحتوى
- اذهب إلى `الأفلام` ← `إضافة جديد`
- أو `المسلسلات` ← `إضافة جديد`
- املأ التفاصيل المطلوبة
- ارفع صورة مميزة (البوستر)

### 3. إعداد التصنيفات
- اذهب إلى `الأفلام` ← `تصنيفات الأفلام`
- أضف التصنيفات المطلوبة (أكشن، دراما، كوميديا، إلخ)
- كرر نفس العملية للمسلسلات والبلدان

## 🎯 استخدام خاصية استعادة المظهر الأصلي

### الوصول للخاصية
يمكنك الوصول لخاصية الاستعادة من:
1. الرابط المباشر: `yoursite.com/wp-content/themes/cinemahub-wordpress-theme/restore-original.php`
2. من إعدادات القالب: `المظهر` ← `إعدادات القالب`
3. من الفوتر: رابط "استعادة المظهر الأصلي"

### كيفية الاستخدام
1. **استعادة المظهر الأصلي:**
   - اضغط على "استعادة المظهر الأصلي"
   - أكد العملية
   - سيتم إنشاء نسخة احتياطية تلقائياً

2. **إدارة النسخ الاحتياطية:**
   - عرض جميع النسخ الاحتياطية المتاحة
   - استعادة أي نسخة احتياطية
   - حذف النسخ غير المرغوب فيها

## 📋 الحقول المخصصة

### للأفلام
- **التقييم:** من 0 إلى 10
- **سنة الإنتاج:** من 1900 إلى 2030
- **المدة:** بالدقائق
- **رابط الإعلان:** YouTube أو أي رابط فيديو
- **رابط المشاهدة:** رابط مشاهدة الفيلم

### للمسلسلات
- **التقييم:** من 0 إلى 10
- **سنة الإنتاج:** من 1900 إلى 2030
- **عدد المواسم:** رقم
- **عدد الحلقات:** رقم إجمالي
- **حالة المسلسل:** مستمر/مكتمل/ملغي
- **رابط الإعلان:** YouTube أو أي رابط فيديو
- **رابط المشاهدة:** رابط مشاهدة المسلسل

## 🎨 التخصيص

### الألوان الأساسية
- **اللون الأساسي:** #dc2626 (أحمر)
- **اللون الثانوي:** #1e293b (أسود مزرق)
- **لون التمييز:** #fbbf24 (ذهبي)
- **لون الخلفية:** #0f172a (أسود داكن)

### تخصيص الألوان
1. اذهب إلى `المظهر` ← `تخصيص`
2. اختر `الألوان`
3. غير الألوان حسب رغبتك
4. احفظ التغييرات

### إضافة CSS مخصص
1. اذهب إلى `المظهر` ← `تخصيص`
2. اختر `CSS إضافي`
3. أضف الكود المخصص
4. احفظ التغييرات

## 🔧 استكشاف الأخطاء وإصلاحها

### المشكلة: القالب لا يظهر بالشكل الصحيح
**الحل:**
1. تأكد من تفعيل القالب من `المظهر` ← `قوالب`
2. امسح الكاش إن وجد
3. تحقق من وجود ملف `style.css` في مجلد القالب

### المشكلة: الأفلام والمسلسلات لا تظهر
**الحل:**
1. اذهب إلى `الإعدادات` ← `روابط دائمة`
2. اضغط `حفظ التغييرات` لإعادة تحديث الروابط
3. تأكد من وجود محتوى في `الأفلام` أو `المسلسلات`

### المشكلة: خاصية الاستعادة لا تعمل
**الحل:**
1. تأكد من صلاحيات الملفات (755 للمجلدات، 644 للملفات)
2. تحقق من وجود ملف `restore-original.php`
3. تأكد من تسجيل الدخول كمدير

## 📱 التوافق

### المتصفحات المدعومة
- Chrome (آخر إصدارين)
- Firefox (آخر إصدارين)
- Safari (آخر إصدارين)
- Edge (آخر إصدارين)

### إصدارات ووردبريس
- WordPress 5.0 أو أحدث
- PHP 7.4 أو أحدث
- MySQL 5.6 أو أحدث

## 🆘 الدعم الفني

### الحصول على المساعدة
1. **الوثائق:** راجع هذا الملف أولاً
2. **الأخطاء الشائعة:** تحقق من قسم استكشاف الأخطاء
3. **النسخ الاحتياطية:** استخدم خاصية الاستعادة عند الحاجة

### معلومات مهمة للدعم
عند طلب المساعدة، يرجى تقديم:
- إصدار ووردبريس
- إصدار PHP
- رسالة الخطأ (إن وجدت)
- خطوات إعادة إنتاج المشكلة

## 📄 الترخيص

هذا القالب مرخص تحت رخصة GPL v2 أو أحدث.

## 🔄 سجل التحديثات

### الإصدار 2.0.0
- إضافة خاصية استعادة المظهر الأصلي
- تحسين الأداء والسرعة
- إضافة المزيد من التأثيرات البصرية
- تحسين التوافق مع الأجهزة المحمولة

### الإصدار 1.0.0
- الإصدار الأولي
- دعم الأفلام والمسلسلات
- تصميم متجاوب
- حقول مخصصة

---

**تم التطوير بواسطة فريق CinemaHub Pro**  
**صُنع بحب للمحتوى العربي** ❤️
