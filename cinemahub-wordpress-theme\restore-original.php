<?php
/**
 * Restore Original Theme Design
 * 
 * @package CinemaHub Pro
 */

// Security check
if (!defined('ABSPATH')) {
    // If accessed directly, load WordPress
    require_once('../../../wp-config.php');
}

// Check user permissions
if (!current_user_can('manage_options')) {
    wp_die('ليس لديك صلاحية للوصول إلى هذه الصفحة.');
}

// Handle form submission
$message = '';
$message_type = '';

if (isset($_POST['restore_original'])) {
    if (wp_verify_nonce($_POST['restore_nonce'], 'restore_original_design')) {
        
        // Create backup of current customizations
        $backup_data = array(
            'timestamp' => current_time('mysql'),
            'customizer_settings' => get_theme_mods(),
            'custom_css' => wp_get_custom_css(),
            'site_options' => array(
                'blogname' => get_option('blogname'),
                'blogdescription' => get_option('blogdescription'),
                'site_logo' => get_theme_mod('custom_logo'),
            )
        );
        
        // Save backup
        update_option('cinemahub_backup_' . date('Y_m_d_H_i_s'), $backup_data);
        
        // Restore original design
        remove_theme_mods();
        
        // Set default theme mods
        set_theme_mod('primary_color', '#dc2626');
        set_theme_mod('secondary_color', '#1e293b');
        set_theme_mod('accent_color', '#fbbf24');
        set_theme_mod('background_color', '#0f172a');
        
        // Clear custom CSS
        wp_update_custom_css_post('');
        
        // Clear any cached data
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
        
        $message = 'تم استعادة المظهر الأصلي بنجاح! تم إنشاء نسخة احتياطية من التخصيصات السابقة.';
        $message_type = 'success';
        
    } else {
        $message = 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.';
        $message_type = 'error';
    }
}

// Handle backup restoration
if (isset($_POST['restore_backup'])) {
    if (wp_verify_nonce($_POST['backup_nonce'], 'restore_backup_design')) {
        $backup_id = sanitize_text_field($_POST['backup_id']);
        $backup_data = get_option($backup_id);
        
        if ($backup_data) {
            // Restore theme mods
            if (isset($backup_data['customizer_settings'])) {
                foreach ($backup_data['customizer_settings'] as $key => $value) {
                    set_theme_mod($key, $value);
                }
            }
            
            // Restore custom CSS
            if (isset($backup_data['custom_css'])) {
                wp_update_custom_css_post($backup_data['custom_css']);
            }
            
            $message = 'تم استعادة النسخة الاحتياطية بنجاح!';
            $message_type = 'success';
        } else {
            $message = 'لم يتم العثور على النسخة الاحتياطية المحددة.';
            $message_type = 'error';
        }
    }
}

// Get available backups
$backups = array();
$all_options = wp_load_alloptions();
foreach ($all_options as $key => $value) {
    if (strpos($key, 'cinemahub_backup_') === 0) {
        $backups[$key] = maybe_unserialize($value);
    }
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استعادة المظهر الأصلي - CinemaHub Pro</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: white;
            direction: rtl;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #dc2626 0%, #fbbf24 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .header p {
            font-size: 1.2rem;
            color: #cbd5e1;
        }
        
        .card {
            background: rgba(30, 41, 59, 0.8);
            border-radius: 20px;
            padding: 2.5rem;
            margin-bottom: 2rem;
            border: 2px solid rgba(148, 163, 184, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .card h2 {
            color: #dc2626;
            margin-bottom: 1.5rem;
            font-size: 1.8rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .message {
            padding: 1rem 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            font-weight: 600;
        }
        
        .message.success {
            background: rgba(16, 185, 129, 0.2);
            border: 2px solid #10b981;
            color: #10b981;
        }
        
        .message.error {
            background: rgba(239, 68, 68, 0.2);
            border: 2px solid #ef4444;
            color: #ef4444;
        }
        
        .btn {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(220, 38, 38, 0.4);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
        }
        
        .btn-success:hover {
            box-shadow: 0 10px 25px rgba(5, 150, 105, 0.4);
        }
        
        .warning-box {
            background: rgba(245, 158, 11, 0.2);
            border: 2px solid #f59e0b;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        
        .warning-box h3 {
            color: #f59e0b;
            margin-bottom: 1rem;
        }
        
        .info-box {
            background: rgba(59, 130, 246, 0.2);
            border: 2px solid #3b82f6;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        
        .info-box h3 {
            color: #3b82f6;
            margin-bottom: 1rem;
        }
        
        .backup-item {
            background: rgba(15, 23, 42, 0.6);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border: 2px solid rgba(148, 163, 184, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .backup-info h4 {
            color: white;
            margin-bottom: 0.5rem;
        }
        
        .backup-info p {
            color: #94a3b8;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #cbd5e1;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0.5rem 0;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: 20px;
            height: 20px;
        }
        
        .back-link {
            position: fixed;
            top: 2rem;
            left: 2rem;
            background: rgba(30, 41, 59, 0.9);
            color: white;
            padding: 1rem;
            border-radius: 50%;
            text-decoration: none;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .back-link:hover {
            background: #dc2626;
            transform: scale(1.1);
        }
        
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .card {
                padding: 1.5rem;
            }
            
            .backup-item {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <a href="<?php echo admin_url(); ?>" class="back-link" title="العودة للوحة التحكم">
        <i class="fas fa-arrow-right"></i>
    </a>
    
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-undo"></i> استعادة المظهر الأصلي</h1>
            <p>استعد التصميم الأصلي لموقع CinemaHub Pro أو أدر النسخ الاحتياطية</p>
        </div>
        
        <?php if ($message): ?>
            <div class="message <?php echo $message_type; ?>">
                <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <!-- Restore Original Design -->
        <div class="card">
            <h2><i class="fas fa-paint-brush"></i> استعادة المظهر الأصلي</h2>
            
            <div class="warning-box">
                <h3><i class="fas fa-exclamation-triangle"></i> تحذير مهم</h3>
                <ul style="color: #fbbf24; line-height: 1.6;">
                    <li>ستتم استعادة جميع الألوان والتصميم إلى الحالة الافتراضية</li>
                    <li>سيتم إنشاء نسخة احتياطية من التخصيصات الحالية تلقائياً</li>
                    <li>لن يتم حذف أي محتوى (أفلام، مسلسلات، مقالات)</li>
                    <li>يمكنك استعادة التخصيصات السابقة من النسخ الاحتياطية</li>
                </ul>
            </div>
            
            <div class="info-box">
                <h3><i class="fas fa-info-circle"></i> ما سيتم استعادته</h3>
                <ul style="color: #60a5fa; line-height: 1.6;">
                    <li>الألوان الأساسية: أحمر (#dc2626) وأسود (#0f172a)</li>
                    <li>تصميم الرأس والفوتر الأصلي</li>
                    <li>تخطيط الصفحات والقوائم</li>
                    <li>الخطوط والأحجام الافتراضية</li>
                    <li>إعدادات العرض والتنسيق</li>
                </ul>
            </div>
            
            <form method="post" style="text-align: center;">
                <?php wp_nonce_field('restore_original_design', 'restore_nonce'); ?>
                
                <div class="form-group">
                    <div class="checkbox-group" style="justify-content: center;">
                        <input type="checkbox" id="confirm_restore" name="confirm_restore" required>
                        <label for="confirm_restore">أؤكد أنني أريد استعادة المظهر الأصلي</label>
                    </div>
                </div>
                
                <button type="submit" name="restore_original" class="btn btn-success">
                    <i class="fas fa-undo"></i>
                    استعادة المظهر الأصلي الآن
                </button>
            </form>
        </div>
        
        <!-- Backup Management -->
        <div class="card">
            <h2><i class="fas fa-archive"></i> إدارة النسخ الاحتياطية</h2>
            
            <?php if (empty($backups)): ?>
                <div style="text-align: center; padding: 2rem; color: #94a3b8;">
                    <i class="fas fa-archive" style="font-size: 3rem; margin-bottom: 1rem; color: #dc2626;"></i>
                    <h3>لا توجد نسخ احتياطية</h3>
                    <p>سيتم إنشاء نسخة احتياطية تلقائياً عند استعادة المظهر الأصلي</p>
                </div>
            <?php else: ?>
                <p style="color: #cbd5e1; margin-bottom: 2rem;">
                    النسخ الاحتياطية المتاحة من التخصيصات السابقة:
                </p>
                
                <?php foreach ($backups as $backup_id => $backup_data): ?>
                    <div class="backup-item">
                        <div class="backup-info">
                            <h4>
                                <i class="fas fa-calendar"></i>
                                نسخة احتياطية - <?php echo date('Y/m/d H:i', strtotime($backup_data['timestamp'])); ?>
                            </h4>
                            <p>
                                <i class="fas fa-info-circle"></i>
                                تحتوي على: إعدادات المخصص، CSS مخصص، شعار الموقع
                            </p>
                        </div>
                        
                        <div style="display: flex; gap: 1rem;">
                            <form method="post" style="display: inline;">
                                <?php wp_nonce_field('restore_backup_design', 'backup_nonce'); ?>
                                <input type="hidden" name="backup_id" value="<?php echo esc_attr($backup_id); ?>">
                                <button type="submit" name="restore_backup" class="btn btn-secondary">
                                    <i class="fas fa-download"></i>
                                    استعادة
                                </button>
                            </form>
                            
                            <button onclick="deleteBackup('<?php echo esc_js($backup_id); ?>')" class="btn" style="background: #ef4444;">
                                <i class="fas fa-trash"></i>
                                حذف
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <!-- Quick Actions -->
        <div class="card">
            <h2><i class="fas fa-bolt"></i> إجراءات سريعة</h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                <a href="<?php echo admin_url('customize.php'); ?>" class="btn btn-secondary">
                    <i class="fas fa-paint-brush"></i>
                    تخصيص المظهر
                </a>
                
                <a href="<?php echo admin_url('themes.php'); ?>" class="btn btn-secondary">
                    <i class="fas fa-th-large"></i>
                    إدارة القوالب
                </a>
                
                <a href="<?php echo home_url(); ?>" class="btn btn-secondary">
                    <i class="fas fa-home"></i>
                    عرض الموقع
                </a>
                
                <a href="<?php echo admin_url('admin.php?page=cinemahub-settings'); ?>" class="btn btn-secondary">
                    <i class="fas fa-cog"></i>
                    إعدادات القالب
                </a>
            </div>
        </div>
    </div>
    
    <script>
        function deleteBackup(backupId) {
            if (confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟ لا يمكن التراجع عن هذا الإجراء.')) {
                // Send AJAX request to delete backup
                fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=delete_backup&backup_id=' + encodeURIComponent(backupId) + '&nonce=<?php echo wp_create_nonce('delete_backup'); ?>'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('حدث خطأ أثناء حذف النسخة الاحتياطية');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء حذف النسخة الاحتياطية');
                });
            }
        }
        
        // Add confirmation for restore original
        document.querySelector('form').addEventListener('submit', function(e) {
            if (e.target.querySelector('button[name="restore_original"]')) {
                if (!confirm('هل أنت متأكد من استعادة المظهر الأصلي؟ سيتم إنشاء نسخة احتياطية من التخصيصات الحالية.')) {
                    e.preventDefault();
                }
            }
        });
    </script>
</body>
</html>
