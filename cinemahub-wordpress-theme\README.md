# CinemaHub Pro - WordPress Theme

قالب WordPress احترافي لمواقع الأفلام والمسلسلات مع تصميم حديث ومتجاوب.

## 🌟 المميزات

### 🎨 التصميم
- **تصميم حديث ومتجاوب** - يعمل على جميع الأجهزة
- **شاشة تحميل متحركة** - تجربة مستخدم مميزة
- **هيدر ثابت مع تأثيرات** - تنقل سهل وجميل
- **تصنيفات تفاعلية** - كروت جميلة للتصنيفات
- **كروت أفلام احترافية** - عرض جذاب للمحتوى
- **فوتر شامل** - معلومات وروابط مفيدة

### 🔧 الوظائف
- **أنواع مقالات مخصصة** - أفلام ومسلسلات
- **تصنيفات مخصصة** - تنظيم المحتوى
- **حقول مخصصة** - تقييم، سنة، مدة، إلخ
- **نظام بحث متقدم** - بحث في جميع المحتوى
- **نظام تعليقات** - تفاعل المستخدمين
- **مشاركة اجتماعية** - مشاركة المحتوى
- **SEO محسن** - تحسين محركات البحث

### 📱 التجاوب
- **موبايل فيرست** - مصمم للهواتف أولاً
- **تابلت متوافق** - يعمل بشكل مثالي على التابلت
- **ديسكتوب محسن** - تجربة رائعة على الكمبيوتر

## 📋 متطلبات التشغيل

- **WordPress:** 5.0 أو أحدث
- **PHP:** 7.4 أو أحدث
- **MySQL:** 5.6 أو أحدث

## 🚀 التثبيت

### الطريقة الأولى: رفع مباشر
1. حمل مجلد `cinemahub-wordpress-theme`
2. ارفعه إلى `/wp-content/themes/`
3. اذهب إلى **المظهر > القوالب**
4. فعل قالب **CinemaHub Pro**

### الطريقة الثانية: ضغط ZIP
1. اضغط مجلد القالب كملف ZIP
2. اذهب إلى **المظهر > القوالب > إضافة جديد**
3. اختر **رفع قالب**
4. ارفع ملف ZIP وفعل القالب

## ⚙️ الإعداد الأولي

### 1. إنشاء القوائم
اذهب إلى **المظهر > القوائم** وأنشئ:
- **القائمة الرئيسية** - للهيدر
- **قائمة الفوتر** - للفوتر

### 2. إعداد الودجات
اذهب إلى **المظهر > الودجات** وأضف محتوى إلى:
- **الشريط الجانبي**
- **فوتر 1، 2، 3، 4**

### 3. إنشاء الصفحات المطلوبة
أنشئ الصفحات التالية:
- **الرئيسية**
- **اتصل بنا**
- **سياسة الخصوصية**
- **شروط الاستخدام**

### 4. إعداد القراءة
اذهب إلى **الإعدادات > القراءة** واختر:
- **الصفحة الرئيسية:** صفحة ثابتة
- **صفحة المقالات:** صفحة منفصلة

## 🎬 إضافة المحتوى

### إضافة فيلم جديد
1. اذهب إلى **الأفلام > إضافة جديد**
2. أدخل عنوان الفيلم
3. أضف وصف الفيلم
4. ارفع صورة مميزة (بوستر)
5. املأ تفاصيل الفيلم:
   - التقييم (من 10)
   - سنة الإنتاج
   - المدة بالدقائق
   - رابط الإعلان
   - رابط المشاهدة
6. اختر التصنيف والبلد
7. انشر الفيلم

### إضافة مسلسل جديد
1. اذهب إلى **المسلسلات > إضافة جديد**
2. أدخل عنوان المسلسل
3. أضف وصف المسلسل
4. ارفع صورة مميزة (بوستر)
5. املأ تفاصيل المسلسل:
   - التقييم (من 10)
   - سنة الإنتاج
   - عدد المواسم
   - عدد الحلقات
   - حالة المسلسل
   - رابط الإعلان
   - رابط المشاهدة
6. اختر التصنيف والبلد
7. انشر المسلسل

## 🎨 التخصيص

### الألوان
يمكنك تغيير الألوان من خلال تعديل متغيرات CSS في `style.css`:

```css
:root {
    --primary-color: #dc2626;    /* اللون الأساسي */
    --secondary-color: #1e293b;  /* اللون الثانوي */
    --accent-color: #f59e0b;     /* لون التمييز */
}
```

### الخطوط
القالب يستخدم خط Cairo من Google Fonts. يمكنك تغييره في `functions.php`:

```php
wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=YourFont&display=swap');
```

### الشعار
1. اذهب إلى **المظهر > تخصيص > هوية الموقع**
2. ارفع شعار مخصص
3. أو عدل النص في `header.php`

## 📁 هيكل الملفات

```
cinemahub-wordpress-theme/
├── style.css              # الأنماط الرئيسية
├── functions.php          # وظائف القالب
├── index.php             # الصفحة الرئيسية
├── header.php            # الهيدر
├── footer.php            # الفوتر
├── sidebar.php           # الشريط الجانبي
├── single.php            # صفحة المقال الواحد
├── archive.php           # صفحة الأرشيف
├── search.php            # صفحة البحث
├── 404.php              # صفحة الخطأ 404
├── js/
│   └── main.js          # JavaScript الرئيسي
├── template-parts/
│   ├── movie-card.php   # قالب كرت الفيلم
│   └── series-card.php  # قالب كرت المسلسل
└── README.md            # هذا الملف
```

## 🔧 الوظائف المتقدمة

### AJAX Load More
القالب يدعم تحميل المزيد من المحتوى بدون إعادة تحميل الصفحة.

### نظام المشاهدات
يتم حساب عدد مشاهدات كل مقال تلقائياً.

### مشاركة اجتماعية
أزرار مشاركة لجميع المنصات الاجتماعية الرئيسية.

### البحث المتقدم
بحث في العناوين والمحتوى والتصنيفات.

## 🎯 نصائح للاستخدام

### تحسين الأداء
1. استخدم إضافة تحسين الصور
2. فعل التخزين المؤقت
3. استخدم CDN للملفات الثابتة

### تحسين SEO
1. استخدم إضافة Yoast SEO
2. أضف أوصاف مناسبة للأفلام
3. استخدم كلمات مفتاحية مناسبة

### الأمان
1. حدث WordPress دائماً
2. استخدم إضافات الأمان
3. عمل نسخ احتياطية منتظمة

## 🐛 استكشاف الأخطاء

### المشاكل الشائعة

**القالب لا يظهر بشكل صحيح:**
- تأكد من رفع جميع الملفات
- تحقق من صلاحيات الملفات
- فعل قالب WordPress الافتراضي ثم ارجع للقالب

**الصور لا تظهر:**
- تحقق من مسارات الصور
- تأكد من رفع الصور بشكل صحيح
- تحقق من إعدادات الوسائط

**JavaScript لا يعمل:**
- تحقق من وجود أخطاء في وحدة التحكم
- تأكد من تحميل jQuery
- تحقق من تضارب الإضافات

## 📞 الدعم

للحصول على الدعم:
1. تحقق من هذا الدليل أولاً
2. ابحث في المشاكل الشائعة
3. تواصل مع فريق التطوير

## 📄 الترخيص

هذا القالب مرخص تحت رخصة GPL v2 أو أحدث.

## 🔄 التحديثات

### الإصدار 1.0
- إطلاق القالب الأولي
- جميع المميزات الأساسية
- دعم الأفلام والمسلسلات
- تصميم متجاوب كامل

---

**تم تطوير القالب بواسطة فريق CinemaHub Pro**

للمزيد من المعلومات، زر موقعنا الرسمي.
