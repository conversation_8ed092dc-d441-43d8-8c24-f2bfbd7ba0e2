<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html b:version='2' class='v2' expr:dir='data:blog.languageDirection' xmlns='http://www.w3.org/1999/xhtml' xmlns:b='http://www.google.com/2005/gml/b' xmlns:data='http://www.google.com/2005/gml/data' xmlns:expr='http://www.google.com/2005/gml/expr'>
<head>
    <meta charset='UTF-8'/>
    <meta content='width=device-width, initial-scale=1.0' name='viewport'/>
    
    <b:if cond='data:view.isHomepage'>
        <title>CinemaHub - سينما هاب | شاهد أحدث الأفلام والمسلسلات</title>
    <b:else/>
        <title><data:view.title/> - CinemaHub</title>
    </b:if>
    
    <meta content='موقعك الأول لمشاهدة أحدث الأفلام والمسلسلات' name='description'/>
    
    <!-- External CSS -->
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'/>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'/>
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&amp;display=swap' rel='stylesheet'/>
    
    <b:skin><![CDATA[
    body {
        font-family: 'Cairo', sans-serif;
        background: #141414;
        color: #ffffff;
        direction: rtl;
    }
    
    .header {
        background: rgba(20, 20, 20, 0.95);
        backdrop-filter: blur(10px);
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 1000;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .navbar { padding: 0.5rem 0; }
    
    .navbar-brand {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: #ffffff !important;
    }
    
    .logo-icon {
        width: 45px;
        height: 45px;
        background: linear-gradient(135deg, #e50914 0%, #b20710 100%);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 12px;
        position: relative;
        animation: logoGlow 3s ease-in-out infinite alternate;
    }
    
    .logo-icon .fa-film {
        font-size: 20px;
        color: #ffffff;
    }
    
    .logo-play {
        position: absolute;
        top: -5px;
        right: -5px;
        font-size: 16px;
        color: #ffd700;
        animation: playPulse 2s ease-in-out infinite;
    }
    
    .brand-text {
        font-size: 18px;
        font-weight: 700;
        background: linear-gradient(135deg, #e50914 0%, #b20710 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .brand-subtitle {
        font-size: 12px;
        color: #757575;
        margin-top: -2px;
    }
    
    @keyframes logoGlow {
        0% { box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3); }
        100% { box-shadow: 0 6px 25px rgba(229, 9, 20, 0.6); }
    }
    
    @keyframes playPulse {
        0%, 100% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.2); opacity: 0.8; }
    }
    
    .main-content {
        margin-top: 80px;
        padding: 2rem 0;
        min-height: 100vh;
    }
    
    .welcome-section {
        text-align: center;
        padding: 3rem 0;
        background: linear-gradient(135deg, #141414 0%, #000000 100%);
        border-radius: 8px;
        margin-bottom: 2rem;
    }
    
    .welcome-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, #e50914 0%, #b20710 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .welcome-subtitle {
        font-size: 1.2rem;
        color: #757575;
        margin-bottom: 2rem;
    }
    
    .blog-posts {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 2rem;
        margin: 2rem 0;
    }
    
    .post-card {
        background: #221f1f;
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .post-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(229, 9, 20, 0.2);
        border-color: #e50914;
    }
    
    .post-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
    }
    
    .post-placeholder {
        width: 100%;
        height: 200px;
        background: linear-gradient(135deg, #e50914 0%, #b20710 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ffffff;
        font-size: 3rem;
    }
    
    .post-content { padding: 1.5rem; }
    
    .post-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #ffffff;
    }
    
    .post-title a {
        color: inherit;
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .post-title a:hover { color: #e50914; }
    
    .post-meta {
        font-size: 12px;
        color: #757575;
        margin-bottom: 1rem;
    }
    
    .post-meta i { color: #e50914; }
    
    .post-snippet {
        font-size: 14px;
        color: #757575;
        line-height: 1.6;
    }
    
    .footer {
        background: linear-gradient(135deg, #141414 0%, #000000 100%);
        padding: 2rem 0;
        text-align: center;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .footer p {
        color: #757575;
        font-size: 14px;
        margin: 0;
    }
    
    @media (max-width: 768px) {
        .logo-icon { width: 35px; height: 35px; }
        .logo-icon .fa-film { font-size: 16px; }
        .brand-text { font-size: 16px; }
        .blog-posts { grid-template-columns: 1fr; gap: 1rem; }
        .welcome-title { font-size: 2rem; }
        .welcome-subtitle { font-size: 1rem; }
    }
    
    @media (max-width: 576px) {
        .logo-icon { width: 30px; height: 30px; }
        .logo-icon .fa-film { font-size: 14px; }
        .brand-text { font-size: 14px; }
        .brand-subtitle { display: none; }
        .welcome-title { font-size: 1.5rem; }
    }
    ]]></b:skin>
</head>

<body>
    <!-- Header -->
    <header class='header'>
        <nav class='navbar'>
            <div class='container'>
                <a class='navbar-brand' expr:href='data:blog.homepageUrl'>
                    <div class='logo-icon'>
                        <i class='fas fa-film'></i>
                        <i class='fas fa-play-circle logo-play'></i>
                    </div>
                    <div>
                        <div class='brand-text'>CinemaHub</div>
                        <div class='brand-subtitle'>سينما هاب</div>
                    </div>
                </a>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class='main-content'>
        <div class='container'>
            <!-- Welcome Section -->
            <b:if cond='data:view.isHomepage'>
                <section class='welcome-section'>
                    <h1 class='welcome-title'>مرحباً بك في CinemaHub</h1>
                    <p class='welcome-subtitle'>موقعك الأول لمشاهدة أحدث الأفلام والمسلسلات</p>
                    <div class='d-flex justify-content-center gap-3'>
                        <i class='fas fa-film' style='color: #e50914; font-size: 2rem;'></i>
                        <i class='fas fa-tv' style='color: #e50914; font-size: 2rem;'></i>
                        <i class='fas fa-star' style='color: #ffd700; font-size: 2rem;'></i>
                    </div>
                </section>
            </b:if>
            
            <!-- Blog Posts -->
            <b:section class='main' id='main' maxwidgets='1' showaddelement='no'>
                <b:widget id='Blog1' locked='true' title='مشاركات المدونة' type='Blog' version='1' visible='true'>
                    <b:includable id='main'>
                        <div class='blog-posts'>
                            <b:loop values='data:posts' var='post'>
                                <article class='post-card'>
                                    <b:if cond='data:post.featuredImage'>
                                        <img class='post-image' expr:alt='data:post.title' expr:src='data:post.featuredImage'/>
                                    <b:else/>
                                        <div class='post-placeholder'>
                                            <i class='fas fa-film'></i>
                                        </div>
                                    </b:if>
                                    
                                    <div class='post-content'>
                                        <h2 class='post-title'>
                                            <a expr:href='data:post.link' expr:title='data:post.title'>
                                                <data:post.title/>
                                            </a>
                                        </h2>
                                        <div class='post-meta'>
                                            <i class='fas fa-calendar'></i> <data:post.dateHeader/>
                                            | <i class='fas fa-user'></i> <data:post.author/>
                                        </div>
                                        <div class='post-snippet'>
                                            <data:post.snippet/>
                                        </div>
                                    </div>
                                </article>
                            </b:loop>
                        </div>
                        
                        <!-- Pagination -->
                        <div class='text-center mt-5'>
                            <b:include name='nextprev'/>
                        </div>
                    </b:includable>
                </b:widget>
            </b:section>
        </div>
    </main>

    <!-- Footer -->
    <footer class='footer'>
        <div class='container'>
            <p>© 2024 CinemaHub - سينما هاب. جميع الحقوق محفوظة.</p>
            <p>موقعك الأول لمشاهدة أحدث الأفلام والمسلسلات</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
    
    <script>
    //<![CDATA[
    // Header scroll effect
    window.addEventListener('scroll', function() {
        const header = document.querySelector('.header');
        if (window.scrollY > 100) {
            header.style.background = 'rgba(20, 20, 20, 0.98)';
        } else {
            header.style.background = 'rgba(20, 20, 20, 0.95)';
        }
    });
    //]]>
    </script>
</body>
</html>
