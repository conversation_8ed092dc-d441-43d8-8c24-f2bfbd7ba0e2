/*
Theme Name: CinemaHub Pro
Description: قالب WordPress احترافي لمواقع الأفلام والمسلسلات
Version: 1.0
Author: CinemaHub Team
Text Domain: cinemahub-pro
*/

/* ===== CSS Variables ===== */
:root {
    /* ===== Professional Color Palette ===== */
    --primary-color: #dc2626;
    --primary-dark: #b91c1c;
    --primary-light: #ef4444;
    --primary-hover: #f87171;
    --primary-50: #fef2f2;
    --primary-100: #fee2e2;
    --primary-500: #ef4444;
    --primary-600: #dc2626;
    --primary-700: #b91c1c;
    --primary-800: #991b1b;
    --primary-900: #7f1d1d;

    --secondary-color: #1e293b;
    --secondary-dark: #0f172a;
    --secondary-light: #334155;
    --accent-color: #f59e0b;
    --accent-light: #fbbf24;
    --accent-dark: #d97706;

    --dark-color: #0f172a;
    --darker-color: #020617;
    --light-color: #ffffff;
    --surface-color: #1e293b;
    --surface-light: #334155;

    /* ===== Gray Scale ===== */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* ===== Semantic Colors ===== */
    --success-color: #059669;
    --success-light: #10b981;
    --success-dark: #047857;
    --warning-color: #d97706;
    --warning-light: #f59e0b;
    --warning-dark: #b45309;
    --danger-color: #dc2626;
    --danger-light: #ef4444;
    --danger-dark: #b91c1c;
    --info-color: #0ea5e9;
    --info-light: #38bdf8;
    --info-dark: #0284c7;
    
    /* ===== Professional Gradients ===== */
    --gradient-primary: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
    --gradient-primary-light: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
    --gradient-dark: linear-gradient(135deg, var(--dark-color) 0%, var(--darker-color) 100%);
    --gradient-surface: linear-gradient(135deg, var(--surface-color) 0%, var(--surface-light) 100%);
    --gradient-overlay: linear-gradient(180deg, rgba(15,23,42,0) 0%, rgba(15,23,42,0.95) 100%);
    --gradient-hero: linear-gradient(45deg, rgba(220,38,38,0.15) 0%, rgba(15,23,42,0.85) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
    
    --font-family: 'Cairo', sans-serif;
    --font-size-xs: 0.65rem;
    --font-size-sm: 0.75rem;
    --font-size-base: 0.85rem;
    --font-size-lg: 0.95rem;
    --font-size-xl: 1.05rem;
    --font-size-2xl: 1.25rem;
    --font-size-3xl: 1.5rem;
    --font-size-4xl: 1.75rem;
    --font-size-5xl: 2.25rem;
    
    --border-radius-sm: 0.375rem;
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-2xl: 1.5rem;
    --border-radius-3xl: 2rem;
    
    /* ===== Professional Shadows ===== */
    --shadow-sm: 0 1px 2px 0 rgba(15, 23, 42, 0.05);
    --shadow: 0 1px 3px 0 rgba(15, 23, 42, 0.1), 0 1px 2px 0 rgba(15, 23, 42, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(15, 23, 42, 0.1), 0 2px 4px -1px rgba(15, 23, 42, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(15, 23, 42, 0.15), 0 4px 6px -2px rgba(15, 23, 42, 0.08);
    --shadow-xl: 0 20px 25px -5px rgba(15, 23, 42, 0.2), 0 10px 10px -5px rgba(15, 23, 42, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(15, 23, 42, 0.35);
    --shadow-glow: 0 0 20px rgba(220, 38, 38, 0.25);
    --shadow-glow-accent: 0 0 20px rgba(245, 158, 11, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(15, 23, 42, 0.06);
    
    --transition-fast: all 0.15s ease;
    --transition: all 0.3s ease;
    --transition-slow: all 0.5s ease;
    
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal: 1050;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

/* ===== Base Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family);
    background: var(--dark-color);
    color: var(--light-color);
    line-height: 1.6;
    direction: rtl;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== Typography ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 0.5rem;
}

h1 { font-size: var(--font-size-4xl); font-weight: 700; }
h2 { font-size: var(--font-size-3xl); font-weight: 600; }
h3 { font-size: var(--font-size-2xl); font-weight: 600; }
h4 { font-size: var(--font-size-xl); font-weight: 500; }
h5 { font-size: var(--font-size-lg); font-weight: 500; }
h6 { font-size: var(--font-size-base); font-weight: 500; }

p {
    margin-bottom: 1rem;
    color: var(--gray-300);
    font-weight: 400;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-light);
}

/* ===== Loading Screen ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-dark);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    animation: fadeInUp 0.8s ease;
}

.loading-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.loading-text {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.loading-text .brand-text {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    letter-spacing: -0.02em;
}

.loading-text .brand-subtitle {
    font-size: var(--font-size-xl);
    color: var(--gray-400);
    font-weight: 500;
}

.loading-text .brand-tagline {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    font-weight: 400;
    margin-top: 0.25rem;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(229, 9, 20, 0.3);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 2rem;
}

.loading-content p {
    color: var(--gray-400);
    font-size: var(--font-size-lg);
    margin: 0;
    font-weight: 500;
}

/* ===== Animations ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes logoShine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(229, 9, 20, 0.7);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 20px rgba(229, 9, 20, 0);
    }
}

@keyframes playPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.3);
        opacity: 0.7;
    }
}

/* ===== MODERN HEADER STYLES ===== */
.modern-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(148, 163, 184, 0.1);
    z-index: 1000;
    transition: all 0.3s ease;
    padding: 0.6rem 0;
}

.modern-header.scrolled {
    background: rgba(15, 23, 42, 0.98);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid rgba(220, 38, 38, 0.2);
    padding: 0.4rem 0;
}

.header-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

/* Logo Section */
.header-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    color: #ffffff;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.header-logo:hover {
    color: #ffffff;
    transform: scale(1.02);
}

.logo-icon {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.3rem;
    font-weight: bold;
    box-shadow: 0 4px 15px rgba(220, 38, 38, 0.4);
    position: relative;
    overflow: hidden;
}

.logo-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.15), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

.header-logo:hover .logo-icon::before {
    opacity: 1;
    animation: logoShine 0.8s ease;
}

.logo-text h1 {
    font-size: 1.5rem;
    font-weight: 800;
    margin: 0;
    color: #ffffff;
    letter-spacing: -0.5px;
}

.logo-text p {
    font-size: 0.7rem;
    color: #dc2626;
    margin: -2px 0 0 0;
    font-weight: 500;
}

/* Navigation Menu */
.header-nav {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
    justify-content: center;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    padding: 0.5rem 0.8rem;
    color: #ffffff;
    text-decoration: none;
    font-size: 0.85rem;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.nav-link:hover {
    color: #ffffff;
    background: rgba(220, 38, 38, 0.15);
    transform: translateY(-2px);
}

.nav-link.active {
    background: var(--gradient-primary);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
}

/* Dropdown Menu */
.dropdown-menu {
    background: rgba(15, 23, 42, 0.98);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    min-width: 200px;
}

.dropdown-item {
    color: #ffffff;
    padding: 0.6rem 1rem;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: right;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dropdown-item:hover {
    background: rgba(220, 38, 38, 0.15);
    color: #ffffff;
    transform: translateX(-5px);
}

.dropdown-item i {
    width: 16px;
    text-align: center;
    color: var(--primary-color);
}

/* Categories Section */
.categories-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, var(--darker-color) 0%, var(--dark-color) 100%);
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--light-color);
    margin: 0;
}

.section-title i {
    color: var(--primary-color);
    font-size: 1.8rem;
}

.view-all-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: rgba(148, 163, 184, 0.1);
    color: var(--gray-300);
    text-decoration: none;
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 500;
    transition: var(--transition);
    border: 1px solid rgba(148, 163, 184, 0.2);
}

.view-all-btn:hover {
    color: var(--light-color);
    background: var(--gradient-primary);
    transform: translateX(-5px) scale(1.05);
    box-shadow: var(--shadow-lg);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.category-card {
    background: var(--gradient-surface);
    border-radius: var(--border-radius-2xl);
    padding: 2rem 1.5rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    border: 2px solid rgba(148, 163, 184, 0.1);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(220, 38, 38, 0.1), transparent);
    transition: left 0.6s ease;
}

.category-card:hover::before {
    left: 100%;
}

.category-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-2xl), var(--shadow-glow);
    border-color: var(--primary-600);
}

.category-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: var(--transition);
    box-shadow: var(--shadow-lg);
}

.category-card:hover .category-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-glow);
}

.category-icon i {
    font-size: 2rem;
    color: var(--light-color);
}

.category-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--light-color);
    margin-bottom: 0.5rem;
}

.category-count {
    font-size: var(--font-size-sm);
    color: var(--gray-400);
    font-weight: 500;
}

/* Movies Grid */
.movies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.movie-card {
    background: var(--surface-color);
    border-radius: var(--border-radius-2xl);
    overflow: hidden;
    transition: var(--transition);
    cursor: pointer;
    border: 2px solid rgba(148, 163, 184, 0.1);
    position: relative;
    box-shadow: var(--shadow-lg);
}

.movie-card:hover {
    transform: translateY(-15px) scale(1.03);
    box-shadow: var(--shadow-2xl), var(--shadow-glow);
    border-color: var(--primary-600);
}

.movie-poster {
    position: relative;
    height: 350px;
    overflow: hidden;
}

.movie-poster img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.movie-card:hover .movie-poster img {
    transform: scale(1.1);
}

.movie-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-overlay);
    opacity: 0;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.movie-card:hover .movie-overlay {
    opacity: 1;
}

.play-btn {
    width: 70px;
    height: 70px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--light-color);
    font-size: 1.5rem;
    transition: var(--transition);
    box-shadow: var(--shadow-lg);
    animation: pulse 2s infinite;
}

.play-btn:hover {
    transform: scale(1.2);
    animation: playPulse 0.6s ease;
}

.movie-info {
    padding: 1.5rem;
}

.movie-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--light-color);
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.movie-title a {
    color: var(--light-color);
    text-decoration: none;
    transition: var(--transition);
}

.movie-title a:hover {
    color: var(--primary-color);
}

.movie-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    font-size: var(--font-size-xs);
    color: var(--gray-400);
    background: rgba(148, 163, 184, 0.1);
    padding: 0.3rem 0.6rem;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
}

.meta-item i {
    color: var(--primary-color);
}

.movie-rating {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    color: var(--accent-color);
    font-weight: 600;
    font-size: var(--font-size-sm);
}

/* Footer */
.footer {
    background: var(--gradient-dark);
    padding: 3rem 0 1.5rem;
    margin-top: 3rem;
    border-top: 2px solid var(--primary-600);
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--gradient-primary);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    color: var(--light-color);
    margin-bottom: 1rem;
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.footer-section p,
.footer-section a {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
    line-height: 1.6;
    transition: var(--transition);
}

.footer-section a:hover {
    color: var(--primary-color);
    transform: translateX(-3px);
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.3rem 0;
}

.footer-links i {
    width: 16px;
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--gray-800);
}

.footer-bottom p {
    color: var(--gray-500);
    font-size: var(--font-size-sm);
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-container {
        padding: 0 1rem;
        gap: 1rem;
    }

    .logo-text h1 {
        font-size: 1.2rem;
    }

    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 1.2rem;
    }

    .category-card {
        padding: 1.5rem 1.2rem;
    }

    .movies-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .movie-poster {
        height: 250px;
    }

    .section-title {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.4rem;
    }

    .section-header {
        flex-direction: column;
        gap: 0.8rem;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .categories-grid {
        grid-template-columns: 1fr;
    }

    .movies-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.6rem;
    }

    .movie-poster {
        height: 200px;
    }

    .movie-info {
        padding: 0.8rem;
    }

    .category-card {
        padding: 1.2rem 0.8rem;
    }

    .category-icon {
        width: 50px;
        height: 50px;
    }

    .category-icon i {
        font-size: 1.5rem;
    }

    .category-title {
        font-size: 1rem;
    }

    .category-count {
        font-size: 0.75rem;
    }
}
