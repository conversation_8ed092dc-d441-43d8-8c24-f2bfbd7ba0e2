# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Production builds
dist/
build/
*.min.js
*.min.css
bundle.js
bundle.css

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# API Keys (keep these secret!)
config-production.js
api-keys.js
secrets.js

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Image optimization output
assets/images/optimized/
assets/images/compressed/

# Backup files
*.backup
*.bak
*.orig

# Test files
test-results/
coverage/
*.test.js.snap

# Lighthouse reports
lighthouse-report.html
lighthouse-report.json

# PWA asset generator output
pwa-assets/

# Service worker cache
sw-cache/

# Analytics and tracking
analytics/
tracking/

# User data (if stored locally)
user-data/
favorites.json
watch-history.json

# Development files
dev-notes.md
todo.md
.development

# Deployment files
deploy.sh
deploy.bat
.deploy/

# Database files (if using local storage)
*.db
*.sqlite
*.sqlite3

# Compressed files
*.zip
*.rar
*.7z
*.tar.gz

# Video files (if any test videos)
*.mp4
*.avi
*.mov
*.wmv
*.flv

# Large image files
*.psd
*.ai
*.sketch

# Documentation build
docs/_build/
docs/build/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Custom ignores for this project
config-local.js
local-settings.js
private/
sensitive/
