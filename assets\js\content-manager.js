// ===== Content Management System =====

class ContentManager {
    constructor() {
        this.apiKey = API_KEY;
        this.baseUrl = BASE_URL;
        this.imageBaseUrl = IMAGE_BASE_URL;
        this.backdropBaseUrl = BACKDROP_BASE_URL;
        this.cache = new Map();
        this.cacheExpiry = 30 * 60 * 1000; // 30 minutes
    }

    // ===== Cache Management =====
    setCache(key, data) {
        this.cache.set(key, {
            data: data,
            timestamp: Date.now()
        });
    }

    getCache(key) {
        const cached = this.cache.get(key);
        if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
            return cached.data;
        }
        this.cache.delete(key);
        return null;
    }

    // ===== API Requests =====
    async makeRequest(endpoint, params = {}) {
        const cacheKey = `${endpoint}_${JSON.stringify(params)}`;
        const cached = this.getCache(cacheKey);
        
        if (cached) {
            return cached;
        }

        try {
            const url = new URL(`${this.baseUrl}${endpoint}`);
            url.searchParams.append('api_key', this.apiKey);
            url.searchParams.append('language', 'ar');
            
            Object.keys(params).forEach(key => {
                url.searchParams.append(key, params[key]);
            });

            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            this.setCache(cacheKey, data);
            return data;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    // ===== Content Fetching Methods =====
    async getPopularMovies(page = 1) {
        return await this.makeRequest('/movie/popular', { page });
    }

    async getNowPlayingMovies(page = 1) {
        return await this.makeRequest('/movie/now_playing', { page });
    }

    async getTopRatedMovies(page = 1) {
        return await this.makeRequest('/movie/top_rated', { page });
    }

    async getUpcomingMovies(page = 1) {
        return await this.makeRequest('/movie/upcoming', { page });
    }

    async getPopularTVShows(page = 1) {
        return await this.makeRequest('/tv/popular', { page });
    }

    async getOnTheAirTVShows(page = 1) {
        return await this.makeRequest('/tv/on_the_air', { page });
    }

    async getTopRatedTVShows(page = 1) {
        return await this.makeRequest('/tv/top_rated', { page });
    }

    async getMovieDetails(id) {
        return await this.makeRequest(`/movie/${id}`);
    }

    async getTVShowDetails(id) {
        return await this.makeRequest(`/tv/${id}`);
    }

    async getMovieCredits(id) {
        return await this.makeRequest(`/movie/${id}/credits`);
    }

    async getTVShowCredits(id) {
        return await this.makeRequest(`/tv/${id}/credits`);
    }

    async getMovieVideos(id) {
        return await this.makeRequest(`/movie/${id}/videos`);
    }

    async getTVShowVideos(id) {
        return await this.makeRequest(`/tv/${id}/videos`);
    }

    async getSimilarMovies(id, page = 1) {
        return await this.makeRequest(`/movie/${id}/similar`, { page });
    }

    async getSimilarTVShows(id, page = 1) {
        return await this.makeRequest(`/tv/${id}/similar`, { page });
    }

    async searchMulti(query, page = 1) {
        return await this.makeRequest('/search/multi', { query, page });
    }

    async searchMovies(query, page = 1) {
        return await this.makeRequest('/search/movie', { query, page });
    }

    async searchTVShows(query, page = 1) {
        return await this.makeRequest('/search/tv', { query, page });
    }

    async getGenres(type = 'movie') {
        return await this.makeRequest(`/genre/${type}/list`);
    }

    async discoverMovies(params = {}) {
        return await this.makeRequest('/discover/movie', params);
    }

    async discoverTVShows(params = {}) {
        return await this.makeRequest('/discover/tv', params);
    }

    // ===== Advanced Filtering =====
    async getMoviesByGenre(genreId, page = 1) {
        return await this.discoverMovies({ with_genres: genreId, page });
    }

    async getTVShowsByGenre(genreId, page = 1) {
        return await this.discoverTVShows({ with_genres: genreId, page });
    }

    async getMoviesByYear(year, page = 1) {
        return await this.discoverMovies({ year, page });
    }

    async getTVShowsByYear(year, page = 1) {
        return await this.discoverTVShows({ first_air_date_year: year, page });
    }

    async getMoviesByRating(minRating, page = 1) {
        return await this.discoverMovies({ 'vote_average.gte': minRating, page });
    }

    async getTVShowsByRating(minRating, page = 1) {
        return await this.discoverTVShows({ 'vote_average.gte': minRating, page });
    }

    // ===== Content Processing =====
    processMovieData(movie) {
        return {
            id: movie.id,
            title: movie.title,
            originalTitle: movie.original_title,
            overview: movie.overview,
            releaseDate: movie.release_date,
            year: movie.release_date ? movie.release_date.split('-')[0] : null,
            rating: movie.vote_average,
            voteCount: movie.vote_count,
            popularity: movie.popularity,
            posterPath: movie.poster_path ? this.imageBaseUrl + movie.poster_path : null,
            backdropPath: movie.backdrop_path ? this.backdropBaseUrl + movie.backdrop_path : null,
            genreIds: movie.genre_ids,
            adult: movie.adult,
            originalLanguage: movie.original_language,
            type: 'movie'
        };
    }

    processTVShowData(tvShow) {
        return {
            id: tvShow.id,
            name: tvShow.name,
            originalName: tvShow.original_name,
            overview: tvShow.overview,
            firstAirDate: tvShow.first_air_date,
            year: tvShow.first_air_date ? tvShow.first_air_date.split('-')[0] : null,
            rating: tvShow.vote_average,
            voteCount: tvShow.vote_count,
            popularity: tvShow.popularity,
            posterPath: tvShow.poster_path ? this.imageBaseUrl + tvShow.poster_path : null,
            backdropPath: tvShow.backdrop_path ? this.backdropBaseUrl + tvShow.backdrop_path : null,
            genreIds: tvShow.genre_ids,
            originalLanguage: tvShow.original_language,
            originCountry: tvShow.origin_country,
            type: 'tv'
        };
    }

    // ===== Local Storage Management =====
    saveToLocalStorage(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify({
                data: data,
                timestamp: Date.now()
            }));
        } catch (error) {
            console.error('Error saving to localStorage:', error);
        }
    }

    getFromLocalStorage(key, maxAge = 24 * 60 * 60 * 1000) { // 24 hours default
        try {
            const stored = localStorage.getItem(key);
            if (!stored) return null;

            const parsed = JSON.parse(stored);
            if (Date.now() - parsed.timestamp > maxAge) {
                localStorage.removeItem(key);
                return null;
            }

            return parsed.data;
        } catch (error) {
            console.error('Error reading from localStorage:', error);
            return null;
        }
    }

    // ===== Favorites Management =====
    addToFavorites(item) {
        const favorites = this.getFavorites();
        const exists = favorites.some(fav => fav.id === item.id && fav.type === item.type);
        
        if (!exists) {
            favorites.push({
                ...item,
                addedAt: new Date().toISOString()
            });
            this.saveToLocalStorage('favorites', favorites);
            return true;
        }
        return false;
    }

    removeFromFavorites(id, type) {
        const favorites = this.getFavorites();
        const filtered = favorites.filter(fav => !(fav.id === id && fav.type === type));
        this.saveToLocalStorage('favorites', filtered);
        return filtered.length < favorites.length;
    }

    getFavorites() {
        return this.getFromLocalStorage('favorites') || [];
    }

    isFavorite(id, type) {
        const favorites = this.getFavorites();
        return favorites.some(fav => fav.id === id && fav.type === type);
    }

    // ===== Watch History =====
    addToWatchHistory(item) {
        const history = this.getWatchHistory();
        const existingIndex = history.findIndex(h => h.id === item.id && h.type === item.type);
        
        if (existingIndex !== -1) {
            // Update existing entry
            history[existingIndex] = {
                ...history[existingIndex],
                lastWatched: new Date().toISOString(),
                watchCount: (history[existingIndex].watchCount || 1) + 1
            };
        } else {
            // Add new entry
            history.unshift({
                ...item,
                firstWatched: new Date().toISOString(),
                lastWatched: new Date().toISOString(),
                watchCount: 1
            });
        }

        // Keep only last 100 items
        if (history.length > 100) {
            history.splice(100);
        }

        this.saveToLocalStorage('watchHistory', history);
    }

    getWatchHistory() {
        return this.getFromLocalStorage('watchHistory') || [];
    }

    clearWatchHistory() {
        localStorage.removeItem('watchHistory');
    }

    // ===== Recommendations =====
    async getPersonalizedRecommendations() {
        const favorites = this.getFavorites();
        const history = this.getWatchHistory();
        
        if (favorites.length === 0 && history.length === 0) {
            // Return popular content if no user data
            const [movies, tvShows] = await Promise.all([
                this.getPopularMovies(),
                this.getPopularTVShows()
            ]);
            
            return [...movies.results.slice(0, 10), ...tvShows.results.slice(0, 10)];
        }

        // Get genres from user's favorites and history
        const userGenres = new Set();
        [...favorites, ...history].forEach(item => {
            if (item.genreIds) {
                item.genreIds.forEach(genreId => userGenres.add(genreId));
            }
        });

        // Get recommendations based on user's preferred genres
        const genreArray = Array.from(userGenres);
        const recommendations = [];

        for (const genreId of genreArray.slice(0, 3)) { // Top 3 genres
            try {
                const [movies, tvShows] = await Promise.all([
                    this.getMoviesByGenre(genreId),
                    this.getTVShowsByGenre(genreId)
                ]);
                
                recommendations.push(...movies.results.slice(0, 5));
                recommendations.push(...tvShows.results.slice(0, 5));
            } catch (error) {
                console.error('Error getting recommendations for genre:', genreId, error);
            }
        }

        // Remove duplicates and items already in favorites/history
        const userItemIds = new Set([...favorites, ...history].map(item => `${item.id}_${item.type}`));
        const uniqueRecommendations = recommendations.filter(item => {
            const itemKey = `${item.id}_${item.type || (item.title ? 'movie' : 'tv')}`;
            return !userItemIds.has(itemKey);
        });

        return uniqueRecommendations.slice(0, 20);
    }
}

// Create global instance
const contentManager = new ContentManager();
