<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الفيلم - CinemaHub</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .movie-details {
            padding: 6rem 0 4rem;
            min-height: 100vh;
        }
        
        .movie-hero {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 3rem;
            margin-bottom: 4rem;
        }
        
        .movie-poster-large {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(220, 38, 38, 0.3);
        }
        
        .movie-poster-large img {
            width: 100%;
            height: 450px;
            object-fit: cover;
        }
        
        .movie-info-detailed {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        
        .movie-title {
            font-size: 2.5rem;
            color: #dc2626;
            margin-bottom: 1rem;
        }
        
        .movie-meta-detailed {
            display: flex;
            flex-wrap: wrap;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #cccccc;
        }
        
        .meta-item i {
            color: #dc2626;
        }
        
        .movie-description {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #e5e5e5;
            margin-bottom: 2rem;
        }
        
        .action-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .btn-large {
            padding: 1rem 2rem;
            font-size: 1.1rem;
            border-radius: 30px;
        }
        
        .movie-sections {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 3rem;
        }
        
        .section-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(220, 38, 38, 0.3);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .section-title {
            color: #dc2626;
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .cast-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 1rem;
        }
        
        .cast-member {
            text-align: center;
        }
        
        .cast-photo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 0.5rem;
            border: 2px solid #dc2626;
        }
        
        .cast-name {
            font-size: 0.9rem;
            color: #ffffff;
        }
        
        .cast-role {
            font-size: 0.8rem;
            color: #cccccc;
        }
        
        .related-movies {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }
        
        .related-movie {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .related-movie:hover {
            transform: translateY(-5px);
            border: 1px solid #dc2626;
        }
        
        .related-movie img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .related-movie-info {
            padding: 1rem;
        }
        
        .related-movie h4 {
            font-size: 0.9rem;
            color: #ffffff;
            margin-bottom: 0.5rem;
        }
        
        .related-movie .rating {
            color: #fbbf24;
            font-size: 0.8rem;
        }
        
        @media (max-width: 768px) {
            .movie-hero {
                grid-template-columns: 1fr;
                text-align: center;
            }
            
            .movie-sections {
                grid-template-columns: 1fr;
            }
            
            .movie-title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1><i class="fas fa-film"></i> CinemaHub</h1>
                </div>
                <nav class="nav">
                    <a href="index.html" class="nav-link">الرئيسية</a>
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link">الأفلام <i class="fas fa-chevron-down"></i></a>
                        <div class="dropdown-menu">
                            <a href="#">أفلام عربية</a>
                            <a href="#">أفلام أجنبية</a>
                            <a href="#">أفلام هندية</a>
                            <a href="#">أفلام تركية</a>
                        </div>
                    </div>
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link">المسلسلات <i class="fas fa-chevron-down"></i></a>
                        <div class="dropdown-menu">
                            <a href="#">مسلسلات عربية</a>
                            <a href="#">مسلسلات أجنبية</a>
                            <a href="#">مسلسلات تركية</a>
                            <a href="#">مسلسلات هندية</a>
                        </div>
                    </div>
                    <a href="#" class="nav-link">الأحدث</a>
                </nav>
                <div class="auth-buttons">
                    <button class="btn btn-outline">تسجيل دخول</button>
                    <button class="btn btn-primary">إنشاء حساب</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Movie Details -->
    <section class="movie-details">
        <div class="container">
            <!-- Movie Hero Section -->
            <div class="movie-hero">
                <div class="movie-poster-large">
                    <img src="https://via.placeholder.com/300x450/dc2626/ffffff?text=ملصق+الفيلم" alt="ملصق الفيلم">
                </div>
                <div class="movie-info-detailed">
                    <h1 class="movie-title">عنوان الفيلم المميز</h1>
                    <div class="movie-meta-detailed">
                        <div class="meta-item">
                            <i class="fas fa-calendar"></i>
                            <span>2024</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-clock"></i>
                            <span>120 دقيقة</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-star"></i>
                            <span>8.5/10</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-tag"></i>
                            <span>دراما، أكشن</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-globe"></i>
                            <span>عربي</span>
                        </div>
                    </div>
                    <p class="movie-description">
                        هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة، لقد تم توليد هذا النص من مولد النص العربى، حيث يمكنك أن تولد مثل هذا النص أو العديد من النصوص الأخرى إضافة إلى زيادة عدد الحروف التى يولدها التطبيق. إذا كنت تحتاج إلى عدد أكبر من الفقرات يتيح لك مولد النص العربى زيادة عدد الفقرات كما تريد.
                    </p>
                    <div class="action-buttons">
                        <button class="btn btn-primary btn-large">
                            <i class="fas fa-play"></i> مشاهدة الآن
                        </button>
                        <button class="btn btn-outline btn-large">
                            <i class="fas fa-download"></i> تحميل
                        </button>
                        <button class="btn btn-outline">
                            <i class="fas fa-heart"></i> إضافة للمفضلة
                        </button>
                        <button class="btn btn-outline">
                            <i class="fas fa-share"></i> مشاركة
                        </button>
                    </div>
                </div>
            </div>

            <!-- Movie Sections -->
            <div class="movie-sections">
                <div class="main-content">
                    <!-- Cast Section -->
                    <div class="section-card">
                        <h3 class="section-title">
                            <i class="fas fa-users"></i>
                            طاقم التمثيل
                        </h3>
                        <div class="cast-grid">
                            <div class="cast-member">
                                <img src="https://via.placeholder.com/80x80/dc2626/ffffff?text=ممثل" alt="ممثل" class="cast-photo">
                                <div class="cast-name">اسم الممثل</div>
                                <div class="cast-role">البطل</div>
                            </div>
                            <div class="cast-member">
                                <img src="https://via.placeholder.com/80x80/dc2626/ffffff?text=ممثلة" alt="ممثلة" class="cast-photo">
                                <div class="cast-name">اسم الممثلة</div>
                                <div class="cast-role">البطلة</div>
                            </div>
                            <div class="cast-member">
                                <img src="https://via.placeholder.com/80x80/dc2626/ffffff?text=مخرج" alt="مخرج" class="cast-photo">
                                <div class="cast-name">اسم المخرج</div>
                                <div class="cast-role">المخرج</div>
                            </div>
                            <div class="cast-member">
                                <img src="https://via.placeholder.com/80x80/dc2626/ffffff?text=منتج" alt="منتج" class="cast-photo">
                                <div class="cast-name">اسم المنتج</div>
                                <div class="cast-role">المنتج</div>
                            </div>
                        </div>
                    </div>

                    <!-- Trailer Section -->
                    <div class="section-card">
                        <h3 class="section-title">
                            <i class="fas fa-video"></i>
                            الإعلان التشويقي
                        </h3>
                        <div style="background: #000; height: 300px; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: #dc2626;">
                            <i class="fas fa-play" style="font-size: 3rem;"></i>
                        </div>
                    </div>
                </div>

                <div class="sidebar">
                    <!-- Movie Info -->
                    <div class="section-card">
                        <h3 class="section-title">
                            <i class="fas fa-info-circle"></i>
                            معلومات إضافية
                        </h3>
                        <div style="display: flex; flex-direction: column; gap: 1rem;">
                            <div><strong>المخرج:</strong> اسم المخرج</div>
                            <div><strong>الكاتب:</strong> اسم الكاتب</div>
                            <div><strong>الإنتاج:</strong> شركة الإنتاج</div>
                            <div><strong>البلد:</strong> مصر</div>
                            <div><strong>الميزانية:</strong> 5 مليون دولار</div>
                            <div><strong>الإيرادات:</strong> 20 مليون دولار</div>
                        </div>
                    </div>

                    <!-- Related Movies -->
                    <div class="section-card">
                        <h3 class="section-title">
                            <i class="fas fa-film"></i>
                            أفلام مشابهة
                        </h3>
                        <div class="related-movies">
                            <div class="related-movie">
                                <img src="https://via.placeholder.com/150x200/dc2626/ffffff?text=فيلم+1" alt="فيلم مشابه">
                                <div class="related-movie-info">
                                    <h4>فيلم مشابه 1</h4>
                                    <div class="rating">
                                        <i class="fas fa-star"></i> 8.2
                                    </div>
                                </div>
                            </div>
                            <div class="related-movie">
                                <img src="https://via.placeholder.com/150x200/dc2626/ffffff?text=فيلم+2" alt="فيلم مشابه">
                                <div class="related-movie-info">
                                    <h4>فيلم مشابه 2</h4>
                                    <div class="rating">
                                        <i class="fas fa-star"></i> 7.9
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>CinemaHub</h3>
                    <p>موقعك المفضل لمشاهدة الأفلام والمسلسلات العربية والأجنبية</p>
                </div>
                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="#">الأفلام</a></li>
                        <li><a href="#">المسلسلات</a></li>
                        <li><a href="#">الأحدث</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>تواصل معنا</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 CinemaHub. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
