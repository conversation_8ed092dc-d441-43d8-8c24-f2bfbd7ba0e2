// ===== Service Worker for Movie Website =====

const CACHE_NAME = 'movie-site-v1.0.0';
const STATIC_CACHE = 'static-v1.0.0';
const DYNAMIC_CACHE = 'dynamic-v1.0.0';
const API_CACHE = 'api-v1.0.0';

// Files to cache immediately
const STATIC_FILES = [
    '/',
    '/index.html',
    '/search.html',
    '/assets/css/style.css',
    '/assets/js/main.js',
    '/assets/js/content-manager.js',
    '/assets/js/movie-modal.js',
    '/assets/js/search.js',
    '/assets/images/logo.png',
    '/assets/images/favicon.ico',
    '/assets/images/no-poster.jpg',
    // External CDN files (fallback)
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
    'https://unpkg.com/aos@2.3.1/dist/aos.css',
    'https://unpkg.com/aos@2.3.1/dist/aos.js',
    'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap'
];

// API endpoints to cache
const API_ENDPOINTS = [
    'https://api.themoviedb.org/3/movie/popular',
    'https://api.themoviedb.org/3/movie/now_playing',
    'https://api.themoviedb.org/3/movie/top_rated',
    'https://api.themoviedb.org/3/tv/popular',
    'https://api.themoviedb.org/3/tv/on_the_air',
    'https://api.themoviedb.org/3/tv/top_rated'
];

// Install event - cache static files
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        Promise.all([
            caches.open(STATIC_CACHE).then(cache => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_FILES);
            }),
            caches.open(API_CACHE).then(cache => {
                console.log('Service Worker: Pre-caching API endpoints');
                // Pre-cache some API endpoints with dummy requests
                return Promise.allSettled(
                    API_ENDPOINTS.map(endpoint => {
                        const url = new URL(endpoint);
                        url.searchParams.append('api_key', 'dummy'); // Will be replaced with real key
                        url.searchParams.append('language', 'ar');
                        return fetch(url).then(response => {
                            if (response.ok) {
                                return cache.put(endpoint, response);
                            }
                        }).catch(() => {
                            // Ignore errors during pre-caching
                        });
                    })
                );
            })
        ]).then(() => {
            console.log('Service Worker: Installation complete');
            return self.skipWaiting();
        })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName !== STATIC_CACHE && 
                        cacheName !== DYNAMIC_CACHE && 
                        cacheName !== API_CACHE) {
                        console.log('Service Worker: Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            console.log('Service Worker: Activation complete');
            return self.clients.claim();
        })
    );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);

    // Handle different types of requests
    if (request.method === 'GET') {
        if (isStaticFile(request.url)) {
            event.respondWith(handleStaticFile(request));
        } else if (isAPIRequest(request.url)) {
            event.respondWith(handleAPIRequest(request));
        } else if (isImageRequest(request.url)) {
            event.respondWith(handleImageRequest(request));
        } else {
            event.respondWith(handleDynamicRequest(request));
        }
    }
});

// Check if request is for static file
function isStaticFile(url) {
    return STATIC_FILES.some(file => url.includes(file)) ||
           url.includes('.css') ||
           url.includes('.js') ||
           url.includes('.woff') ||
           url.includes('.woff2') ||
           url.includes('fonts.googleapis.com') ||
           url.includes('fonts.gstatic.com');
}

// Check if request is for API
function isAPIRequest(url) {
    return url.includes('api.themoviedb.org');
}

// Check if request is for image
function isImageRequest(url) {
    return url.includes('image.tmdb.org') ||
           url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i);
}

// Handle static files - cache first strategy
async function handleStaticFile(request) {
    try {
        const cache = await caches.open(STATIC_CACHE);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            // Return cached version and update in background
            updateCacheInBackground(request, cache);
            return cachedResponse;
        }
        
        // If not in cache, fetch and cache
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
        
    } catch (error) {
        console.log('Service Worker: Static file fetch failed:', error);
        
        // Return offline fallback for HTML pages
        if (request.headers.get('accept').includes('text/html')) {
            return caches.match('/offline.html') || new Response(
                '<h1>Offline</h1><p>You are currently offline. Please check your internet connection.</p>',
                { headers: { 'Content-Type': 'text/html' } }
            );
        }
        
        throw error;
    }
}

// Handle API requests - network first with cache fallback
async function handleAPIRequest(request) {
    try {
        const cache = await caches.open(API_CACHE);
        
        // Try network first
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // Cache successful responses
            cache.put(request, networkResponse.clone());
            return networkResponse;
        }
        
        // If network fails, try cache
        const cachedResponse = await cache.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        return networkResponse;
        
    } catch (error) {
        console.log('Service Worker: API request failed:', error);
        
        // Try to return cached version
        const cache = await caches.open(API_CACHE);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return error response
        return new Response(
            JSON.stringify({ 
                error: 'Network unavailable', 
                message: 'Unable to fetch data. Please check your internet connection.' 
            }),
            { 
                status: 503,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
}

// Handle image requests - cache first with network fallback
async function handleImageRequest(request) {
    try {
        const cache = await caches.open(DYNAMIC_CACHE);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // Cache images for future use
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
        
    } catch (error) {
        console.log('Service Worker: Image fetch failed:', error);
        
        // Return placeholder image
        return caches.match('/assets/images/no-poster.jpg') || 
               new Response('', { status: 404 });
    }
}

// Handle dynamic requests - network first
async function handleDynamicRequest(request) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
        
    } catch (error) {
        console.log('Service Worker: Dynamic request failed:', error);
        
        // Try cache
        const cache = await caches.open(DYNAMIC_CACHE);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return offline page for HTML requests
        if (request.headers.get('accept').includes('text/html')) {
            return caches.match('/offline.html') || new Response(
                '<h1>Offline</h1><p>This page is not available offline.</p>',
                { headers: { 'Content-Type': 'text/html' } }
            );
        }
        
        throw error;
    }
}

// Update cache in background
async function updateCacheInBackground(request, cache) {
    try {
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            cache.put(request, networkResponse);
        }
    } catch (error) {
        // Ignore background update errors
        console.log('Service Worker: Background update failed:', error);
    }
}

// Handle background sync
self.addEventListener('sync', event => {
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

async function doBackgroundSync() {
    try {
        // Sync any pending data
        console.log('Service Worker: Background sync triggered');
        
        // You can add logic here to sync offline actions
        // like favorites, watch history, etc.
        
    } catch (error) {
        console.log('Service Worker: Background sync failed:', error);
    }
}

// Handle push notifications
self.addEventListener('push', event => {
    if (event.data) {
        const data = event.data.json();
        
        const options = {
            body: data.body,
            icon: '/assets/images/icon-192x192.png',
            badge: '/assets/images/badge-72x72.png',
            vibrate: [100, 50, 100],
            data: {
                dateOfArrival: Date.now(),
                primaryKey: data.primaryKey || 1
            },
            actions: [
                {
                    action: 'explore',
                    title: 'مشاهدة الآن',
                    icon: '/assets/images/checkmark.png'
                },
                {
                    action: 'close',
                    title: 'إغلاق',
                    icon: '/assets/images/xmark.png'
                }
            ]
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );
    }
});

// Handle notification clicks
self.addEventListener('notificationclick', event => {
    event.notification.close();
    
    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

// Periodic background sync (if supported)
self.addEventListener('periodicsync', event => {
    if (event.tag === 'content-sync') {
        event.waitUntil(syncContent());
    }
});

async function syncContent() {
    try {
        // Sync latest content in background
        console.log('Service Worker: Periodic sync triggered');
        
        const cache = await caches.open(API_CACHE);
        
        // Update popular movies and TV shows
        const endpoints = [
            'https://api.themoviedb.org/3/movie/popular',
            'https://api.themoviedb.org/3/tv/popular'
        ];
        
        await Promise.allSettled(
            endpoints.map(async endpoint => {
                try {
                    const response = await fetch(endpoint);
                    if (response.ok) {
                        await cache.put(endpoint, response);
                    }
                } catch (error) {
                    console.log('Service Worker: Failed to sync:', endpoint);
                }
            })
        );
        
    } catch (error) {
        console.log('Service Worker: Content sync failed:', error);
    }
}

// Clean up old cache entries periodically
setInterval(async () => {
    try {
        const cache = await caches.open(DYNAMIC_CACHE);
        const requests = await cache.keys();
        
        // Remove entries older than 7 days
        const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
        
        await Promise.all(
            requests.map(async request => {
                const response = await cache.match(request);
                if (response) {
                    const dateHeader = response.headers.get('date');
                    if (dateHeader && new Date(dateHeader).getTime() < oneWeekAgo) {
                        await cache.delete(request);
                    }
                }
            })
        );
        
    } catch (error) {
        console.log('Service Worker: Cache cleanup failed:', error);
    }
}, 24 * 60 * 60 * 1000); // Run daily
