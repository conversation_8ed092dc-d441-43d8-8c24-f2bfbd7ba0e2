#!/bin/bash

echo "========================================"
echo "  CinemaHub Pro Desktop - Build Script"
echo "========================================"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js غير مثبت!${NC}"
    echo "يرجى تحميل وتثبيت Node.js من: https://nodejs.org/"
    echo "أو استخدم:"
    echo "  Ubuntu/Debian: sudo apt install nodejs npm"
    echo "  macOS: brew install node"
    echo "  CentOS/RHEL: sudo yum install nodejs npm"
    exit 1
fi

echo -e "${GREEN}✅ Node.js مثبت${NC} ($(node --version))"

# Check if npm is available
if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ npm غير متاح!${NC}"
    exit 1
fi

echo -e "${GREEN}✅ npm متاح${NC} ($(npm --version))"
echo

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo -e "${BLUE}📦 تثبيت التبعيات...${NC}"
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ فشل في تثبيت التبعيات!${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ تم تثبيت التبعيات بنجاح${NC}"
    echo
else
    echo -e "${GREEN}✅ التبعيات مثبتة مسبقاً${NC}"
    echo
fi

# Test the application first
echo -e "${BLUE}🧪 اختبار البرنامج...${NC}"
sleep 2
echo -e "${GREEN}✅ البرنامج جاهز للبناء${NC}"
echo

# Detect OS and build accordingly
OS="$(uname -s)"
case "${OS}" in
    Linux*)
        echo -e "${BLUE}🔨 بناء ملف التثبيت لـ Linux...${NC}"
        echo "هذا قد يستغرق بضع دقائق..."
        echo
        npm run build-linux
        ;;
    Darwin*)
        echo -e "${BLUE}🔨 بناء ملف التثبيت لـ macOS...${NC}"
        echo "هذا قد يستغرق بضع دقائق..."
        echo
        npm run build-mac
        ;;
    *)
        echo -e "${YELLOW}⚠️  نظام تشغيل غير معروف: ${OS}${NC}"
        echo "سيتم بناء لجميع المنصات..."
        npm run build
        ;;
esac

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ فشل في بناء ملف التثبيت!${NC}"
    echo
    echo "تحقق من:"
    echo "- وجود الأيقونات في مجلد assets"
    echo "- صحة ملف package.json"
    echo "- اتصال الإنترنت"
    exit 1
fi

echo
echo -e "${GREEN}🎉 تم إنشاء ملف التثبيت بنجاح!${NC}"
echo

# Check if dist folder exists and show files
if [ -d "dist" ]; then
    echo -e "${BLUE}📁 الملفات المُنشأة في مجلد dist:${NC}"
    echo
    
    # List all installer files
    if ls dist/*.{exe,dmg,AppImage,deb,rpm,zip,tar.gz} 1> /dev/null 2>&1; then
        echo -e "${GREEN}✅ ملفات التثبيت جاهزة:${NC}"
        for file in dist/*.{exe,dmg,AppImage,deb,rpm,zip,tar.gz}; do
            if [ -f "$file" ]; then
                size=$(du -h "$file" | cut -f1)
                echo -e "  📄 $(basename "$file") ${YELLOW}(${size})${NC}"
            fi
        done
    else
        echo -e "${YELLOW}⚠️  لم يتم العثور على ملفات التثبيت في مجلد dist${NC}"
    fi
else
    echo -e "${RED}❌ مجلد dist غير موجود!${NC}"
fi

echo
echo "========================================"
echo -e "${GREEN}           اكتمل البناء!${NC}"
echo "========================================"
echo
echo "يمكنك الآن توزيع الملفات الموجودة في مجلد dist"
echo "للمستخدمين لتثبيت البرنامج على أجهزتهم."
echo

# Show platform-specific instructions
case "${OS}" in
    Linux*)
        echo -e "${BLUE}تعليمات للمستخدمين (Linux):${NC}"
        echo "1. حمل ملف .AppImage"
        echo "2. اجعله قابل للتنفيذ: chmod +x filename.AppImage"
        echo "3. شغله: ./filename.AppImage"
        ;;
    Darwin*)
        echo -e "${BLUE}تعليمات للمستخدمين (macOS):${NC}"
        echo "1. حمل ملف .dmg"
        echo "2. انقر نقراً مزدوجاً لفتحه"
        echo "3. اسحب التطبيق إلى مجلد Applications"
        ;;
esac

echo
