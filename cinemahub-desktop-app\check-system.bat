@echo off
chcp 65001 >nul
title CinemaHub Pro - System Check

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║              CinemaHub Pro - فحص النظام                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص متطلبات النظام...
echo.

REM Check Node.js
echo 1️⃣ Node.js:
node --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('node --version') do echo   ✅ مثبت - الإصدار: %%i
) else (
    echo   ❌ غير مثبت
    echo   📥 حمل من: https://nodejs.org/
)

echo.

REM Check npm
echo 2️⃣ npm:
npm --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('npm --version') do echo   ✅ مثبت - الإصدار: %%i
) else (
    echo   ❌ غير مثبت
)

echo.

REM Check current directory
echo 3️⃣ مجلد العمل:
echo   📂 %cd%

echo.

REM Check package.json
echo 4️⃣ ملف package.json:
if exist "package.json" (
    echo   ✅ موجود
) else (
    echo   ❌ غير موجود
    echo   ⚠️  تأكد من أنك في المجلد الصحيح
)

echo.

REM Check icon
echo 5️⃣ الأيقونة:
if exist "assets\icon.png" (
    echo   ✅ موجودة في assets\icon.png
) else (
    echo   ❌ غير موجودة
    echo   📋 افتح assets\download-icon.html لإنشائها
)

echo.

REM Check node_modules
echo 6️⃣ التبعيات:
if exist "node_modules" (
    echo   ✅ مثبتة
) else (
    echo   ❌ غير مثبتة
    echo   📦 شغل: npm install
)

echo.

REM Check internet connection
echo 7️⃣ اتصال الإنترنت:
ping google.com -n 1 >nul 2>&1
if %errorlevel% equ 0 (
    echo   ✅ متصل
) else (
    echo   ❌ غير متصل
    echo   🌐 تأكد من اتصال الإنترنت
)

echo.

REM Check disk space
echo 8️⃣ مساحة القرص:
for /f "tokens=3" %%a in ('dir /-c ^| find "bytes free"') do (
    echo   💾 المساحة المتاحة: %%a bytes
)

echo.

REM Summary
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        الملخص                               ║
echo ╚══════════════════════════════════════════════════════════════╝

set /a issues=0

node --version >nul 2>&1
if %errorlevel% neq 0 set /a issues+=1

npm --version >nul 2>&1
if %errorlevel% neq 0 set /a issues+=1

if not exist "package.json" set /a issues+=1
if not exist "assets\icon.png" set /a issues+=1

if %issues% equ 0 (
    echo.
    echo ✅ النظام جاهز لبناء البرنامج!
    echo.
    echo 🚀 يمكنك الآن تشغيل:
    echo    - create-installer.bat
    echo    - أو build-simple.bat
) else (
    echo.
    echo ❌ يوجد %issues% مشاكل تحتاج إصلاح
    echo.
    echo 📋 يرجى إصلاح المشاكل المذكورة أعلاه أولاً
)

echo.
echo اضغط أي زر للخروج...
pause >nul
