// ===== Global Variables =====
const API_KEY = CONFIG?.API?.TMDB_API_KEY || 'YOUR_TMDB_API_KEY'; // Replace with your TMDB API key
const BASE_URL = CONFIG?.API?.BASE_URL || 'https://api.themoviedb.org/3';
const IMAGE_BASE_URL = CONFIG?.API?.IMAGE_BASE_URL || 'https://image.tmdb.org/t/p/w500';
const BACKDROP_BASE_URL = CONFIG?.API?.BACKDROP_BASE_URL || 'https://image.tmdb.org/t/p/w1280';

// ===== DOM Elements =====
const loadingScreen = document.getElementById('loading-screen');
const backToTopBtn = document.getElementById('backToTop');
const searchInput = document.querySelector('.search-input');
const searchForm = document.querySelector('.search-form');

// ===== Initialize App =====
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Hide loading screen
    setTimeout(() => {
        loadingScreen.style.opacity = '0';
        setTimeout(() => {
            loadingScreen.style.display = 'none';
        }, 500);
    }, 1500);

    // Check API key and show demo notice
    if (!API_KEY || API_KEY === 'YOUR_TMDB_API_KEY') {
        showDemoNotice();
    }

    // Initialize components
    initializeHeader();
    initializeHeroSlider();
    initializeBackToTop();
    initializeSearch();
    initializeLazyLoading();
    initializeAOS();
    loadSavedFontSize();

    // Load content
    loadLatestMovies();
    loadLatestSeries();
    loadTopRated();
    loadHeroContent();
}

// ===== Demo Notice =====
function showDemoNotice() {
    const notice = document.createElement('div');
    notice.className = 'demo-notice';
    notice.innerHTML = `
        <div class="demo-content">
            <i class="fas fa-info-circle"></i>
            <div class="demo-text">
                <h4>مرحباً بك في CinemaHub التجريبي!</h4>
                <p>هذا عرض تجريبي للموقع مع بيانات وهمية. للحصول على البيانات الحقيقية، يرجى إضافة مفتاح TMDB API في ملف config.js</p>
            </div>
            <button class="demo-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    document.body.appendChild(notice);

    // Auto-hide after 10 seconds
    setTimeout(() => {
        if (notice.parentElement) {
            notice.remove();
        }
    }, 10000);
}

// ===== Responsive Text Management =====
function initializeResponsiveText() {
    // تحسين حجم الخط حسب حجم الشاشة
    function adjustTextSize() {
        const screenWidth = window.innerWidth;
        const body = document.body;

        // إزالة الفئات السابقة
        body.classList.remove('compact-mode', 'large-text', 'medium-text');

        if (screenWidth <= 480) {
            body.classList.add('compact-mode');
        } else if (screenWidth <= 768) {
            // الحجم الافتراضي للتابلت
        } else if (screenWidth >= 1200) {
            body.classList.add('large-text');
        }
    }

    // تطبيق التحسين عند التحميل وتغيير حجم الشاشة
    adjustTextSize();
    window.addEventListener('resize', adjustTextSize);

    // تحسين المسافات حسب المحتوى
    optimizeSpacing();
}

function optimizeSpacing() {
    // تحسين المسافات بين العناصر
    const movieCards = document.querySelectorAll('.movie-card');
    const screenWidth = window.innerWidth;

    movieCards.forEach(card => {
        if (screenWidth <= 480) {
            card.classList.add('compact-card');
        } else {
            card.classList.remove('compact-card');
        }
    });
}

// ===== Header Functions =====
function initializeHeader() {
    const header = document.querySelector('.header');

    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });
}

// ===== Search Functions =====
function initializeSearch() {
    const searchForm = document.querySelector('.search-form');
    const searchInput = document.querySelector('.search-input');

    if (searchForm && searchInput) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const query = searchInput.value.trim();
            if (query.length >= 3) {
                performSearch(query);
            }
        });

        // Real-time search suggestions
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            if (query.length >= 3) {
                // Implement search suggestions here
                console.log('Searching for:', query);
            }
        });
    }
}

function performSearch(query) {
    // Redirect to search page or implement search functionality
    window.location.href = `search.html?q=${encodeURIComponent(query)}`;
}

// ===== Hero Slider =====
function initializeHeroSlider() {
    const heroSlider = new Swiper('.hero-slider', {
        loop: true,
        autoplay: {
            delay: 5000,
            disableOnInteraction: false,
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        effect: 'fade',
        fadeEffect: {
            crossFade: true
        }
    });
}

// ===== Back to Top Button =====
function initializeBackToTop() {
    window.addEventListener('scroll', () => {
        if (window.scrollY > 300) {
            backToTopBtn.classList.add('show');
        } else {
            backToTopBtn.classList.remove('show');
        }
    });

    backToTopBtn.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// ===== Search Functions =====
function initializeSearch() {
    let searchTimeout;
    
    searchInput.addEventListener('input', (e) => {
        clearTimeout(searchTimeout);
        const query = e.target.value.trim();
        
        if (query.length > 2) {
            searchTimeout = setTimeout(() => {
                performSearch(query);
            }, 500);
        }
    });

    searchForm.addEventListener('submit', (e) => {
        e.preventDefault();
        const query = searchInput.value.trim();
        if (query) {
            window.location.href = `search.html?q=${encodeURIComponent(query)}`;
        }
    });
}

async function performSearch(query) {
    try {
        const response = await fetch(`${BASE_URL}/search/multi?api_key=${API_KEY}&query=${encodeURIComponent(query)}&language=ar`);
        const data = await response.json();
        
        // Display search suggestions (implement dropdown)
        displaySearchSuggestions(data.results.slice(0, 5));
    } catch (error) {
        console.error('Search error:', error);
    }
}

function displaySearchSuggestions(results) {
    // Create and display search suggestions dropdown
    let suggestionsContainer = document.querySelector('.search-suggestions');
    
    if (!suggestionsContainer) {
        suggestionsContainer = document.createElement('div');
        suggestionsContainer.className = 'search-suggestions';
        document.querySelector('.search-container').appendChild(suggestionsContainer);
    }

    suggestionsContainer.innerHTML = '';
    
    results.forEach(item => {
        const suggestion = document.createElement('div');
        suggestion.className = 'search-suggestion';
        suggestion.innerHTML = `
            <img src="${item.poster_path ? IMAGE_BASE_URL + item.poster_path : 'assets/images/no-poster.jpg'}" alt="${item.title || item.name}">
            <div class="suggestion-info">
                <h6>${item.title || item.name}</h6>
                <span>${item.media_type === 'movie' ? 'فيلم' : 'مسلسل'} - ${item.release_date ? item.release_date.split('-')[0] : item.first_air_date ? item.first_air_date.split('-')[0] : 'غير محدد'}</span>
            </div>
        `;
        
        suggestion.addEventListener('click', () => {
            window.location.href = `${item.media_type}.html?id=${item.id}`;
        });
        
        suggestionsContainer.appendChild(suggestion);
    });
    
    suggestionsContainer.style.display = results.length > 0 ? 'block' : 'none';
}

// ===== Sample Data =====
const sampleMovies = [
    {
        id: 1,
        title: "أفاتار: طريق الماء",
        poster_path: "https://image.tmdb.org/t/p/w500/94xxm5701CzOdJdUEdIuwqZaowx.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/s16H6tpK2utvwDtzZ8Qy4qm5Emw.jpg",
        release_date: "2022-12-14",
        vote_average: 7.7,
        overview: "بعد أكثر من عقد من الأحداث الأولى، تعلم عائلة سولي (جيك ونيتيري وأطفالهم) المشاكل التي تتبعهم والأطوال التي سيذهبون إليها للحفاظ على سلامة بعضهم البعض."
    },
    {
        id: 2,
        title: "الرجل العنكبوت: لا طريق للعودة",
        poster_path: "https://image.tmdb.org/t/p/w500/1g0dhYtq4irTY1GPXvft6k4YLjm.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/14QbnygCuTO0vl7CAFmPf1fgZfV.jpg",
        release_date: "2021-12-15",
        vote_average: 8.4,
        overview: "بيتر باركر غير مقنع وغير قادر على فصل حياته الطبيعية عن المخاطر العالية لكونه بطلاً خارقاً."
    },
    {
        id: 3,
        title: "توب غان: مافريك",
        poster_path: "https://image.tmdb.org/t/p/w500/62HCnUTziyWcpDaBO2i1DX17ljH.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/odJ4hx6g6vBt4lBWKFD1tI8WS4x.jpg",
        release_date: "2022-05-24",
        vote_average: 8.3,
        overview: "بعد أكثر من ثلاثين عاماً من الخدمة كواحد من أفضل الطيارين البحريين، يواصل بيت مافريك ميتشل دفع المغلف كطيار اختبار شجاع."
    },
    {
        id: 4,
        title: "دكتور سترينج في الكون المتعدد للجنون",
        poster_path: "https://image.tmdb.org/t/p/w500/9Gtg2DzBhmYamXBS1hKAhiwbBKS.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/wcKFYIiVDvRURrzglV9kGu7fpfY.jpg",
        release_date: "2022-05-04",
        vote_average: 7.3,
        overview: "يسافر الدكتور ستيفن سترينج إلى الكون المتعدد ويواجه نسخة جديدة وخطيرة من نفسه."
    },
    {
        id: 5,
        title: "مينيونز: صعود جرو",
        poster_path: "https://image.tmdb.org/t/p/w500/wKiOkZTN9lUUUNZLmtnwubZYONg.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/26yQPXymbWeCLKwcmyL8dRjAzth.jpg",
        release_date: "2022-06-29",
        vote_average: 7.8,
        overview: "في السبعينيات، يكبر جرو في الضواحي ويصبح من أشد المعجبين بمجموعة من الأشرار الخارقين المعروفين باسم فيشوس 6."
    },
    {
        id: 6,
        title: "ثور: الحب والرعد",
        poster_path: "https://image.tmdb.org/t/p/w500/pIkRyD18kl4FhoCNQuWxWu5cBLM.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/jsoz1HlxczSuTx0mDl2h0lxy36l.jpg",
        release_date: "2022-07-06",
        vote_average: 6.8,
        overview: "يشرع ثور في رحلة لا مثيل لها - البحث عن السلام الداخلي. لكن تقاعده يتعطل بسبب قاتل مجرة يُعرف باسم جور إله الجزار."
    },
    {
        id: 7,
        title: "الوحوش الرائعة: أسرار دمبلدور",
        poster_path: "https://image.tmdb.org/t/p/w500/jrgifaYeUtTnaH7NF5Drkgjg2MB.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/zGLHX92Gk96O1DJvLil7ObJTbaL.jpg",
        release_date: "2022-04-06",
        vote_average: 6.9,
        overview: "يقود البروفيسور ألبوس دمبلدور فريقاً من السحرة والساحرات وخباز واحد في مهمة خطيرة."
    },
    {
        id: 8,
        title: "جوراسيك وورلد دومينيون",
        poster_path: "https://image.tmdb.org/t/p/w500/kAVRgw7GgK1CfYEJq8ME6EvRIgU.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/53BC9F2tpZnsGno2cLhzvGprDYS.jpg",
        release_date: "2022-06-08",
        vote_average: 7.0,
        overview: "بعد أربع سنوات من تدمير إيسلا نوبلار، تعيش الديناصورات الآن وتصطاد جنباً إلى جنب مع البشر في جميع أنحاء العالم."
    },
    // أفلام عربية
    {
        id: 9,
        title: "الفيل الأزرق 2",
        poster_path: "https://image.tmdb.org/t/p/w500/gzJnMEMkHowkUndn9gCr8ghQPzN.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/xJWPZIYOEFIjZpBL7SVBGnzRYXp.jpg",
        release_date: "2019-07-25",
        vote_average: 6.2,
        overview: "يواصل الدكتور يحيى رحلته في عالم الطب النفسي، ويواجه حالات جديدة ومعقدة تختبر قدراته المهنية والشخصية."
    },
    {
        id: 10,
        title: "كيرة والجن",
        poster_path: "https://image.tmdb.org/t/p/w500/aKx1ARwG55zZ0GpRvU2WrGrCG9o.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/zzWGRw277MNoCs3zhyG3YmYQsNy.jpg",
        release_date: "2022-01-13",
        vote_average: 5.8,
        overview: "في عالم مليء بالسحر والجن، تخوض كيرة مغامرة شيقة لإنقاذ عالمها من الشر الذي يهدده."
    },
    {
        id: 11,
        title: "الممر",
        poster_path: "https://image.tmdb.org/t/p/w500/lm3pQ2QoQ16pextRsmnUbG2onES.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/w2PMyoyLU22YvrGK3smVM9fW1jj.jpg",
        release_date: "2019-06-26",
        vote_average: 7.1,
        overview: "قصة حقيقية عن بطولة الجيش المصري في حرب أكتوبر 1973، وتضحيات الجنود في سبيل الوطن."
    },
    {
        id: 12,
        title: "122",
        poster_path: "https://image.tmdb.org/t/p/w500/vB8o2p4ETnrfiWEgVxHmHWP9yRl.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/hZkgoQYus5vegHoetLkCJzb17zJ.jpg",
        release_date: "2019-01-23",
        vote_average: 6.5,
        overview: "في إطار من التشويق والإثارة، تدور أحداث الفيلم حول مجموعة من الأشخاص المحاصرين في مصعد."
    }
];

const sampleSeries = [
    {
        id: 101,
        name: "بيت التنين",
        poster_path: "https://image.tmdb.org/t/p/w500/7QMsOTMUswlwxJP0rTTZfmz2tX2.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/t9XkeE7HzOsdQcDDDapDYh8Rrmt.jpg",
        first_air_date: "2022-08-21",
        vote_average: 8.5,
        overview: "تدور أحداث المسلسل قبل 200 عام من أحداث صراع العروش، ويحكي قصة بيت تارغاريان."
    },
    {
        id: 102,
        name: "حلقات القوة",
        poster_path: "https://image.tmdb.org/t/p/w500/mYLOqiStMxDK3fYZFirgrMt8z5d.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/1HdUgaifkNXnus2sECMqxJJQZHP.jpg",
        first_air_date: "2022-09-01",
        vote_average: 7.3,
        overview: "تدور أحداث المسلسل في الأرض الوسطى، آلاف السنين قبل أحداث الهوبيت وسيد الخواتم."
    },
    {
        id: 103,
        name: "أشياء غريبة",
        poster_path: "https://image.tmdb.org/t/p/w500/49WJfeN0moxb9IPfGn8AIqMGskD.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/56v2KjBlU4XaOv9rVYEQypROD7P.jpg",
        first_air_date: "2016-07-15",
        vote_average: 8.7,
        overview: "عندما يختفي صبي صغير، تكشف مدينته الصغيرة عن لغز يشمل تجارب سرية وقوى خارقة للطبيعة مرعبة."
    },
    {
        id: 104,
        name: "الدب",
        poster_path: "https://image.tmdb.org/t/p/w500/sHFlbKS3WLqMnp9t2ghADIJFnuQ.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/9KnIzPCv9XpWA0MqmwiKBZvV1Sj.jpg",
        first_air_date: "2022-06-23",
        vote_average: 8.3,
        overview: "طاهٍ شاب من شيكاغو يعود إلى مسقط رأسه لإدارة مطعم ساندويتش اللحم البقري الإيطالي."
    },
    {
        id: 105,
        name: "أوبي وان كينوبي",
        poster_path: "https://image.tmdb.org/t/p/w500/qJRB789ceLryrLvOKrZqLKr2CGf.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/qsKvZIGmziScZMlH17nZaWxHHfX.jpg",
        first_air_date: "2022-05-27",
        vote_average: 7.2,
        overview: "خلال عهد الإمبراطورية المجرية، يواجه أوبي وان كينوبي السابق في الجيداي أعظم هزيمة له."
    },
    {
        id: 106,
        name: "مون نايت",
        poster_path: "https://image.tmdb.org/t/p/w500/x6FWMThYnNyBdlWhZIeQOmlGZ5d.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/vdpE5pjJVql5aD6pnzRqlFmgxXf.jpg",
        first_air_date: "2022-03-30",
        vote_average: 7.9,
        overview: "ستيفن غرانت، موظف متجر هدايا لطيف ومتعلم، يعاني من فقدان الذاكرة وذكريات من حياة أخرى."
    },
    {
        id: 107,
        name: "يوفوريا",
        poster_path: "https://image.tmdb.org/t/p/w500/3Q0hd3heuWwDWpwcDkhQOA6TYWI.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/jtnfNzqZwN4E32FGGxx1YZaBWWf.jpg",
        first_air_date: "2019-06-16",
        vote_average: 8.4,
        overview: "مجموعة من طلاب المدارس الثانوية يتنقلون في الحب والصداقات في عالم من المخدرات والجنس والصدمات والوسائط الاجتماعية."
    },
    {
        id: 108,
        name: "ذا بويز",
        poster_path: "https://image.tmdb.org/t/p/w500/stTEycfG9928HYGEISBFaG1ngjM.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/mY7SeH4HFFxW1hiI6cWuwCRKptN.jpg",
        first_air_date: "2019-07-26",
        vote_average: 8.7,
        overview: "مجموعة من اليقظين يشرعون في البحث عن الأبطال الخارقين الفاسدين الذين يسيئون استخدام قواهم العظمى."
    },
    // مسلسلات عربية وتركية
    {
        id: 109,
        name: "جعفر العمدة",
        poster_path: "https://image.tmdb.org/t/p/w500/6XH7V5FuNH99qOcihTRtTU0wGBm.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/aq4Pwv5Xeuvj6HZKtxyd23RF6tG.jpg",
        first_air_date: "2023-03-10",
        vote_average: 8.1,
        overview: "مسلسل كوميدي اجتماعي يحكي قصة جعفر العمدة وحياته في القرية مع أهلها ومشاكلهم اليومية."
    },
    {
        id: 110,
        name: "نسل الأغراب",
        poster_path: "https://image.tmdb.org/t/p/w500/zra8NrzxaEeunRWJmUm3HZOL4sd.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/suopoADq0k8YHr9VB6eTWRrPQBW.jpg",
        first_air_date: "2023-04-01",
        vote_average: 7.9,
        overview: "مسلسل درامي يتناول قضايا اجتماعية معاصرة من خلال قصة عائلة تواجه تحديات الحياة الحديثة."
    },
    {
        id: 111,
        name: "قيامة عثمان",
        poster_path: "https://image.tmdb.org/t/p/w500/iIvQnZyzgx9TkbrOgcXx0p7aLiq.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/o86DbpburjxrqAzEDhXZcyE8pDb.jpg",
        first_air_date: "2019-11-20",
        vote_average: 8.6,
        overview: "مسلسل تاريخي تركي يحكي قصة عثمان بن أرطغرل، مؤسس الإمبراطورية العثمانية."
    },
    {
        id: 112,
        name: "المؤسس عثمان",
        poster_path: "https://image.tmdb.org/t/p/w500/nGJXdjXtKDL0rqTNe8iXkrSiIHm.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/1R6cvRtZgsYCkh8UFuWFN33xBP4.jpg",
        first_air_date: "2019-11-20",
        vote_average: 8.4,
        overview: "استكمال لقصة أرطغرل، يركز على ابنه عثمان وكيف أسس الإمبراطورية العثمانية العظيمة."
    },
    {
        id: 113,
        name: "حكايتي",
        poster_path: "https://image.tmdb.org/t/p/w500/36iqSQzLIrge4xijQhNp1DSFrvZ.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/mMtUybQ6hL24FXo0F3Z4j2KG7kZ.jpg",
        first_air_date: "2019-09-09",
        vote_average: 7.8,
        overview: "مسلسل تركي رومانسي يحكي قصة حب معقدة بين شاب وفتاة من خلفيات اجتماعية مختلفة."
    },
    {
        id: 114,
        name: "الطبيب المعجزة",
        poster_path: "https://image.tmdb.org/t/p/w500/6tfT03sGp9k4c0J3dypjrI8TSAI.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/mUkuc2wyV9dHLG0D0Loaw5pO2s8.jpg",
        first_air_date: "2019-09-12",
        vote_average: 8.2,
        overview: "مسلسل طبي تركي يحكي قصة طبيب شاب موهوب يعاني من متلازمة التوحد ومتلازمة العبقري."
    },
    {
        id: 115,
        name: "ابنتي",
        poster_path: "https://image.tmdb.org/t/p/w500/oOce4jR6OqQxjZ2sVBQSKZOGgbb.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/rAiKFaU7q6F7AqJPiWpkDlrfMgu.jpg",
        first_air_date: "2018-11-13",
        vote_average: 8.0,
        overview: "مسلسل تركي عائلي يحكي قصة أب يكتشف أن لديه ابنة لم يعرف بوجودها من قبل."
    },
    {
        id: 116,
        name: "لعبة القدر",
        poster_path: "https://image.tmdb.org/t/p/w500/vI8o7PjJVNhDr7M7o2bx6XMw6R2.jpg",
        backdrop_path: "https://image.tmdb.org/t/p/w1280/zGLHX92Gk96O1DJvLil7ObJTbaL.jpg",
        first_air_date: "2022-06-15",
        vote_average: 7.6,
        overview: "مسلسل درامي تركي يتناول قصة امرأة تحاول إعادة بناء حياتها بعد مأساة عائلية."
    }
];

// ===== Content Loading Functions =====
async function loadLatestMovies() {
    try {
        // محاولة جلب البيانات من API
        if (API_KEY && API_KEY !== 'YOUR_TMDB_API_KEY') {
            const response = await fetch(`${BASE_URL}/movie/now_playing?api_key=${API_KEY}&language=ar&page=1`);
            const data = await response.json();
            displayMovies(data.results.slice(0, 8), 'latest-movies');
        } else {
            // استخدام البيانات الوهمية
            displayMovies(sampleMovies, 'latest-movies');
        }
    } catch (error) {
        console.error('Error loading latest movies:', error);
        // استخدام البيانات الوهمية في حالة الخطأ
        displayMovies(sampleMovies, 'latest-movies');
    }
}

async function loadLatestSeries() {
    try {
        // محاولة جلب البيانات من API
        if (API_KEY && API_KEY !== 'YOUR_TMDB_API_KEY') {
            const response = await fetch(`${BASE_URL}/tv/on_the_air?api_key=${API_KEY}&language=ar&page=1`);
            const data = await response.json();
            displaySeries(data.results.slice(0, 8), 'latest-series');
        } else {
            // استخدام البيانات الوهمية
            displaySeries(sampleSeries, 'latest-series');
        }
    } catch (error) {
        console.error('Error loading latest series:', error);
        // استخدام البيانات الوهمية في حالة الخطأ
        displaySeries(sampleSeries, 'latest-series');
    }
}

async function loadTopRated() {
    try {
        // محاولة جلب البيانات من API
        if (API_KEY && API_KEY !== 'YOUR_TMDB_API_KEY') {
            const [moviesResponse, seriesResponse] = await Promise.all([
                fetch(`${BASE_URL}/movie/top_rated?api_key=${API_KEY}&language=ar&page=1`),
                fetch(`${BASE_URL}/tv/top_rated?api_key=${API_KEY}&language=ar&page=1`)
            ]);

            const moviesData = await moviesResponse.json();
            const seriesData = await seriesResponse.json();

            // Mix movies and series
            const mixed = [...moviesData.results.slice(0, 4), ...seriesData.results.slice(0, 4)];
            displayMixed(mixed, 'top-rated');
        } else {
            // استخدام البيانات الوهمية
            const mixed = [...sampleMovies.slice(0, 4), ...sampleSeries.slice(0, 4)];
            displayMixed(mixed, 'top-rated');
        }
    } catch (error) {
        console.error('Error loading top rated content:', error);
        // استخدام البيانات الوهمية في حالة الخطأ
        const mixed = [...sampleMovies.slice(0, 4), ...sampleSeries.slice(0, 4)];
        displayMixed(mixed, 'top-rated');
    }
}

async function loadHeroContent() {
    try {
        // محاولة جلب البيانات من API
        if (API_KEY && API_KEY !== 'YOUR_TMDB_API_KEY') {
            const response = await fetch(`${BASE_URL}/movie/popular?api_key=${API_KEY}&language=ar&page=1`);
            const data = await response.json();
            displayHeroSlides(data.results.slice(0, 5));
        } else {
            // استخدام البيانات الوهمية
            displayHeroSlides(sampleMovies.slice(0, 5));
        }
    } catch (error) {
        console.error('Error loading hero content:', error);
        // استخدام البيانات الوهمية في حالة الخطأ
        displayHeroSlides(sampleMovies.slice(0, 5));
    }
}

// ===== Display Functions =====
function displayMovies(movies, containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;

    container.innerHTML = '';
    
    movies.forEach(movie => {
        const movieCard = createMovieCard(movie, 'movie');
        container.appendChild(movieCard);
    });
}

function displaySeries(series, containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;

    container.innerHTML = '';
    
    series.forEach(show => {
        const seriesCard = createMovieCard(show, 'tv');
        container.appendChild(seriesCard);
    });
}

function displayMixed(items, containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;

    container.innerHTML = '';

    items.forEach((item, index) => {
        // تحديد النوع بناءً على وجود خصائص معينة
        const type = item.title ? 'movie' : 'tv';
        const card = createMovieCard(item, type);
        container.appendChild(card);
    });
}

function createMovieCard(item, type) {
    const card = document.createElement('div');
    card.className = 'movie-card';
    card.setAttribute('data-aos', 'fade-up');
    card.setAttribute('data-aos-delay', Math.random() * 200);

    const title = item.title || item.name || 'عنوان غير متوفر';
    const releaseDate = item.release_date || item.first_air_date;
    const year = releaseDate ? releaseDate.split('-')[0] : 'غير محدد';
    const rating = item.vote_average ? item.vote_average.toFixed(1) : 'N/A';

    // تحسين عرض الصور
    let posterPath;
    if (item.poster_path) {
        posterPath = item.poster_path.startsWith('http') ?
            item.poster_path :
            IMAGE_BASE_URL + item.poster_path;
    } else {
        posterPath = getPlaceholderUrl(title);
    }

    // تحديد نوع المحتوى للعرض
    const contentType = type === 'movie' ? 'فيلم' : 'مسلسل';
    const qualityBadge = type === 'movie' ? 'HD' : 'مسلسل';

    const fallbackImage = getPlaceholderUrl(title);

    card.innerHTML = `
        <div class="movie-poster">
            <img src="${posterPath}" alt="${title}" loading="lazy" onerror="this.src='${fallbackImage}'">
            <div class="quality-badge">${qualityBadge}</div>
            <div class="movie-overlay">
                <button class="play-btn" onclick="openMovieModal(${item.id}, '${type}')">
                    <i class="fas fa-play"></i>
                </button>
            </div>
        </div>
        <div class="movie-info">
            <h3 class="movie-title" title="${title}">${title}</h3>
            <div class="movie-meta">
                <span class="movie-year">${year}</span>
                <div class="movie-rating">
                    <i class="fas fa-star"></i>
                    <span>${rating}</span>
                </div>
            </div>
        </div>
    `;

    return card;
}

function displayHeroSlides(movies) {
    const swiperWrapper = document.querySelector('.swiper-wrapper');
    if (!swiperWrapper) return;

    swiperWrapper.innerHTML = '';

    movies.forEach(movie => {
        const slide = document.createElement('div');
        slide.className = 'swiper-slide hero-slide';

        // استخدام الصورة الخلفية من البيانات
        const backdropUrl = movie.backdrop_path ?
            (movie.backdrop_path.startsWith('http') ? movie.backdrop_path : BACKDROP_BASE_URL + movie.backdrop_path) :
            'https://via.placeholder.com/1280x720/141414/ffffff?text=No+Image';

        slide.style.backgroundImage = `url(${backdropUrl})`;

        const title = movie.title || movie.name || 'عنوان غير متوفر';
        const overview = movie.overview || 'لا يوجد وصف متاح لهذا العمل في الوقت الحالي.';

        slide.innerHTML = `
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">${title}</h1>
                    <p class="hero-description">${overview}</p>
                    <div class="hero-buttons">
                        <a href="#" class="btn-hero primary" onclick="openMovieModal(${movie.id}, 'movie')">
                            <i class="fas fa-play"></i>
                            مشاهدة الآن
                        </a>
                        <a href="#" class="btn-hero secondary">
                            <i class="fas fa-info-circle"></i>
                            المزيد من المعلومات
                        </a>
                    </div>
                </div>
            </div>
        `;

        swiperWrapper.appendChild(slide);
    });
}

// ===== Lazy Loading =====
function initializeLazyLoading() {
    const images = document.querySelectorAll('img[loading="lazy"]');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src || img.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    }
}

// ===== AOS Animation =====
function initializeAOS() {
    AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true,
        offset: 100
    });
}

// ===== Movie Modal Functions =====
function openMovieModal(id, type) {
    // This function will be implemented in the next part
    console.log(`Opening modal for ${type} with ID: ${id}`);
}

// ===== Utility Functions =====
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// ===== Image Utilities =====
function createPlaceholderImage(title, width = 500, height = 750) {
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');

    // Background gradient
    const gradient = ctx.createLinearGradient(0, 0, 0, height);
    gradient.addColorStop(0, '#221f1f');
    gradient.addColorStop(1, '#141414');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);

    // Border
    ctx.strokeStyle = '#e50914';
    ctx.lineWidth = 4;
    ctx.strokeRect(2, 2, width - 4, height - 4);

    // Film icon
    ctx.fillStyle = '#e50914';
    ctx.font = `${width * 0.2}px FontAwesome`;
    ctx.textAlign = 'center';
    ctx.fillText('🎬', width / 2, height * 0.4);

    // Title
    ctx.fillStyle = '#ffffff';
    ctx.font = `bold ${width * 0.06}px Cairo, Arial`;
    ctx.textAlign = 'center';

    // Word wrap for title
    const words = title.split(' ');
    const maxWidth = width * 0.8;
    let line = '';
    let y = height * 0.6;

    for (let n = 0; n < words.length; n++) {
        const testLine = line + words[n] + ' ';
        const metrics = ctx.measureText(testLine);
        const testWidth = metrics.width;

        if (testWidth > maxWidth && n > 0) {
            ctx.fillText(line, width / 2, y);
            line = words[n] + ' ';
            y += width * 0.08;
        } else {
            line = testLine;
        }
    }
    ctx.fillText(line, width / 2, y);

    return canvas.toDataURL();
}

function getPlaceholderUrl(title, width = 500, height = 750) {
    // Use a more attractive placeholder service
    const encodedTitle = encodeURIComponent(title);
    return `https://via.placeholder.com/${width}x${height}/221f1f/ffffff?text=${encodedTitle}`;
}

// ===== Error Handling =====
window.addEventListener('error', (e) => {
    console.error('Global error:', e.error);
});

// ===== Font Size Control =====
function changeFontSize(size) {
    const body = document.body;
    const buttons = document.querySelectorAll('.font-size-btn');

    // إزالة الفئات السابقة
    body.classList.remove('compact-mode', 'large-text', 'medium-text');
    buttons.forEach(btn => btn.classList.remove('active'));

    // إضافة الفئة الجديدة
    switch(size) {
        case 'small':
            body.classList.add('compact-mode');
            buttons[0].classList.add('active');
            break;
        case 'medium':
            body.classList.add('medium-text');
            buttons[1].classList.add('active');
            break;
        case 'large':
            body.classList.add('large-text');
            buttons[2].classList.add('active');
            break;
    }

    // حفظ الإعداد
    localStorage.setItem('fontSize', size);

    // إعادة تحسين المسافات
    optimizeSpacing();

    // إشعار المستخدم
    showNotification(`تم تغيير حجم الخط إلى ${size === 'small' ? 'صغير' : size === 'medium' ? 'متوسط' : 'كبير'}`, 'success');
}

// تحميل إعداد حجم الخط المحفوظ
function loadSavedFontSize() {
    const savedSize = localStorage.getItem('fontSize') || 'medium';
    changeFontSize(savedSize);
}

// ===== Optimize Spacing =====
function optimizeSpacing() {
    // تحسين المسافات بناءً على حجم الخط
    const body = document.body;
    const isCompact = body.classList.contains('compact-mode');
    const isLarge = body.classList.contains('large-text');

    // تطبيق تحسينات CSS ديناميكية
    if (isCompact) {
        document.documentElement.style.setProperty('--spacing-multiplier', '0.8');
    } else if (isLarge) {
        document.documentElement.style.setProperty('--spacing-multiplier', '1.2');
    } else {
        document.documentElement.style.setProperty('--spacing-multiplier', '1');
    }
}

// ===== Service Worker Registration =====
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}
