# دليل البناء - CinemaHub Pro Desktop

دليل شامل لبناء وتوزيع برنامج CinemaHub Pro Desktop.

## 📋 المتطلبات الأساسية

### البرامج المطلوبة
```bash
# Node.js (الإصدار 16 أو أحدث)
node --version  # يجب أن يكون >= 16.0.0

# npm (يأتي مع Node.js)
npm --version

# Git (للتحكم في الإصدارات)
git --version
```

### تثبيت المتطلبات
```bash
# تثبيت Node.js من الموقع الرسمي
# https://nodejs.org/

# أو باستخدام مدير الحزم
# Windows (Chocolatey)
choco install nodejs

# macOS (Homebrew)
brew install node

# Ubuntu/Debian
sudo apt update
sudo apt install nodejs npm
```

## 🚀 الإعداد الأولي

### 1. تحميل المشروع
```bash
# إذا كان لديك Git repository
git clone <repository-url>
cd cinemahub-desktop-app

# أو إذا كان لديك ملفات محلية
cd cinemahub-desktop-app
```

### 2. تثبيت التبعيات
```bash
# تثبيت جميع المتطلبات
npm install

# أو باستخدام Yarn (إذا كان مثبتاً)
yarn install
```

### 3. التحقق من التثبيت
```bash
# تشغيل البرنامج في وضع التطوير
npm run dev

# يجب أن يفتح البرنامج بنجاح
```

## 🔧 أوامر البناء

### التطوير
```bash
# تشغيل في وضع التطوير مع إعادة التحميل التلقائي
npm run dev

# تشغيل عادي
npm start
```

### البناء للإنتاج
```bash
# بناء لجميع المنصات
npm run build

# بناء لمنصة واحدة
npm run build-win    # Windows
npm run build-mac    # macOS
npm run build-linux  # Linux

# بناء محمول (بدون تثبيت)
npm run pack
```

### التوزيع
```bash
# إنشاء ملفات التوزيع
npm run dist

# نشر التحديثات (إذا كان مُعد)
npm run publish
```

## 📦 إعدادات البناء

### ملف package.json
```json
{
  "build": {
    "appId": "com.cinemahub.desktop",
    "productName": "CinemaHub Pro Desktop",
    "directories": {
      "output": "dist"
    },
    "files": [
      "**/*",
      "!**/node_modules/*/{CHANGELOG.md,README.md}",
      "!**/node_modules/*/{test,__tests__,tests}",
      "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp}",
      "!.editorconfig",
      "!**/._*",
      "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS}",
      "!**/{__pycache__,thumbs.db,.flowconfig,.idea}",
      "!**/{appveyor.yml,.travis.yml,circle.yml}",
      "!**/{npm-debug.log,yarn.lock,.yarn-integrity}"
    ]
  }
}
```

### إعدادات Windows
```json
{
  "win": {
    "target": [
      {
        "target": "nsis",
        "arch": ["x64", "ia32"]
      }
    ],
    "icon": "assets/icon.ico",
    "publisherName": "CinemaHub Team",
    "verifyUpdateCodeSignature": false
  },
  "nsis": {
    "oneClick": false,
    "allowToChangeInstallationDirectory": true,
    "createDesktopShortcut": true,
    "createStartMenuShortcut": true,
    "shortcutName": "CinemaHub Pro",
    "installerIcon": "assets/icon.ico",
    "uninstallerIcon": "assets/icon.ico"
  }
}
```

### إعدادات macOS
```json
{
  "mac": {
    "target": "dmg",
    "icon": "assets/icon.icns",
    "category": "public.app-category.entertainment",
    "hardenedRuntime": true,
    "entitlements": "assets/entitlements.mac.plist",
    "entitlementsInherit": "assets/entitlements.mac.plist"
  },
  "dmg": {
    "contents": [
      {
        "x": 130,
        "y": 220
      },
      {
        "x": 410,
        "y": 220,
        "type": "link",
        "path": "/Applications"
      }
    ]
  }
}
```

### إعدادات Linux
```json
{
  "linux": {
    "target": [
      {
        "target": "AppImage",
        "arch": ["x64"]
      },
      {
        "target": "deb",
        "arch": ["x64"]
      },
      {
        "target": "rpm",
        "arch": ["x64"]
      }
    ],
    "icon": "assets/icon.png",
    "category": "AudioVideo"
  }
}
```

## 🔐 التوقيع الرقمي

### Windows Code Signing
```bash
# تحتاج شهادة توقيع رقمي
# إعداد متغيرات البيئة
set CSC_LINK=path/to/certificate.p12
set CSC_KEY_PASSWORD=your_password

# البناء مع التوقيع
npm run build-win
```

### macOS Code Signing
```bash
# تحتاج Apple Developer Account
# إعداد متغيرات البيئة
export CSC_LINK=path/to/certificate.p12
export CSC_KEY_PASSWORD=your_password
export APPLE_ID=your_apple_id
export APPLE_ID_PASSWORD=app_specific_password

# البناء مع التوقيع
npm run build-mac
```

## 📁 هيكل ملفات البناء

```
dist/
├── win-unpacked/          # Windows (غير مضغوط)
├── mac/                   # macOS (غير مضغوط)
├── linux-unpacked/        # Linux (غير مضغوط)
├── CinemaHub-Setup.exe    # Windows installer
├── CinemaHub.dmg          # macOS installer
├── CinemaHub.AppImage     # Linux portable
├── CinemaHub.deb          # Debian package
├── CinemaHub.rpm          # RedHat package
└── latest.yml             # Update info
```

## 🧪 الاختبار

### اختبار محلي
```bash
# تشغيل الاختبارات
npm test

# اختبار البناء
npm run pack
# ثم اختبر الملف المُنتج في dist/
```

### اختبار على منصات مختلفة
```bash
# استخدام Docker للاختبار على Linux
docker run --rm -ti \
  --env-file <(env | grep -iE 'DEBUG|NODE_|ELECTRON_|YARN_|NPM_|CI|CIRCLE|TRAVIS_TAG|TRAVIS|TRAVIS_REPO_|TRAVIS_BUILD_|TRAVIS_BRANCH|TRAVIS_PULL_REQUEST_|APPVEYOR_|CSC_|GH_|GITHUB_|BT_|AWS_|STRIP|BUILD_') \
  --env ELECTRON_CACHE="/root/.cache/electron" \
  --env ELECTRON_BUILDER_CACHE="/root/.cache/electron-builder" \
  -v ${PWD}:/project \
  -v ${PWD##*/}-node-modules:/project/node_modules \
  -v ~/.cache/electron:/root/.cache/electron \
  -v ~/.cache/electron-builder:/root/.cache/electron-builder \
  electronuserland/builder:wine \
  /bin/bash -c "npm install && npm run build"
```

## 🚀 النشر والتوزيع

### GitHub Releases
```bash
# إنشاء tag جديد
git tag v1.0.0
git push origin v1.0.0

# البناء والنشر
npm run build
npm run publish
```

### التحديثات التلقائية
```bash
# إعداد خادم التحديثات
# يمكن استخدام GitHub Releases أو خادم مخصص

# في package.json
{
  "build": {
    "publish": [
      {
        "provider": "github",
        "owner": "your-username",
        "repo": "cinemahub-desktop"
      }
    ]
  }
}
```

## 🐛 استكشاف أخطاء البناء

### مشاكل شائعة

#### خطأ في تثبيت native modules
```bash
# إعادة بناء native modules
npm rebuild

# أو حذف node_modules وإعادة التثبيت
rm -rf node_modules
npm install
```

#### مشكلة في الأيقونات
```bash
# تأكد من وجود الأيقونات في assets/
ls -la assets/

# تأكد من صيغ الأيقونات الصحيحة
file assets/icon.ico
file assets/icon.icns
file assets/icon.png
```

#### خطأ في electron-builder
```bash
# تحديث electron-builder
npm update electron-builder

# أو إعادة تثبيت
npm uninstall electron-builder
npm install electron-builder --save-dev
```

#### مشكلة في Python/Visual Studio (Windows)
```bash
# تثبيت build tools
npm install --global windows-build-tools

# أو تثبيت Visual Studio Build Tools يدوياً
```

### تنظيف ملفات البناء
```bash
# حذف ملفات البناء
rm -rf dist/
rm -rf node_modules/

# إعادة التثبيت والبناء
npm install
npm run build
```

## 📊 تحسين الأداء

### تقليل حجم البناء
```json
{
  "build": {
    "compression": "maximum",
    "nsis": {
      "differentialPackage": true
    }
  }
}
```

### استبعاد ملفات غير ضرورية
```json
{
  "build": {
    "files": [
      "!**/node_modules/*/{README.md,CHANGELOG.md,LICENSE}",
      "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}",
      "!**/node_modules/*.d.ts",
      "!**/node_modules/.bin",
      "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}",
      "!.editorconfig",
      "!**/._*",
      "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}",
      "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}",
      "!**/{appveyor.yml,.travis.yml,circle.yml}",
      "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"
    ]
  }
}
```

## 📝 ملاحظات مهمة

1. **تأكد من الأيقونات:** يجب أن تكون جميع الأيقونات موجودة وبالصيغ الصحيحة
2. **اختبر على المنصة المستهدفة:** اختبر البرنامج على نفس نظام التشغيل المستهدف
3. **احفظ نسخة من الشهادات:** احتفظ بنسخة آمنة من شهادات التوقيع الرقمي
4. **راقب حجم البناء:** تأكد من أن حجم البرنامج معقول
5. **اختبر التحديثات:** اختبر آلية التحديث التلقائي قبل النشر

---

**نصائح للنجاح:**
- ابدأ بالبناء المحلي أولاً
- اختبر على أجهزة مختلفة
- استخدم CI/CD للبناء التلقائي
- احتفظ بسجل للإصدارات
- وثق أي تغييرات في إعدادات البناء
