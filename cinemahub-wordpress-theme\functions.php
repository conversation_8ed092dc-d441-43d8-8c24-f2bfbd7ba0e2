<?php
/**
 * CinemaHub Pro Theme Functions
 *
 * @package CinemaHub Pro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Theme setup
function cinemahub_theme_setup() {
    // Add theme support
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    add_theme_support('custom-logo');
    add_theme_support('customize-selective-refresh-widgets');
    
    // Set content width
    $GLOBALS['content_width'] = 1200;
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => 'القائمة الرئيسية',
        'footer' => 'قائمة الفوتر',
    ));
    
    // Add image sizes
    add_image_size('movie-poster', 300, 400, true);
    add_image_size('series-poster', 300, 400, true);
    add_image_size('featured-large', 800, 600, true);
}
add_action('after_setup_theme', 'cinemahub_theme_setup');

// Enqueue scripts and styles
function cinemahub_scripts() {
    // Enqueue styles
    wp_enqueue_style('cinemahub-style', get_stylesheet_uri(), array(), '2.0.0');
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css', array(), '6.4.0');
    wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap', array(), null);
    
    // Enqueue scripts
    wp_enqueue_script('cinemahub-main', get_template_directory_uri() . '/assets/js/main.js', array('jquery'), '2.0.0', true);
    
    // Localize script
    wp_localize_script('cinemahub-main', 'cinemahub_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('cinemahub_nonce'),
    ));
}
add_action('wp_enqueue_scripts', 'cinemahub_scripts');

// Register Custom Post Types
function cinemahub_register_post_types() {
    // Movies Post Type
    register_post_type('movie', array(
        'labels' => array(
            'name' => 'الأفلام',
            'singular_name' => 'فيلم',
            'add_new' => 'إضافة فيلم جديد',
            'add_new_item' => 'إضافة فيلم جديد',
            'edit_item' => 'تحرير الفيلم',
            'new_item' => 'فيلم جديد',
            'view_item' => 'عرض الفيلم',
            'search_items' => 'البحث في الأفلام',
            'not_found' => 'لم يتم العثور على أفلام',
            'not_found_in_trash' => 'لا توجد أفلام في المهملات'
        ),
        'public' => true,
        'has_archive' => true,
        'menu_icon' => 'dashicons-video-alt',
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'rewrite' => array('slug' => 'movies'),
        'show_in_rest' => true,
        'menu_position' => 5
    ));

    // Series Post Type
    register_post_type('series', array(
        'labels' => array(
            'name' => 'المسلسلات',
            'singular_name' => 'مسلسل',
            'add_new' => 'إضافة مسلسل جديد',
            'add_new_item' => 'إضافة مسلسل جديد',
            'edit_item' => 'تحرير المسلسل',
            'new_item' => 'مسلسل جديد',
            'view_item' => 'عرض المسلسل',
            'search_items' => 'البحث في المسلسلات',
            'not_found' => 'لم يتم العثور على مسلسلات',
            'not_found_in_trash' => 'لا توجد مسلسلات في المهملات'
        ),
        'public' => true,
        'has_archive' => true,
        'menu_icon' => 'dashicons-desktop',
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'rewrite' => array('slug' => 'series'),
        'show_in_rest' => true,
        'menu_position' => 6
    ));
}
add_action('init', 'cinemahub_register_post_types');

// Register Taxonomies
function cinemahub_register_taxonomies() {
    // Movie Genres
    register_taxonomy('movie_genre', 'movie', array(
        'labels' => array(
            'name' => 'تصنيفات الأفلام',
            'singular_name' => 'تصنيف فيلم',
            'add_new_item' => 'إضافة تصنيف جديد',
            'edit_item' => 'تحرير التصنيف',
            'update_item' => 'تحديث التصنيف',
            'view_item' => 'عرض التصنيف',
            'search_items' => 'البحث في التصنيفات'
        ),
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'show_in_rest' => true,
        'rewrite' => array('slug' => 'movie-genre')
    ));

    // Series Genres
    register_taxonomy('series_genre', 'series', array(
        'labels' => array(
            'name' => 'تصنيفات المسلسلات',
            'singular_name' => 'تصنيف مسلسل',
            'add_new_item' => 'إضافة تصنيف جديد',
            'edit_item' => 'تحرير التصنيف',
            'update_item' => 'تحديث التصنيف',
            'view_item' => 'عرض التصنيف',
            'search_items' => 'البحث في التصنيفات'
        ),
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'show_in_rest' => true,
        'rewrite' => array('slug' => 'series-genre')
    ));

    // Countries
    register_taxonomy('country', array('movie', 'series'), array(
        'labels' => array(
            'name' => 'البلدان',
            'singular_name' => 'بلد',
            'add_new_item' => 'إضافة بلد جديد',
            'edit_item' => 'تحرير البلد',
            'update_item' => 'تحديث البلد',
            'view_item' => 'عرض البلد',
            'search_items' => 'البحث في البلدان'
        ),
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'show_in_rest' => true,
        'rewrite' => array('slug' => 'country')
    ));
}
add_action('init', 'cinemahub_register_taxonomies');

// Add Meta Boxes
function cinemahub_add_meta_boxes() {
    // Movie Meta Box
    add_meta_box(
        'movie_details',
        'تفاصيل الفيلم',
        'cinemahub_movie_meta_box_callback',
        'movie',
        'normal',
        'high'
    );

    // Series Meta Box
    add_meta_box(
        'series_details',
        'تفاصيل المسلسل',
        'cinemahub_series_meta_box_callback',
        'series',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'cinemahub_add_meta_boxes');

// Movie Meta Box Callback
function cinemahub_movie_meta_box_callback($post) {
    wp_nonce_field('cinemahub_movie_meta_box', 'cinemahub_movie_meta_box_nonce');
    
    $rating = get_post_meta($post->ID, '_movie_rating', true);
    $year = get_post_meta($post->ID, '_movie_year', true);
    $duration = get_post_meta($post->ID, '_movie_duration', true);
    $trailer_url = get_post_meta($post->ID, '_movie_trailer_url', true);
    $watch_url = get_post_meta($post->ID, '_movie_watch_url', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="movie_rating">التقييم (من 10):</label></th>';
    echo '<td><input type="number" id="movie_rating" name="movie_rating" value="' . esc_attr($rating) . '" min="0" max="10" step="0.1" /></td></tr>';
    
    echo '<tr><th><label for="movie_year">سنة الإنتاج:</label></th>';
    echo '<td><input type="number" id="movie_year" name="movie_year" value="' . esc_attr($year) . '" min="1900" max="2030" /></td></tr>';
    
    echo '<tr><th><label for="movie_duration">المدة (بالدقائق):</label></th>';
    echo '<td><input type="number" id="movie_duration" name="movie_duration" value="' . esc_attr($duration) . '" min="1" /></td></tr>';
    
    echo '<tr><th><label for="movie_trailer_url">رابط الإعلان:</label></th>';
    echo '<td><input type="url" id="movie_trailer_url" name="movie_trailer_url" value="' . esc_attr($trailer_url) . '" style="width: 100%;" /></td></tr>';
    
    echo '<tr><th><label for="movie_watch_url">رابط المشاهدة:</label></th>';
    echo '<td><input type="url" id="movie_watch_url" name="movie_watch_url" value="' . esc_attr($watch_url) . '" style="width: 100%;" /></td></tr>';
    echo '</table>';
}

// Series Meta Box Callback
function cinemahub_series_meta_box_callback($post) {
    wp_nonce_field('cinemahub_series_meta_box', 'cinemahub_series_meta_box_nonce');
    
    $rating = get_post_meta($post->ID, '_series_rating', true);
    $year = get_post_meta($post->ID, '_series_year', true);
    $seasons = get_post_meta($post->ID, '_series_seasons', true);
    $episodes = get_post_meta($post->ID, '_series_episodes', true);
    $status = get_post_meta($post->ID, '_series_status', true);
    $trailer_url = get_post_meta($post->ID, '_series_trailer_url', true);
    $watch_url = get_post_meta($post->ID, '_series_watch_url', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="series_rating">التقييم (من 10):</label></th>';
    echo '<td><input type="number" id="series_rating" name="series_rating" value="' . esc_attr($rating) . '" min="0" max="10" step="0.1" /></td></tr>';
    
    echo '<tr><th><label for="series_year">سنة الإنتاج:</label></th>';
    echo '<td><input type="number" id="series_year" name="series_year" value="' . esc_attr($year) . '" min="1900" max="2030" /></td></tr>';
    
    echo '<tr><th><label for="series_seasons">عدد المواسم:</label></th>';
    echo '<td><input type="number" id="series_seasons" name="series_seasons" value="' . esc_attr($seasons) . '" min="1" /></td></tr>';
    
    echo '<tr><th><label for="series_episodes">عدد الحلقات:</label></th>';
    echo '<td><input type="number" id="series_episodes" name="series_episodes" value="' . esc_attr($episodes) . '" min="1" /></td></tr>';
    
    echo '<tr><th><label for="series_status">حالة المسلسل:</label></th>';
    echo '<td><select id="series_status" name="series_status">';
    echo '<option value="ongoing"' . selected($status, 'ongoing', false) . '>مستمر</option>';
    echo '<option value="completed"' . selected($status, 'completed', false) . '>مكتمل</option>';
    echo '<option value="cancelled"' . selected($status, 'cancelled', false) . '>ملغي</option>';
    echo '</select></td></tr>';
    
    echo '<tr><th><label for="series_trailer_url">رابط الإعلان:</label></th>';
    echo '<td><input type="url" id="series_trailer_url" name="series_trailer_url" value="' . esc_attr($trailer_url) . '" style="width: 100%;" /></td></tr>';
    
    echo '<tr><th><label for="series_watch_url">رابط المشاهدة:</label></th>';
    echo '<td><input type="url" id="series_watch_url" name="series_watch_url" value="' . esc_attr($watch_url) . '" style="width: 100%;" /></td></tr>';
    echo '</table>';
}

// Save Meta Box Data
function cinemahub_save_meta_box_data($post_id) {
    // Check if nonce is valid
    if (!isset($_POST['cinemahub_movie_meta_box_nonce']) && !isset($_POST['cinemahub_series_meta_box_nonce'])) {
        return;
    }

    // Verify nonce
    if (isset($_POST['cinemahub_movie_meta_box_nonce'])) {
        if (!wp_verify_nonce($_POST['cinemahub_movie_meta_box_nonce'], 'cinemahub_movie_meta_box')) {
            return;
        }
    }

    if (isset($_POST['cinemahub_series_meta_box_nonce'])) {
        if (!wp_verify_nonce($_POST['cinemahub_series_meta_box_nonce'], 'cinemahub_series_meta_box')) {
            return;
        }
    }

    // Check if user has permission to edit
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Save movie meta
    if (get_post_type($post_id) == 'movie') {
        $fields = array('movie_rating', 'movie_year', 'movie_duration', 'movie_trailer_url', 'movie_watch_url');
        foreach ($fields as $field) {
            if (isset($_POST[$field])) {
                update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
            }
        }
    }

    // Save series meta
    if (get_post_type($post_id) == 'series') {
        $fields = array('series_rating', 'series_year', 'series_seasons', 'series_episodes', 'series_status', 'series_trailer_url', 'series_watch_url');
        foreach ($fields as $field) {
            if (isset($_POST[$field])) {
                update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
            }
        }
    }
}
add_action('save_post', 'cinemahub_save_meta_box_data');

// Add admin menu for theme options
function cinemahub_admin_menu() {
    add_theme_page(
        'إعدادات CinemaHub Pro',
        'إعدادات القالب',
        'manage_options',
        'cinemahub-settings',
        'cinemahub_settings_page'
    );
}
add_action('admin_menu', 'cinemahub_admin_menu');

// Settings page callback
function cinemahub_settings_page() {
    ?>
    <div class="wrap">
        <h1>إعدادات CinemaHub Pro</h1>
        
        <div style="background: white; padding: 20px; border-radius: 8px; margin-top: 20px;">
            <h2>خاصية استعادة المظهر الأصلي</h2>
            <p>يمكنك استعادة المظهر الأصلي للموقع في أي وقت باستخدام الأزرار التالية:</p>
            
            <div style="margin: 20px 0;">
                <a href="<?php echo get_template_directory_uri(); ?>/restore-original.php" class="button button-primary" style="margin-left: 10px;">
                    استعادة المظهر الأصلي
                </a>
                
                <a href="<?php echo get_template_directory_uri(); ?>/backup-restore.php" class="button button-secondary">
                    إدارة النسخ الاحتياطية
                </a>
            </div>
            
            <div style="background: #f0f8ff; padding: 15px; border-radius: 5px; border-left: 4px solid #0073aa;">
                <h3>ملاحظات مهمة:</h3>
                <ul>
                    <li>استعادة المظهر الأصلي ستعيد جميع الألوان والتصميم إلى الحالة الافتراضية</li>
                    <li>لن يتم حذف أي محتوى (أفلام، مسلسلات، مقالات)</li>
                    <li>يمكنك إنشاء نسخة احتياطية قبل التغيير</li>
                    <li>العملية آمنة ويمكن التراجع عنها</li>
                </ul>
            </div>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; margin-top: 20px;">
            <h2>معلومات القالب</h2>
            <table class="form-table">
                <tr>
                    <th>اسم القالب:</th>
                    <td>CinemaHub Pro</td>
                </tr>
                <tr>
                    <th>الإصدار:</th>
                    <td>2.0.0</td>
                </tr>
                <tr>
                    <th>المطور:</th>
                    <td>CinemaHub Team</td>
                </tr>
                <tr>
                    <th>آخر تحديث:</th>
                    <td><?php echo date('Y-m-d H:i:s'); ?></td>
                </tr>
            </table>
        </div>
    </div>
    <?php
}

// Custom excerpt length
function cinemahub_excerpt_length($length) {
    return 20;
}
add_filter('excerpt_length', 'cinemahub_excerpt_length');

// Custom excerpt more
function cinemahub_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'cinemahub_excerpt_more');

// Add body classes
function cinemahub_body_classes($classes) {
    if (is_singular('movie')) {
        $classes[] = 'single-movie';
    }
    if (is_singular('series')) {
        $classes[] = 'single-series';
    }
    if (is_post_type_archive('movie')) {
        $classes[] = 'archive-movies';
    }
    if (is_post_type_archive('series')) {
        $classes[] = 'archive-series';
    }
    return $classes;
}
add_filter('body_class', 'cinemahub_body_classes');

// Flush rewrite rules on theme activation
function cinemahub_flush_rewrite_rules() {
    cinemahub_register_post_types();
    cinemahub_register_taxonomies();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'cinemahub_flush_rewrite_rules');

// Add default terms on activation
function cinemahub_add_default_terms() {
    // Default movie genres
    $movie_genres = array('أكشن', 'دراما', 'كوميديا', 'رعب', 'خيال علمي', 'رومانسي', 'مغامرات', 'جريمة');
    foreach ($movie_genres as $genre) {
        if (!term_exists($genre, 'movie_genre')) {
            wp_insert_term($genre, 'movie_genre');
        }
    }
    
    // Default series genres
    $series_genres = array('دراما', 'كوميديا', 'أكشن', 'إثارة', 'تاريخي', 'اجتماعي', 'رومانسي', 'جريمة');
    foreach ($series_genres as $genre) {
        if (!term_exists($genre, 'series_genre')) {
            wp_insert_term($genre, 'series_genre');
        }
    }
    
    // Default countries
    $countries = array('مصر', 'السعودية', 'الإمارات', 'لبنان', 'سوريا', 'الأردن', 'المغرب', 'تونس', 'الجزائر', 'العراق');
    foreach ($countries as $country) {
        if (!term_exists($country, 'country')) {
            wp_insert_term($country, 'country');
        }
    }
}
add_action('after_switch_theme', 'cinemahub_add_default_terms');

?>
