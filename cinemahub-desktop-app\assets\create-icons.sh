#!/bin/bash

# CinemaHub Pro Desktop - Icon Creation Script
# هذا السكريبت ينشئ جميع الأيقونات المطلوبة من ملف PNG واحد

echo "🎨 إنشاء أيقونات CinemaHub Pro Desktop..."

# التحقق من وجود ImageMagick
if ! command -v convert &> /dev/null; then
    echo "❌ ImageMagick غير مثبت. يرجى تثبيته أولاً:"
    echo "   Ubuntu/Debian: sudo apt install imagemagick"
    echo "   macOS: brew install imagemagick"
    echo "   Windows: تحميل من https://imagemagick.org/"
    exit 1
fi

# التحقق من وجود الملف المصدر
if [ ! -f "icon-source.png" ]; then
    echo "❌ ملف icon-source.png غير موجود"
    echo "يرجى إنشاء ملف icon-source.png بحجم 1024x1024 بكسل"
    exit 1
fi

echo "📁 إنشاء الأيقونات..."

# إنشاء أيقونة PNG عامة (512x512)
convert icon-source.png -resize 512x512 icon.png
echo "✅ تم إنشاء icon.png"

# إنشاء أيقونة Windows ICO
convert icon-source.png \
    \( -clone 0 -resize 16x16 \) \
    \( -clone 0 -resize 32x32 \) \
    \( -clone 0 -resize 48x48 \) \
    \( -clone 0 -resize 64x64 \) \
    \( -clone 0 -resize 128x128 \) \
    \( -clone 0 -resize 256x256 \) \
    -delete 0 icon.ico
echo "✅ تم إنشاء icon.ico"

# إنشاء أيقونة macOS ICNS (إذا كان النظام macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 إنشاء أيقونة macOS..."
    
    # إنشاء مجلد iconset
    mkdir -p icon.iconset
    
    # إنشاء جميع الأحجام المطلوبة
    convert icon-source.png -resize 16x16 icon.iconset/icon_16x16.png
    convert icon-source.png -resize 32x32 icon.iconset/<EMAIL>
    convert icon-source.png -resize 32x32 icon.iconset/icon_32x32.png
    convert icon-source.png -resize 64x64 icon.iconset/<EMAIL>
    convert icon-source.png -resize 128x128 icon.iconset/icon_128x128.png
    convert icon-source.png -resize 256x256 icon.iconset/<EMAIL>
    convert icon-source.png -resize 256x256 icon.iconset/icon_256x256.png
    convert icon-source.png -resize 512x512 icon.iconset/<EMAIL>
    convert icon-source.png -resize 512x512 icon.iconset/icon_512x512.png
    convert icon-source.png -resize 1024x1024 icon.iconset/<EMAIL>
    
    # تحويل إلى ICNS
    iconutil -c icns icon.iconset
    
    # تنظيف
    rm -rf icon.iconset
    
    echo "✅ تم إنشاء icon.icns"
else
    echo "⚠️  تخطي إنشاء ICNS (يتطلب macOS)"
fi

# إنشاء أيقونات إضافية للـ installer
convert icon-source.png -resize 164x314 installer-welcome.png
echo "✅ تم إنشاء installer-welcome.png"

convert icon-source.png -resize 540x380 dmg-background.png
echo "✅ تم إنشاء dmg-background.png"

echo ""
echo "🎉 تم إنشاء جميع الأيقونات بنجاح!"
echo ""
echo "الملفات المُنشأة:"
echo "  📄 icon.png (512x512) - أيقونة عامة"
echo "  📄 icon.ico (متعدد الأحجام) - Windows"
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "  📄 icon.icns (متعدد الأحجام) - macOS"
fi
echo "  📄 installer-welcome.png (164x314) - صورة ترحيب Windows"
echo "  📄 dmg-background.png (540x380) - خلفية DMG macOS"
echo ""
echo "يمكنك الآن بناء البرنامج باستخدام: npm run build"
