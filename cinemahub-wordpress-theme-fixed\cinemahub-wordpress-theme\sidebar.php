<?php
/**
 * The sidebar containing the main widget area
 *
 * @package CinemaHub Pro
 */

if (!is_active_sidebar('sidebar-1')) {
    return;
}
?>

<aside id="secondary" class="widget-area sidebar" style="background: var(--surface-color); border-radius: var(--border-radius-2xl); padding: 2rem; border: 2px solid rgba(148, 163, 184, 0.1); box-shadow: var(--shadow-lg); position: sticky; top: 100px;">
    <?php if (is_active_sidebar('sidebar-1')) : ?>
        <?php dynamic_sidebar('sidebar-1'); ?>
    <?php else : ?>
        <!-- Default Sidebar Content -->
        
        <!-- Search Widget -->
        <div class="widget widget-search" style="margin-bottom: 2rem;">
            <h3 class="widget-title" style="color: var(--light-color); font-size: var(--font-size-lg); font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-search" style="color: var(--primary-color);"></i>
                البحث
            </h3>
            <form class="search-form" method="get" action="<?php echo esc_url(home_url('/')); ?>">
                <div style="position: relative;">
                    <input type="search" name="s" placeholder="ابحث عن الأفلام والمسلسلات..." value="<?php echo get_search_query(); ?>" style="width: 100%; padding: 0.75rem 1rem; padding-left: 3rem; border: 1px solid rgba(148, 163, 184, 0.3); border-radius: var(--border-radius-lg); background: rgba(255, 255, 255, 0.05); color: var(--light-color); font-size: var(--font-size-sm);">
                    <button type="submit" style="position: absolute; left: 0.5rem; top: 50%; transform: translateY(-50%); background: var(--gradient-primary); border: none; border-radius: var(--border-radius); padding: 0.5rem; color: white; cursor: pointer;">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>

        <!-- Popular Movies Widget -->
        <div class="widget widget-popular-movies" style="margin-bottom: 2rem;">
            <h3 class="widget-title" style="color: var(--light-color); font-size: var(--font-size-lg); font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-fire" style="color: var(--primary-color);"></i>
                الأفلام الشائعة
            </h3>
            <div class="popular-movies-list">
                <?php
                $popular_movies = new WP_Query(array(
                    'post_type' => 'movie',
                    'posts_per_page' => 5,
                    'meta_key' => 'views',
                    'orderby' => 'meta_value_num',
                    'order' => 'DESC'
                ));

                if ($popular_movies->have_posts()) :
                    while ($popular_movies->have_posts()) : $popular_movies->the_post();
                        $rating = get_post_meta(get_the_ID(), '_movie_rating', true);
                        $year = get_post_meta(get_the_ID(), '_movie_year', true);
                ?>
                    <div class="popular-movie-item" style="display: flex; gap: 1rem; margin-bottom: 1rem; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: var(--border-radius-lg); transition: var(--transition); cursor: pointer;" onclick="window.location.href='<?php the_permalink(); ?>'" onmouseover="this.style.background='rgba(220, 38, 38, 0.1)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.05)'">
                        <div class="movie-thumb" style="flex-shrink: 0;">
                            <?php if (has_post_thumbnail()) : ?>
                                <?php the_post_thumbnail('movie-thumb', array('style' => 'width: 60px; height: 80px; object-fit: cover; border-radius: var(--border-radius);')); ?>
                            <?php else : ?>
                                <div style="width: 60px; height: 80px; background: var(--gradient-primary); border-radius: var(--border-radius); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">
                                    <i class="fas fa-film"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="movie-info" style="flex: 1; min-width: 0;">
                            <h4 style="color: var(--light-color); font-size: var(--font-size-sm); font-weight: 600; margin-bottom: 0.25rem; line-height: 1.3; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                <?php the_title(); ?>
                            </h4>
                            <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.25rem;">
                                <?php if ($year) : ?>
                                    <span style="color: var(--gray-400); font-size: var(--font-size-xs);">
                                        <i class="fas fa-calendar"></i> <?php echo esc_html($year); ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                            <?php if ($rating) : ?>
                                <div style="display: flex; align-items: center; gap: 0.25rem; color: var(--accent-color); font-size: var(--font-size-xs); font-weight: 600;">
                                    <i class="fas fa-star"></i>
                                    <span><?php echo esc_html($rating); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php 
                    endwhile;
                    wp_reset_postdata();
                else :
                    // Default popular movies if no posts exist
                    for ($i = 1; $i <= 5; $i++) :
                ?>
                    <div class="popular-movie-item" style="display: flex; gap: 1rem; margin-bottom: 1rem; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: var(--border-radius-lg); transition: var(--transition);">
                        <div class="movie-thumb" style="flex-shrink: 0;">
                            <div style="width: 60px; height: 80px; background: var(--gradient-primary); border-radius: var(--border-radius); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">
                                <i class="fas fa-film"></i>
                            </div>
                        </div>
                        <div class="movie-info" style="flex: 1; min-width: 0;">
                            <h4 style="color: var(--light-color); font-size: var(--font-size-sm); font-weight: 600; margin-bottom: 0.25rem; line-height: 1.3;">
                                فيلم شائع <?php echo $i; ?>
                            </h4>
                            <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.25rem;">
                                <span style="color: var(--gray-400); font-size: var(--font-size-xs);">
                                    <i class="fas fa-calendar"></i> 2024
                                </span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.25rem; color: var(--accent-color); font-size: var(--font-size-xs); font-weight: 600;">
                                <i class="fas fa-star"></i>
                                <span>8.<?php echo $i; ?></span>
                            </div>
                        </div>
                    </div>
                <?php endfor; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Categories Widget -->
        <div class="widget widget-categories" style="margin-bottom: 2rem;">
            <h3 class="widget-title" style="color: var(--light-color); font-size: var(--font-size-lg); font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-th-large" style="color: var(--primary-color);"></i>
                التصنيفات
            </h3>
            <div class="categories-list">
                <?php
                $sidebar_genres = get_terms(array(
                    'taxonomy' => 'movie_genre',
                    'hide_empty' => false,
                    'number' => 8
                ));

                if (!empty($sidebar_genres)) :
                    foreach ($sidebar_genres as $genre) :
                ?>
                    <a href="<?php echo get_term_link($genre); ?>" style="display: flex; align-items: center; justify-content: space-between; padding: 0.5rem 0.75rem; margin-bottom: 0.5rem; background: rgba(255, 255, 255, 0.05); border-radius: var(--border-radius); color: var(--gray-300); text-decoration: none; transition: var(--transition); font-size: var(--font-size-sm);" onmouseover="this.style.background='rgba(220, 38, 38, 0.1)'; this.style.color='var(--light-color)'; this.style.transform='translateX(-3px)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.05)'; this.style.color='var(--gray-300)'; this.style.transform='translateX(0)'">
                        <span>
                            <i class="fas fa-tag" style="color: var(--primary-color); margin-left: 0.5rem;"></i>
                            <?php echo esc_html($genre->name); ?>
                        </span>
                        <span style="background: var(--primary-color); color: white; padding: 0.2rem 0.5rem; border-radius: var(--border-radius-sm); font-size: var(--font-size-xs); font-weight: 600;">
                            <?php echo $genre->count; ?>
                        </span>
                    </a>
                <?php 
                    endforeach;
                else :
                    // Default categories
                    $default_sidebar_categories = array(
                        array('name' => 'أفلام الأكشن', 'count' => '150'),
                        array('name' => 'أفلام الكوميديا', 'count' => '120'),
                        array('name' => 'أفلام الرعب', 'count' => '80'),
                        array('name' => 'مسلسلات تركية', 'count' => '200'),
                        array('name' => 'مسلسلات كورية', 'count' => '100'),
                        array('name' => 'أفلام وثائقية', 'count' => '60'),
                        array('name' => 'أفلام رومانسية', 'count' => '90'),
                        array('name' => 'أفلام خيال علمي', 'count' => '70')
                    );
                    
                    foreach ($default_sidebar_categories as $category) :
                ?>
                    <a href="#" style="display: flex; align-items: center; justify-content: space-between; padding: 0.5rem 0.75rem; margin-bottom: 0.5rem; background: rgba(255, 255, 255, 0.05); border-radius: var(--border-radius); color: var(--gray-300); text-decoration: none; transition: var(--transition); font-size: var(--font-size-sm);" onmouseover="this.style.background='rgba(220, 38, 38, 0.1)'; this.style.color='var(--light-color)'; this.style.transform='translateX(-3px)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.05)'; this.style.color='var(--gray-300)'; this.style.transform='translateX(0)'">
                        <span>
                            <i class="fas fa-tag" style="color: var(--primary-color); margin-left: 0.5rem;"></i>
                            <?php echo esc_html($category['name']); ?>
                        </span>
                        <span style="background: var(--primary-color); color: white; padding: 0.2rem 0.5rem; border-radius: var(--border-radius-sm); font-size: var(--font-size-xs); font-weight: 600;">
                            <?php echo esc_html($category['count']); ?>
                        </span>
                    </a>
                <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Recent Posts Widget -->
        <div class="widget widget-recent-posts" style="margin-bottom: 2rem;">
            <h3 class="widget-title" style="color: var(--light-color); font-size: var(--font-size-lg); font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-newspaper" style="color: var(--primary-color);"></i>
                أحدث المقالات
            </h3>
            <div class="recent-posts-list">
                <?php
                $recent_posts = new WP_Query(array(
                    'posts_per_page' => 5,
                    'post_status' => 'publish'
                ));

                if ($recent_posts->have_posts()) :
                    while ($recent_posts->have_posts()) : $recent_posts->the_post();
                ?>
                    <div class="recent-post-item" style="margin-bottom: 1rem; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: var(--border-radius-lg); transition: var(--transition); cursor: pointer;" onclick="window.location.href='<?php the_permalink(); ?>'" onmouseover="this.style.background='rgba(220, 38, 38, 0.1)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.05)'">
                        <h4 style="color: var(--light-color); font-size: var(--font-size-sm); font-weight: 600; margin-bottom: 0.5rem; line-height: 1.3;">
                            <?php the_title(); ?>
                        </h4>
                        <div style="color: var(--gray-400); font-size: var(--font-size-xs); display: flex; align-items: center; gap: 0.5rem;">
                            <span><i class="fas fa-calendar"></i> <?php echo get_the_date(); ?></span>
                            <span><i class="fas fa-comments"></i> <?php comments_number('0', '1', '%'); ?></span>
                        </div>
                    </div>
                <?php 
                    endwhile;
                    wp_reset_postdata();
                else :
                    // Default recent posts
                    for ($i = 1; $i <= 5; $i++) :
                ?>
                    <div class="recent-post-item" style="margin-bottom: 1rem; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: var(--border-radius-lg); transition: var(--transition);">
                        <h4 style="color: var(--light-color); font-size: var(--font-size-sm); font-weight: 600; margin-bottom: 0.5rem; line-height: 1.3;">
                            مقال تجريبي <?php echo $i; ?>
                        </h4>
                        <div style="color: var(--gray-400); font-size: var(--font-size-xs); display: flex; align-items: center; gap: 0.5rem;">
                            <span><i class="fas fa-calendar"></i> <?php echo date('Y-m-d'); ?></span>
                            <span><i class="fas fa-comments"></i> 0</span>
                        </div>
                    </div>
                <?php endfor; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Tags Widget -->
        <div class="widget widget-tags">
            <h3 class="widget-title" style="color: var(--light-color); font-size: var(--font-size-lg); font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-tags" style="color: var(--primary-color);"></i>
                الكلمات المفتاحية
            </h3>
            <div class="tags-cloud" style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                <?php
                $tags = get_tags(array('number' => 15));
                if (!empty($tags)) :
                    foreach ($tags as $tag) :
                ?>
                    <a href="<?php echo get_tag_link($tag->term_id); ?>" style="padding: 0.4rem 0.8rem; background: rgba(255, 255, 255, 0.05); color: var(--gray-300); text-decoration: none; border-radius: var(--border-radius); font-size: var(--font-size-xs); font-weight: 500; transition: var(--transition);" onmouseover="this.style.background='var(--gradient-primary)'; this.style.color='white'; this.style.transform='scale(1.05)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.05)'; this.style.color='var(--gray-300)'; this.style.transform='scale(1)'">
                        <?php echo esc_html($tag->name); ?>
                    </a>
                <?php 
                    endforeach;
                else :
                    // Default tags
                    $default_tags = array('أكشن', 'كوميديا', 'رعب', 'رومانسي', 'خيال علمي', 'مغامرة', 'دراما', 'إثارة', 'تركي', 'كوري', 'أمريكي', 'عربي', 'هندي', 'ياباني', 'أنمي');
                    foreach ($default_tags as $tag) :
                ?>
                    <a href="#" style="padding: 0.4rem 0.8rem; background: rgba(255, 255, 255, 0.05); color: var(--gray-300); text-decoration: none; border-radius: var(--border-radius); font-size: var(--font-size-xs); font-weight: 500; transition: var(--transition);" onmouseover="this.style.background='var(--gradient-primary)'; this.style.color='white'; this.style.transform='scale(1.05)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.05)'; this.style.color='var(--gray-300)'; this.style.transform='scale(1)'">
                        <?php echo esc_html($tag); ?>
                    </a>
                <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</aside>

<style>
/* Sidebar Animations */
.widget {
    animation: fadeInUp 0.6s ease;
}

.widget:nth-child(2) {
    animation-delay: 0.1s;
}

.widget:nth-child(3) {
    animation-delay: 0.2s;
}

.widget:nth-child(4) {
    animation-delay: 0.3s;
}

.widget:nth-child(5) {
    animation-delay: 0.4s;
}

/* Search Form Styles */
.widget-search input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
}

.widget-search button:hover {
    background: var(--gradient-primary-light);
    transform: scale(1.1);
}

/* Responsive Sidebar */
@media (max-width: 991px) {
    .sidebar {
        margin-top: 3rem;
        position: static !important;
    }
}

@media (max-width: 768px) {
    .sidebar {
        padding: 1.5rem;
    }
    
    .widget-title {
        font-size: var(--font-size-base) !important;
    }
    
    .popular-movie-item {
        flex-direction: column;
        text-align: center;
    }
    
    .movie-thumb {
        align-self: center;
    }
}
</style>
