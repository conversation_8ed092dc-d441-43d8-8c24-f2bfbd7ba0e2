# 🚀 دليل البدء السريع - CinemaHub Pro Desktop

## 📥 **للمستخدمين العاديين - تحميل وتثبيت البرنامج**

### 🪟 **Windows**

#### **الطريقة الأولى: Installer (مستحسن)**
1. **حمل الملف**: `CinemaHub-Pro-Desktop-Setup-1.0.0.exe`
2. **انقر نقراً مزدوجاً** على الملف المحمل
3. **اتبع تعليمات التثبيت**:
   - اضغط "Next" للمتابعة
   - اقرأ ووافق على الترخيص
   - اختر مجلد التثبيت (أو اترك الافتراضي)
   - اضغط "Install"
4. **انتظر انتهاء التثبيت** (1-2 دقيقة)
5. **شغل البرنامج** من سطح المكتب أو قائمة ابدأ

#### **الطريقة الثانية: النسخة المحمولة**
1. **حمل الملف**: `CinemaHub-Pro-Desktop-1.0.0-Portable.exe`
2. **انقر نقراً مزدوجاً** لتشغيل البرنامج مباشرة
3. **لا يحتاج تثبيت** - يعمل فوراً

### 🍎 **macOS**

#### **الطريقة الأولى: DMG (مستحسن)**
1. **حمل الملف**: `CinemaHub-Pro-Desktop-1.0.0.dmg`
2. **انقر نقراً مزدوجاً** لفتح الملف
3. **اسحب أيقونة التطبيق** إلى مجلد Applications
4. **شغل التطبيق** من مجلد Applications أو Launchpad

#### **الطريقة الثانية: ZIP**
1. **حمل الملف**: `CinemaHub-Pro-Desktop-1.0.0-mac.zip`
2. **انقر نقراً مزدوجاً** لاستخراج الملف
3. **انقل التطبيق** إلى مجلد Applications
4. **شغل التطبيق**

### 🐧 **Linux**

#### **الطريقة الأولى: AppImage (الأسهل)**
1. **حمل الملف**: `CinemaHub-Pro-Desktop-1.0.0-x86_64.AppImage`
2. **افتح Terminal** في مجلد التحميل
3. **اجعل الملف قابل للتنفيذ**:
   ```bash
   chmod +x CinemaHub-Pro-Desktop-1.0.0-x86_64.AppImage
   ```
4. **شغل البرنامج**:
   ```bash
   ./CinemaHub-Pro-Desktop-1.0.0-x86_64.AppImage
   ```

#### **الطريقة الثانية: DEB (Ubuntu/Debian)**
1. **حمل الملف**: `CinemaHub-Pro-Desktop-1.0.0-amd64.deb`
2. **ثبت الحزمة**:
   ```bash
   sudo dpkg -i CinemaHub-Pro-Desktop-1.0.0-amd64.deb
   ```
3. **شغل البرنامج** من قائمة التطبيقات

#### **الطريقة الثالثة: RPM (CentOS/RHEL/Fedora)**
1. **حمل الملف**: `CinemaHub-Pro-Desktop-1.0.0-x86_64.rpm`
2. **ثبت الحزمة**:
   ```bash
   sudo rpm -i CinemaHub-Pro-Desktop-1.0.0-x86_64.rpm
   ```
3. **شغل البرنامج** من قائمة التطبيقات

---

## 🛠️ **للمطورين - بناء البرنامج من المصدر**

### **المتطلبات**
- Node.js (الإصدار 16 أو أحدث)
- npm
- Git (اختياري)

### **خطوات البناء**

#### **1. تحضير البيئة**
```bash
# تحميل المشروع
git clone <repository-url>
cd cinemahub-desktop-app

# أو إذا كان لديك ملفات محلية
cd cinemahub-desktop-app
```

#### **2. تثبيت التبعيات**
```bash
npm install
```

#### **3. إنشاء الأيقونات (مطلوب)**
```bash
# ضع ملف icon-source.png (1024x1024) في مجلد assets/
# ثم شغل:
cd assets
chmod +x create-icons.sh
./create-icons.sh
cd ..
```

#### **4. اختبار البرنامج**
```bash
# تشغيل في وضع التطوير
npm run dev

# أو تشغيل عادي
npm start
```

#### **5. بناء ملفات التوزيع**

##### **بناء لجميع المنصات**
```bash
npm run build
```

##### **بناء لمنصة محددة**
```bash
# Windows
npm run build-win

# macOS
npm run build-mac

# Linux
npm run build-linux
```

#### **6. العثور على الملفات المُنتجة**
```bash
# ستجد الملفات في مجلد dist/
ls -la dist/

# أمثلة على الملفات:
# Windows: CinemaHub-Pro-Desktop-Setup-1.0.0.exe
# macOS: CinemaHub-Pro-Desktop-1.0.0.dmg
# Linux: CinemaHub-Pro-Desktop-1.0.0-x86_64.AppImage
```

---

## 🎯 **الاستخدام الأساسي**

### **عند تشغيل البرنامج لأول مرة**

1. **شاشة التحميل**: ستظهر لثوانٍ قليلة
2. **لوحة التحكم**: ستفتح الصفحة الرئيسية
3. **الشريط الجانبي**: يحتوي على جميع الأقسام

### **الأقسام الرئيسية**

#### **📊 لوحة التحكم**
- عرض الإحصائيات العامة
- النشاط الأخير
- إجراءات سريعة

#### **🎬 إدارة الأفلام**
- إضافة أفلام جديدة
- تحرير الأفلام الموجودة
- البحث والفلترة

#### **📺 إدارة المسلسلات**
- إضافة مسلسلات جديدة
- إدارة المواسم والحلقات
- تتبع حالة المسلسل

#### **🗂️ التصنيفات**
- إدارة تصنيفات الأفلام والمسلسلات
- إنشاء تصنيفات مخصصة

#### **🌐 إدارة الموقع**
- تحرير ملفات الموقع
- معاينة التغييرات
- إدارة الملفات

#### **🔄 المزامنة**
- مزامنة مع الموقع
- تتبع حالة المزامنة
- إعدادات الخادم

#### **⚙️ الإعدادات**
- إعدادات عامة
- إعدادات المزامنة
- معلومات البرنامج

---

## 🆘 **حل المشاكل الشائعة**

### **البرنامج لا يبدأ**

#### **Windows**
- تأكد من أن Windows Defender لا يحجب البرنامج
- شغل البرنامج كمدير (Run as Administrator)
- تأكد من تثبيت Visual C++ Redistributable

#### **macOS**
- اذهب إلى System Preferences > Security & Privacy
- اسمح بتشغيل البرنامج من مطور غير معروف
- أو شغل: `sudo spctl --master-disable`

#### **Linux**
- تأكد من أن الملف قابل للتنفيذ: `chmod +x filename`
- ثبت المكتبات المطلوبة: `sudo apt install libgtk-3-0 libxss1`

### **مشاكل المزامنة**
1. تحقق من اتصال الإنترنت
2. تأكد من صحة إعدادات الخادم
3. تحقق من صلاحيات الكتابة في مجلد الموقع

### **مشاكل قاعدة البيانات**
1. أعد تشغيل البرنامج
2. تحقق من مساحة القرص الصلب
3. استورد نسخة احتياطية إن وجدت

---

## 📞 **الحصول على المساعدة**

### **الموارد المتاحة**
- **README.md**: دليل المستخدم الشامل
- **BUILD.md**: دليل البناء والتطوير
- **DEVELOPMENT.md**: دليل المساهمة في التطوير

### **الإبلاغ عن مشاكل**
1. اجمع معلومات المشكلة (نظام التشغيل، رسالة الخطأ)
2. أرفق لقطات شاشة
3. اذكر خطوات إعادة المشكلة
4. تواصل مع فريق الدعم

### **معلومات الاتصال**
- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: https://cinemahub-pro.com
- **GitHub**: https://github.com/cinemahub/desktop-app

---

## 🎉 **مبروك!**

الآن لديك برنامج CinemaHub Pro Desktop جاهز للاستخدام!

**نصائح للبداية:**
1. ابدأ بإضافة بعض الأفلام والمسلسلات
2. اربط البرنامج بمجلد موقعك
3. فعل المزامنة التلقائية
4. استكشف جميع الميزات المتاحة

**استمتع بإدارة موقع الأفلام والمسلسلات بسهولة!** 🍿🎬
