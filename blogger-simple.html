<!-- 
==============================================
CinemaHub - قالب بلوجر للأفلام والمسلسلات
==============================================
تعليمات التطبيق:
1. اذهب إلى blogger.com
2. اختر Theme > Edit HTML
3. احذف الكود الموجود
4. انسخ هذا الكود والصقه
5. احفظ القالب
==============================================
-->

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html b:version='2' class='v2' expr:dir='data:blog.languageDirection' xmlns='http://www.w3.org/1999/xhtml' xmlns:b='http://www.google.com/2005/gml/b' xmlns:data='http://www.google.com/2005/gml/data' xmlns:expr='http://www.google.com/2005/gml/expr'>
<head>
    <meta charset='UTF-8'/>
    <meta content='width=device-width, initial-scale=1.0' name='viewport'/>

    <b:if cond='data:view.isHomepage'>
        <title>CinemaHub - سينما هاب | شاهد أحدث الأفلام والمسلسلات مجاناً</title>
    <b:else/>
        <title><data:view.title/> - CinemaHub سينما هاب</title>
    </b:if>

    <meta content='موقعك الأول لمشاهدة أحدث الأفلام والمسلسلات العربية والأجنبية بجودة عالية ومجاناً' name='description'/>
    <meta content='أفلام, مسلسلات, مشاهدة اونلاين, تحميل أفلام, أفلام عربية, مسلسلات تركية' name='keywords'/>
    
    <!-- External CSS -->
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'/>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'/>
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&amp;display=swap' rel='stylesheet'/>

    <b:skin><![CDATA[
    /* ===== Custom Styles ===== */
    :root {
        --primary-color: #e50914;
        --secondary-color: #221f1f;
        --dark-color: #141414;
        --light-color: #ffffff;
        --gray-color: #757575;
    }
    
    * { margin: 0; padding: 0; box-sizing: border-box; }
    
    body {
        font-family: 'Cairo', sans-serif;
        background: var(--dark-color);
        color: var(--light-color);
        line-height: 1.6;
        direction: rtl;
    }
    
    /* Header */
    .header {
        background: rgba(20, 20, 20, 0.95);
        backdrop-filter: blur(10px);
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 1000;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .navbar { padding: 0.5rem 0; }
    
    .navbar-brand {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: var(--light-color) !important;
        transition: all 0.3s ease;
    }
    
    .navbar-brand:hover { transform: scale(1.05); }
    
    .logo-container {
        display: flex;
        align-items: center;
        gap: 12px;
    }
    
    .logo-icon {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 45px;
        height: 45px;
        background: linear-gradient(135deg, #e50914 0%, #b20710 100%);
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
        animation: logoGlow 3s ease-in-out infinite alternate;
    }
    
    .logo-icon .fa-film {
        font-size: 20px;
        color: var(--light-color);
        z-index: 2;
    }
    
    .logo-play {
        position: absolute;
        top: -5px;
        right: -5px;
        font-size: 16px !important;
        color: #ffd700;
        animation: playPulse 2s ease-in-out infinite;
        z-index: 3;
    }
    
    .brand-text {
        font-size: 18px;
        font-weight: 700;
        background: linear-gradient(135deg, #e50914 0%, #b20710 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .brand-subtitle {
        font-size: 12px;
        color: var(--gray-color);
        margin-top: -2px;
    }
    
    /* Main Content */
    .main-content {
        margin-top: 80px;
        padding: 2rem 0;
        min-height: 100vh;
    }
    
    /* Blog Posts */
    .blog-posts {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 2rem;
        margin: 2rem 0;
    }
    
    .post-card {
        background: var(--secondary-color);
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }
    
    .post-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    }
    
    .post-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
    }
    
    .post-content { padding: 1rem; }
    
    .post-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--light-color);
    }
    
    .post-title a {
        color: inherit;
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .post-title a:hover { color: var(--primary-color); }
    
    .post-meta {
        font-size: 12px;
        color: var(--gray-color);
        margin-bottom: 0.5rem;
    }
    
    .post-snippet {
        font-size: 14px;
        color: var(--gray-color);
        line-height: 1.6;
    }
    
    /* Footer */
    .footer {
        background: linear-gradient(135deg, #141414 0%, #000000 100%);
        padding: 2rem 0;
        text-align: center;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .footer p {
        color: var(--gray-color);
        font-size: 14px;
        margin: 0;
    }
    
    /* Animations */
    @keyframes logoGlow {
        0% { box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3); }
        100% { box-shadow: 0 6px 25px rgba(229, 9, 20, 0.6); }
    }
    
    @keyframes playPulse {
        0%, 100% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.2); opacity: 0.8; }
    }
    
    /* Responsive */
    @media (max-width: 768px) {
        .logo-container { gap: 8px; }
        .logo-icon { width: 35px; height: 35px; }
        .logo-icon .fa-film { font-size: 16px; }
        .brand-text { font-size: 16px; }
        .blog-posts { grid-template-columns: 1fr; gap: 1rem; }
    }
    
    @media (max-width: 576px) {
        .logo-icon { width: 30px; height: 30px; }
        .logo-icon .fa-film { font-size: 14px; }
        .brand-text { font-size: 14px; }
        .brand-subtitle { display: none; }
    }
    ]]></b:skin>
</head>

<body>
    <!-- Header -->
    <header class='header'>
        <nav class='navbar'>
            <div class='container'>
                <a class='navbar-brand' expr:href='data:blog.homepageUrl'>
                    <div class='logo-container'>
                        <div class='logo-icon'>
                            <i class='fas fa-film'></i>
                            <i class='fas fa-play-circle logo-play'></i>
                        </div>
                        <div class='logo-text'>
                            <div class='brand-text'>CinemaHub</div>
                            <div class='brand-subtitle'>سينما هاب</div>
                        </div>
                    </div>
                </a>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class='main-content'>
        <div class='container'>
            <!-- Homepage Welcome -->
            <b:if cond='data:view.isHomepage'>
                <div class='text-center mb-5' style='padding: 3rem 0; background: linear-gradient(135deg, #141414 0%, #000000 100%); border-radius: 8px; margin-bottom: 2rem;'>
                    <h1 style='font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem; background: linear-gradient(135deg, #e50914 0%, #b20710 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;'>مرحباً بك في CinemaHub</h1>
                    <p style='font-size: 1.2rem; color: #757575; margin-bottom: 2rem;'>موقعك الأول لمشاهدة أحدث الأفلام والمسلسلات العربية والأجنبية</p>
                    <div style='display: flex; justify-content: center; gap: 1rem;'>
                        <i class='fas fa-film' style='color: #e50914; font-size: 2rem;'></i>
                        <i class='fas fa-tv' style='color: #e50914; font-size: 2rem;'></i>
                        <i class='fas fa-star' style='color: #ffd700; font-size: 2rem;'></i>
                    </div>
                </div>
            </b:if>

            <!-- Blog Posts Section -->
            <b:section class='main' id='main' maxwidgets='1' showaddelement='no'>
                <b:widget id='Blog1' locked='true' title='Blog Posts' type='Blog' version='1' visible='true'>
                    <b:includable id='main'>
                        <div class='blog-posts'>
                            <b:loop values='data:posts' var='post'>
                                <article class='post-card'>
                                    <!-- Post Image -->
                                    <b:if cond='data:post.featuredImage'>
                                        <img class='post-image' expr:alt='data:post.title' expr:src='data:post.featuredImage'/>
                                    <b:else/>
                                        <div class='post-image' style='background: linear-gradient(135deg, #e50914 0%, #b20710 100%); display: flex; align-items: center; justify-content: center;'>
                                            <i class='fas fa-film' style='font-size: 3rem; color: white;'></i>
                                        </div>
                                    </b:if>

                                    <!-- Post Content -->
                                    <div class='post-content'>
                                        <h2 class='post-title'>
                                            <a expr:href='data:post.link' expr:title='data:post.title'>
                                                <data:post.title/>
                                            </a>
                                        </h2>
                                        <div class='post-meta'>
                                            <i class='fas fa-calendar'></i> <data:post.dateHeader/>
                                            <span style='margin: 0 0.5rem;'>|</span>
                                            <i class='fas fa-user'></i> <data:post.author/>
                                        </div>
                                        <div class='post-snippet'>
                                            <data:post.snippet/>
                                        </div>
                                    </div>
                                </article>
                            </b:loop>
                        </div>

                        <!-- Pagination -->
                        <div class='text-center mt-5'>
                            <b:include name='nextprev'/>
                        </div>
                    </b:includable>
                </b:widget>
            </b:section>
        </div>
    </main>

    <!-- Footer -->
    <footer class='footer'>
        <div class='container'>
            <div class='row'>
                <div class='col-md-6'>
                    <div class='d-flex align-items-center justify-content-center justify-content-md-start mb-3'>
                        <div class='logo-icon me-3' style='width: 40px; height: 40px; font-size: 16px;'>
                            <i class='fas fa-film'></i>
                        </div>
                        <div>
                            <div class='brand-text' style='font-size: 16px;'>CinemaHub</div>
                            <div class='brand-subtitle'>سينما هاب</div>
                        </div>
                    </div>
                </div>
                <div class='col-md-6 text-center text-md-end'>
                    <p>© 2024 CinemaHub - سينما هاب. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
    
    <script>
    // Header scroll effect
    window.addEventListener('scroll', function() {
        const header = document.querySelector('.header');
        if (window.scrollY > 100) {
            header.style.background = 'rgba(20, 20, 20, 0.98)';
        } else {
            header.style.background = 'rgba(20, 20, 20, 0.95)';
        }
    });
    
    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
        });
    });
    </script>
</body>
</html>
