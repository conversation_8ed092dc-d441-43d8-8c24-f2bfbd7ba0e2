# CinemaHub - موقع الأفلام والمسلسلات العربية

## 🎬 نظرة عامة
CinemaHub هو موقع ويب حديث ومتجاوب لعرض الأفلام والمسلسلات العربية والأجنبية. يتميز الموقع بتصميم أنيق باللونين الأحمر والأسود مع واجهة مستخدم سهلة الاستخدام.

## ✨ المميزات الرئيسية

### 🎨 التصميم
- تصميم متجاوب يعمل على جميع الأجهزة
- ألوان أحمر وأسود أنيقة
- خطوط عربية جميلة (Cairo Font)
- تأثيرات بصرية متقدمة
- واجهة مستخدم حديثة

### 🔍 الوظائف
- صفحة رئيسية تفاعلية
- صفحة تفاصيل الأفلام
- صفحة المسلسلات مع فلاتر
- نظام بحث متقدم
- قوائم منسدلة للتصنيفات
- أزرار تسجيل الدخول والتسجيل

### 📱 التوافق
- متوافق مع جميع المتصفحات الحديثة
- تصميم متجاوب للهواتف والأجهزة اللوحية
- سرعة تحميل عالية
- تحسين محركات البحث (SEO)

## 📁 هيكل الملفات

```
CinemaHub/
├── index.html          # الصفحة الرئيسية
├── movie.html          # صفحة تفاصيل الفيلم
├── series.html         # صفحة المسلسلات
├── style.css           # ملف الأنماط الرئيسي
├── script.js           # ملف JavaScript
└── README.md           # دليل المستخدم
```

## 🚀 كيفية الاستخدام

### 1. التشغيل المحلي
```bash
# افتح الملفات في متصفح الويب
# أو استخدم خادم محلي
python -m http.server 8000
# ثم اذهب إلى http://localhost:8000
```

### 2. النشر على الويب
- ارفع جميع الملفات إلى خادم الويب
- تأكد من أن index.html هو الملف الرئيسي
- تحقق من أن جميع الروابط تعمل بشكل صحيح

## 🎯 الصفحات المتاحة

### 🏠 الصفحة الرئيسية (index.html)
- عرض الأفلام المميزة
- تصنيفات الأفلام والمسلسلات
- شريط بحث تفاعلي
- قسم البطل (Hero Section)

### 🎬 صفحة تفاصيل الفيلم (movie.html)
- معلومات مفصلة عن الفيلم
- طاقم التمثيل والإخراج
- الإعلان التشويقي
- أفلام مشابهة
- أزرار المشاهدة والتحميل

### 📺 صفحة المسلسلات (series.html)
- عرض جميع المسلسلات
- فلاتر التصنيف
- معلومات كل مسلسل
- نظام الصفحات (Pagination)

## 🛠️ التخصيص

### تغيير الألوان
```css
/* في ملف style.css */
:root {
    --primary-color: #dc2626;    /* الأحمر الرئيسي */
    --secondary-color: #1a1a1a;  /* الأسود */
    --text-color: #ffffff;       /* النص الأبيض */
}
```

### إضافة محتوى جديد
1. افتح الملف المطلوب (HTML)
2. ابحث عن القسم المناسب
3. أضف المحتوى الجديد
4. احفظ الملف

### تعديل الأنماط
1. افتح ملف style.css
2. ابحث عن الكلاس المطلوب
3. عدل الخصائص حسب الحاجة
4. احفظ الملف

## 📋 المتطلبات

### المتطلبات الأساسية
- متصفح ويب حديث
- اتصال بالإنترنت (للخطوط والأيقونات)

### المكتبات المستخدمة
- Font Awesome 6.0.0 (الأيقونات)
- Google Fonts - Cairo (الخطوط العربية)

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### الخطوط لا تظهر بشكل صحيح
```html
<!-- تأكد من وجود هذا الرابط في <head> -->
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
```

#### الأيقونات لا تظهر
```html
<!-- تأكد من وجود هذا الرابط في <head> -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
```

#### مشاكل التجاوب
- تحقق من وجود viewport meta tag
- تأكد من استخدام CSS Grid و Flexbox بشكل صحيح

## 🎨 دليل الألوان

| اللون | الكود | الاستخدام |
|-------|-------|----------|
| الأحمر الرئيسي | #dc2626 | الأزرار والعناوين |
| الأسود | #1a1a1a | الخلفية الرئيسية |
| الأبيض | #ffffff | النصوص |
| الرمادي الفاتح | #cccccc | النصوص الثانوية |
| الذهبي | #fbbf24 | التقييمات |

## 📱 التوافق مع الأجهزة

### الهواتف الذكية
- iPhone (جميع الأحجام)
- Android (جميع الأحجام)
- تصميم متجاوب كامل

### الأجهزة اللوحية
- iPad
- Android Tablets
- تخطيط محسن للشاشات المتوسطة

### أجهزة الكمبيوتر
- Desktop (1920px+)
- Laptop (1366px+)
- تخطيط كامل مع جميع المميزات

## 🔄 التحديثات المستقبلية

### المميزات المخططة
- [ ] نظام تسجيل المستخدمين
- [ ] قاعدة بيانات للأفلام
- [ ] نظام التقييمات
- [ ] التعليقات والمراجعات
- [ ] قائمة المفضلة
- [ ] إشعارات الأفلام الجديدة

### التحسينات التقنية
- [ ] تحسين الأداء
- [ ] إضافة PWA
- [ ] تحسين SEO
- [ ] إضافة اختبارات تلقائية

## 📞 الدعم والمساعدة

إذا واجهت أي مشاكل أو لديك اقتراحات:
1. تحقق من دليل استكشاف الأخطاء
2. راجع الكود المصدري
3. تأكد من التحديثات

## 📄 الترخيص

هذا المشروع مفتوح المصدر ويمكن استخدامه لأغراض تعليمية وتجارية.

---

**CinemaHub** - موقعك المفضل للأفلام والمسلسلات العربية 🎬
