@echo off
chcp 65001 >nul
title CinemaHub Pro Desktop - Build Tool

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                CinemaHub Pro Desktop Builder                 ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 خطوة 1: التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    echo.
    echo 📥 يرجى تحميل Node.js من: https://nodejs.org/
    echo 💡 اختر النسخة LTS الموصى بها
    echo.
    goto :error
)
echo ✅ Node.js مثبت

echo.
echo 🔍 خطوة 2: التحقق من الأيقونة...
if not exist "assets\icon.png" (
    echo ❌ الأيقونة غير موجودة!
    echo.
    echo 📋 لإنشاء الأيقونة:
    echo 1. افتح assets\download-icon.html
    echo 2. اضغط "تحميل الأيقونة"
    echo 3. احفظها باسم icon.png في مجلد assets
    echo.
    goto :error
)
echo ✅ الأيقونة موجودة

echo.
echo 🔍 خطوة 3: تثبيت التبعيات...
if not exist "node_modules" (
    echo 📦 جاري تثبيت التبعيات...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل تثبيت التبعيات!
        goto :error
    )
)
echo ✅ التبعيات جاهزة

echo.
echo 🔍 خطوة 4: بناء البرنامج...
echo ⏳ هذا قد يستغرق 5-10 دقائق...
echo.

npm run build-win
if %errorlevel% neq 0 (
    echo ❌ فشل البناء!
    goto :error
)

echo.
echo 🎉 تم البناء بنجاح!
echo.

if exist "dist\*.exe" (
    echo 📁 ملفات التثبيت:
    for %%f in (dist\*.exe) do echo   📄 %%~nxf
    echo.
    echo ✅ جاهز للتوزيع!
) else (
    echo ⚠️  لم يتم إنشاء ملفات التثبيت
)

echo.
echo اضغط أي زر للخروج...
pause >nul
exit /b 0

:error
echo.
echo ❌ حدث خطأ! يرجى إصلاح المشكلة وإعادة المحاولة.
echo.
echo اضغط أي زر للخروج...
pause >nul
exit /b 1
