<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html b:version='2' class='v2' expr:dir='data:blog.languageDirection' xmlns='http://www.w3.org/1999/xhtml' xmlns:b='http://www.google.com/2005/gml/b' xmlns:data='http://www.google.com/2005/gml/data' xmlns:expr='http://www.google.com/2005/gml/expr'>
<head>
    <meta charset='UTF-8'/>
    <meta content='width=device-width, initial-scale=1.0' name='viewport'/>
    
    <!-- Title and Meta -->
    <b:if cond='data:view.isHomepage'>
        <title>CinemaHub - سينما هاب | شاهد أحدث الأفلام والمسلسلات مجاناً</title>
    <b:else/>
        <title><data:view.title/> - CinemaHub سينما هاب</title>
    </b:if>
    
    <meta content='موقعك الأول لمشاهدة أحدث الأفلام والمسلسلات العربية والأجنبية بجودة عالية ومجاناً' name='description'/>
    <meta content='أفلام, مسلسلات, مشاهدة اونلاين, تحميل أفلام, أفلام عربية, مسلسلات تركية, CinemaHub' name='keywords'/>
    
    <!-- Favicon -->
    <link href='data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgcng9IjIwIiBmaWxsPSIjZTUwOTE0Ii8+PHRleHQgeD0iNTAiIHk9IjY1IiBmb250LXNpemU9IjYwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSI+8J+OrDwvdGV4dD48L3N2Zz4=' rel='icon'/>
    
    <!-- External CSS -->
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'/>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'/>
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&amp;display=swap' rel='stylesheet'/>
    
    <!-- Custom CSS -->
    <b:skin><![CDATA[
    /* ===== CSS Variables ===== */
    :root {
        --primary-color: #e50914;
        --secondary-color: #221f1f;
        --dark-color: #141414;
        --light-color: #ffffff;
        --gray-color: #757575;
        --success-color: #28a745;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
        --info-color: #17a2b8;
        
        --gradient-primary: linear-gradient(135deg, #e50914 0%, #b20710 100%);
        --gradient-dark: linear-gradient(135deg, #141414 0%, #000000 100%);
        
        --font-family: 'Cairo', sans-serif;
        --font-size-base: 14px;
        --font-size-small: 12px;
        --font-size-large: 16px;
        --font-size-xl: 18px;
        --border-radius: 8px;
        --transition: all 0.3s ease;
    }

    /* ===== Base Styles ===== */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    html {
        scroll-behavior: smooth;
        font-size: 16px;
    }

    body {
        font-family: var(--font-family);
        background-color: var(--dark-color);
        color: var(--light-color);
        line-height: 1.6;
        overflow-x: hidden;
        font-size: var(--font-size-base);
        direction: rtl;
    }

    /* ===== Header Styles ===== */
    .header {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        background: rgba(20, 20, 20, 0.95);
        backdrop-filter: blur(10px);
        z-index: 1000;
        transition: var(--transition);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .header.scrolled {
        background: rgba(20, 20, 20, 0.98);
        box-shadow: 0 2px 20px rgba(0,0,0,0.3);
    }

    .navbar {
        padding: 0.5rem 0;
    }

    .navbar-brand {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: var(--light-color) !important;
        transition: var(--transition);
    }

    .navbar-brand:hover {
        transform: scale(1.05);
        color: var(--light-color) !important;
    }

    .logo-container {
        display: flex;
        align-items: center;
        gap: 12px;
        animation: logoEntrance 1s ease-out;
    }

    .logo-icon {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 45px;
        height: 45px;
        background: var(--gradient-primary);
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
        animation: logoGlow 3s ease-in-out infinite alternate;
    }

    .logo-icon .fa-film {
        font-size: 20px;
        color: var(--light-color);
        z-index: 2;
    }

    .logo-play {
        position: absolute;
        top: -5px;
        right: -5px;
        font-size: 16px !important;
        color: #ffd700;
        animation: playPulse 2s ease-in-out infinite;
        z-index: 3;
    }

    .logo-text {
        display: flex;
        flex-direction: column;
        line-height: 1.2;
    }

    .brand-text {
        font-size: var(--font-size-xl);
        font-weight: 700;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin: 0;
        letter-spacing: -0.5px;
    }

    .brand-subtitle {
        font-size: var(--font-size-small);
        color: var(--gray-color);
        font-weight: 400;
        margin-top: -2px;
    }

    /* ===== Animations ===== */
    @keyframes logoGlow {
        0% { box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3); }
        100% { box-shadow: 0 6px 25px rgba(229, 9, 20, 0.6); }
    }

    @keyframes playPulse {
        0%, 100% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.2); opacity: 0.8; }
    }

    @keyframes logoEntrance {
        0% { opacity: 0; transform: translateY(-20px) scale(0.8); }
        50% { opacity: 0.7; transform: translateY(-5px) scale(1.1); }
        100% { opacity: 1; transform: translateY(0) scale(1); }
    }

    /* ===== Main Content ===== */
    .main-content {
        margin-top: 80px;
        padding: 2rem 0;
        min-height: 100vh;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    /* ===== Welcome Section ===== */
    .welcome-section {
        text-align: center;
        padding: 3rem 0;
        background: var(--gradient-dark);
        border-radius: var(--border-radius);
        margin-bottom: 2rem;
    }

    .welcome-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .welcome-subtitle {
        font-size: 1.2rem;
        color: var(--gray-color);
        margin-bottom: 2rem;
    }

    /* ===== Blog Posts ===== */
    .blog-posts {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 2rem;
        margin: 2rem 0;
    }

    .post-card {
        background: var(--secondary-color);
        border-radius: var(--border-radius);
        overflow: hidden;
        transition: var(--transition);
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .post-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(229, 9, 20, 0.2);
        border-color: var(--primary-color);
    }

    .post-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
        transition: var(--transition);
    }

    .post-card:hover .post-image {
        transform: scale(1.05);
    }

    .post-placeholder {
        width: 100%;
        height: 200px;
        background: var(--gradient-primary);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--light-color);
        font-size: 3rem;
    }

    .post-content {
        padding: 1.5rem;
    }

    .post-title {
        font-size: var(--font-size-large);
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--light-color);
        line-height: 1.4;
    }

    .post-title a {
        color: inherit;
        text-decoration: none;
        transition: var(--transition);
    }

    .post-title a:hover {
        color: var(--primary-color);
    }

    .post-meta {
        font-size: var(--font-size-small);
        color: var(--gray-color);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .post-meta i {
        color: var(--primary-color);
    }

    .post-snippet {
        font-size: var(--font-size-small);
        color: var(--gray-color);
        line-height: 1.6;
    }

    /* ===== Footer ===== */
    .footer {
        background: var(--gradient-dark);
        padding: 2rem 0;
        margin-top: 3rem;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .footer-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
    }

    .footer-logo .logo-icon {
        width: 40px;
        height: 40px;
        margin-left: 1rem;
    }

    .footer-text {
        text-align: center;
        color: var(--gray-color);
        font-size: var(--font-size-small);
    }

    /* ===== Responsive ===== */
    @media (max-width: 768px) {
        .logo-container { gap: 8px; }
        .logo-icon { width: 35px; height: 35px; border-radius: 8px; }
        .logo-icon .fa-film { font-size: 16px; }
        .logo-play { font-size: 12px !important; top: -3px; right: -3px; }
        .brand-text { font-size: var(--font-size-base); }
        .brand-subtitle { font-size: 10px; }
        .blog-posts { grid-template-columns: 1fr; gap: 1rem; }
        .welcome-title { font-size: 2rem; }
        .welcome-subtitle { font-size: 1rem; }
    }

    @media (max-width: 576px) {
        .logo-container { gap: 6px; }
        .logo-icon { width: 30px; height: 30px; border-radius: 6px; }
        .logo-icon .fa-film { font-size: 14px; }
        .logo-play { font-size: 10px !important; top: -2px; right: -2px; }
        .brand-text { font-size: 12px; }
        .brand-subtitle { display: none; }
        .welcome-section { padding: 2rem 0; }
        .welcome-title { font-size: 1.5rem; }
    }
    ]]></b:skin>
</head>

<body>
    <!-- Header -->
    <header class='header' id='header'>
        <nav class='navbar'>
            <div class='container'>
                <a class='navbar-brand' expr:href='data:blog.homepageUrl'>
                    <div class='logo-container'>
                        <div class='logo-icon'>
                            <i class='fas fa-film'></i>
                            <i class='fas fa-play-circle logo-play'></i>
                        </div>
                        <div class='logo-text'>
                            <div class='brand-text'>CinemaHub</div>
                            <div class='brand-subtitle'>سينما هاب</div>
                        </div>
                    </div>
                </a>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class='main-content'>
        <div class='container'>
            <!-- Welcome Section for Homepage -->
            <b:if cond='data:view.isHomepage'>
                <section class='welcome-section'>
                    <h1 class='welcome-title'>مرحباً بك في CinemaHub</h1>
                    <p class='welcome-subtitle'>موقعك الأول لمشاهدة أحدث الأفلام والمسلسلات العربية والأجنبية</p>
                    <div class='d-flex justify-content-center gap-3'>
                        <i class='fas fa-film text-primary fa-2x'></i>
                        <i class='fas fa-tv text-primary fa-2x'></i>
                        <i class='fas fa-star text-warning fa-2x'></i>
                    </div>
                </section>
            </b:if>
            
            <!-- Blog Posts Section -->
            <b:section class='main' id='main' maxwidgets='1' showaddelement='no'>
                <b:widget id='Blog1' locked='true' title='Blog Posts' type='Blog' version='1' visible='true'>
                    <b:includable id='main'>
                        <div class='blog-posts'>
                            <b:loop values='data:posts' var='post'>
                                <article class='post-card'>
                                    <!-- Post Image -->
                                    <b:if cond='data:post.featuredImage'>
                                        <img class='post-image' expr:alt='data:post.title' expr:src='data:post.featuredImage'/>
                                    <b:else/>
                                        <div class='post-placeholder'>
                                            <i class='fas fa-film'></i>
                                        </div>
                                    </b:if>
                                    
                                    <!-- Post Content -->
                                    <div class='post-content'>
                                        <h2 class='post-title'>
                                            <a expr:href='data:post.link' expr:title='data:post.title'>
                                                <data:post.title/>
                                            </a>
                                        </h2>
                                        <div class='post-meta'>
                                            <i class='fas fa-calendar'></i>
                                            <span><data:post.dateHeader/></span>
                                            <span>|</span>
                                            <i class='fas fa-user'></i>
                                            <span><data:post.author/></span>
                                        </div>
                                        <div class='post-snippet'>
                                            <data:post.snippet/>
                                        </div>
                                    </div>
                                </article>
                            </b:loop>
                        </div>
                        
                        <!-- Pagination -->
                        <b:include name='nextprev'/>
                    </b:includable>
                </b:widget>
            </b:section>
        </div>
    </main>

    <!-- Footer -->
    <footer class='footer'>
        <div class='container'>
            <div class='footer-logo'>
                <div class='logo-icon'>
                    <i class='fas fa-film'></i>
                </div>
                <div class='logo-text'>
                    <div class='brand-text'>CinemaHub</div>
                    <div class='brand-subtitle'>سينما هاب</div>
                </div>
            </div>
            <div class='footer-text'>
                <p>© 2024 CinemaHub - سينما هاب. جميع الحقوق محفوظة.</p>
                <p>موقعك الأول لمشاهدة أحدث الأفلام والمسلسلات</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
    
    <script>
    //<![CDATA[
    // Header scroll effect
    window.addEventListener('scroll', function() {
        const header = document.getElementById('header');
        if (window.scrollY > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });
    
    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // Add loading animation to images
    document.querySelectorAll('.post-image').forEach(img => {
        img.addEventListener('load', function() {
            this.style.opacity = '1';
        });
        img.style.opacity = '0';
        img.style.transition = 'opacity 0.3s ease';
    });
    //]]>
    </script>
</body>
</html>
