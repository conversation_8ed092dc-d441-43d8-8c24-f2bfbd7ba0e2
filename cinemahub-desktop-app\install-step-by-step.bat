@echo off
title CinemaHub Pro - Step by Step Installer

:start
cls
echo ========================================
echo   CinemaHub Pro - Step by Step
echo ========================================
echo.
echo Choose an option:
echo.
echo 1. Check if Node.js is installed
echo 2. Install dependencies (npm install)
echo 3. Check icon file
echo 4. Build installer
echo 5. Open dist folder
echo 6. Exit
echo.
set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto check_node
if "%choice%"=="2" goto install_deps
if "%choice%"=="3" goto check_icon
if "%choice%"=="4" goto build_installer
if "%choice%"=="5" goto open_dist
if "%choice%"=="6" goto exit
goto start

:check_node
cls
echo Checking Node.js...
echo.
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo SUCCESS: Node.js is installed
    node --version
    echo.
    echo You can proceed to step 2
) else (
    echo ERROR: Node.js is NOT installed
    echo.
    echo Please:
    echo 1. Go to https://nodejs.org/
    echo 2. Download LTS version
    echo 3. Install it
    echo 4. Restart computer
    echo 5. Come back and try again
)
echo.
echo Press any key to return to menu...
pause >nul
goto start

:install_deps
cls
echo Installing dependencies...
echo.
if not exist "package.json" (
    echo ERROR: package.json not found
    echo Make sure you are in the correct folder
    echo.
    echo Press any key to return to menu...
    pause >nul
    goto start
)

echo Running: npm install
echo This may take a few minutes...
echo.
npm install
if %errorlevel% equ 0 (
    echo.
    echo SUCCESS: Dependencies installed
) else (
    echo.
    echo ERROR: Failed to install dependencies
    echo Check your internet connection
)
echo.
echo Press any key to return to menu...
pause >nul
goto start

:check_icon
cls
echo Checking icon file...
echo.
if exist "assets\icon.png" (
    echo SUCCESS: Icon file found at assets\icon.png
    echo You can proceed to build
) else (
    echo ERROR: Icon file missing
    echo.
    echo To create icon:
    echo 1. Go to assets folder
    echo 2. Open download-icon.html in browser
    echo 3. Click download button
    echo 4. Save as icon.png in assets folder
    echo.
    echo Or use any PNG image (512x512 pixels)
    echo and rename it to icon.png
)
echo.
echo Press any key to return to menu...
pause >nul
goto start

:build_installer
cls
echo Building installer...
echo.

REM Check prerequisites
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    echo Please install Node.js first (option 1)
    echo.
    echo Press any key to return to menu...
    pause >nul
    goto start
)

if not exist "assets\icon.png" (
    echo ERROR: Icon file missing
    echo Please create icon first (option 3)
    echo.
    echo Press any key to return to menu...
    pause >nul
    goto start
)

if not exist "node_modules" (
    echo ERROR: Dependencies not installed
    echo Please install dependencies first (option 2)
    echo.
    echo Press any key to return to menu...
    pause >nul
    goto start
)

echo All prerequisites OK
echo.
echo Starting build process...
echo This will take 5-10 minutes
echo DO NOT CLOSE THIS WINDOW!
echo.

npm run build-win
if %errorlevel% equ 0 (
    echo.
    echo SUCCESS: Build completed!
    echo.
    if exist "dist\*.exe" (
        echo Installer files created:
        dir dist\*.exe /b
        echo.
        echo Files are in the 'dist' folder
    ) else (
        echo WARNING: No .exe files found
    )
) else (
    echo.
    echo ERROR: Build failed
    echo Check internet connection and try again
)

echo.
echo Press any key to return to menu...
pause >nul
goto start

:open_dist
cls
echo Opening dist folder...
if exist "dist" (
    explorer dist
    echo Dist folder opened in Windows Explorer
) else (
    echo Dist folder does not exist yet
    echo You need to build first (option 4)
)
echo.
echo Press any key to return to menu...
pause >nul
goto start

:exit
echo Goodbye!
exit /b 0
