/**
 * ESLint Configuration for CinemaHub Pro Desktop
 * 
 * This configuration ensures code quality and consistency
 * across the entire project.
 */

module.exports = {
    // Environment settings
    env: {
        browser: true,      // Browser global variables
        es2021: true,       // ES2021 globals
        node: true,         // Node.js global variables
        jquery: true,       // jQuery global variables
        commonjs: true      // CommonJS global variables
    },

    // Extend recommended configurations
    extends: [
        'eslint:recommended'
    ],

    // Parser options
    parserOptions: {
        ecmaVersion: 2021,      // ECMAScript version
        sourceType: 'module'    // Module type
    },

    // Global variables
    globals: {
        // Electron globals
        require: 'readonly',
        module: 'readonly',
        exports: 'readonly',
        process: 'readonly',
        __dirname: 'readonly',
        __filename: 'readonly',
        
        // Browser globals
        window: 'readonly',
        document: 'readonly',
        console: 'readonly',
        
        // Bootstrap globals
        bootstrap: 'readonly',
        
        // Application globals
        app: 'writable',
        modalManager: 'writable',
        syncManager: 'writable',
        dbManager: 'writable'
    },

    // Custom rules
    rules: {
        // ===== Possible Errors =====
        'no-console': 'off',                    // Allow console statements
        'no-debugger': 'warn',                  // Warn about debugger statements
        'no-alert': 'warn',                     // Warn about alert statements
        'no-unused-vars': ['error', {           // Error on unused variables
            'vars': 'all',
            'args': 'after-used',
            'ignoreRestSiblings': false,
            'argsIgnorePattern': '^_'           // Ignore args starting with _
        }],
        'no-undef': 'error',                    // Error on undefined variables
        'no-unreachable': 'error',              // Error on unreachable code

        // ===== Best Practices =====
        'curly': ['error', 'all'],              // Require curly braces
        'eqeqeq': ['error', 'always'],          // Require === and !==
        'no-eval': 'error',                     // Disallow eval()
        'no-implied-eval': 'error',             // Disallow implied eval()
        'no-new-func': 'error',                 // Disallow new Function()
        'no-return-assign': 'error',            // Disallow assignment in return
        'no-self-compare': 'error',             // Disallow self comparison
        'no-throw-literal': 'error',            // Disallow throwing literals
        'no-unused-expressions': 'error',       // Disallow unused expressions
        'no-useless-call': 'error',             // Disallow useless call()
        'no-useless-concat': 'error',           // Disallow useless concatenation
        'no-void': 'error',                     // Disallow void operator
        'no-with': 'error',                     // Disallow with statements
        'radix': 'error',                       // Require radix in parseInt()
        'wrap-iife': ['error', 'inside'],       // Wrap IIFEs

        // ===== Variables =====
        'no-delete-var': 'error',               // Disallow deleting variables
        'no-label-var': 'error',                // Disallow labels with variables
        'no-shadow': 'warn',                    // Warn about variable shadowing
        'no-shadow-restricted-names': 'error',  // Disallow shadowing restricted names
        'no-undef-init': 'error',               // Disallow initializing to undefined
        'no-use-before-define': ['error', {     // Disallow use before definition
            'functions': false,
            'classes': true,
            'variables': true
        }],

        // ===== Stylistic Issues =====
        'indent': ['error', 4, {                // 4-space indentation
            'SwitchCase': 1,
            'VariableDeclarator': 1,
            'outerIIFEBody': 1,
            'MemberExpression': 1,
            'FunctionDeclaration': {
                'parameters': 1,
                'body': 1
            },
            'FunctionExpression': {
                'parameters': 1,
                'body': 1
            }
        }],
        'linebreak-style': ['error', 'unix'],   // Unix line endings
        'quotes': ['error', 'single', {         // Single quotes
            'avoidEscape': true,
            'allowTemplateLiterals': true
        }],
        'semi': ['error', 'always'],            // Require semicolons
        'semi-spacing': ['error', {             // Semicolon spacing
            'before': false,
            'after': true
        }],
        'comma-dangle': ['error', 'never'],     // No trailing commas
        'comma-spacing': ['error', {            // Comma spacing
            'before': false,
            'after': true
        }],
        'comma-style': ['error', 'last'],       // Comma style
        'brace-style': ['error', '1tbs', {      // Brace style
            'allowSingleLine': true
        }],
        'block-spacing': ['error', 'always'],   // Block spacing
        'keyword-spacing': ['error', {          // Keyword spacing
            'before': true,
            'after': true
        }],
        'space-before-blocks': ['error', 'always'], // Space before blocks
        'space-before-function-paren': ['error', { // Space before function paren
            'anonymous': 'never',
            'named': 'never',
            'asyncArrow': 'always'
        }],
        'space-in-parens': ['error', 'never'],  // No space in parentheses
        'space-infix-ops': 'error',             // Space around operators
        'space-unary-ops': ['error', {          // Space around unary operators
            'words': true,
            'nonwords': false
        }],
        'spaced-comment': ['error', 'always'],  // Space in comments
        'key-spacing': ['error', {              // Key spacing in objects
            'beforeColon': false,
            'afterColon': true
        }],
        'object-curly-spacing': ['error', 'always'], // Space in object braces
        'array-bracket-spacing': ['error', 'never'], // No space in array brackets

        // ===== ECMAScript 6 =====
        'arrow-spacing': ['error', {            // Arrow function spacing
            'before': true,
            'after': true
        }],
        'constructor-super': 'error',           // Require super() in constructors
        'no-class-assign': 'error',             // Disallow class assignment
        'no-const-assign': 'error',             // Disallow const assignment
        'no-dupe-class-members': 'error',       // Disallow duplicate class members
        'no-duplicate-imports': 'error',        // Disallow duplicate imports
        'no-new-symbol': 'error',               // Disallow new Symbol()
        'no-this-before-super': 'error',        // Disallow this before super()
        'no-useless-computed-key': 'error',     // Disallow useless computed keys
        'no-useless-constructor': 'error',      // Disallow useless constructors
        'no-useless-rename': 'error',           // Disallow useless renaming
        'no-var': 'error',                      // Require let/const
        'prefer-const': ['error', {             // Prefer const
            'destructuring': 'any',
            'ignoreReadBeforeAssign': false
        }],
        'prefer-arrow-callback': 'warn',        // Prefer arrow callbacks
        'prefer-template': 'warn',              // Prefer template literals
        'template-curly-spacing': ['error', 'never'], // No space in template literals

        // ===== Custom Rules for Arabic Content =====
        'no-irregular-whitespace': 'off',       // Allow Arabic text spacing

        // ===== Async/Await =====
        'require-await': 'warn',                // Warn about async without await
        'no-async-promise-executor': 'error',   // Disallow async promise executors
        'no-await-in-loop': 'warn',             // Warn about await in loops
        'no-promise-executor-return': 'error',  // Disallow return in promise executor

        // ===== Node.js specific =====
        'no-process-exit': 'warn',              // Warn about process.exit()
        'no-path-concat': 'error',              // Disallow path concatenation

        // ===== Security =====
        'no-eval': 'error',                     // Disallow eval()
        'no-implied-eval': 'error',             // Disallow implied eval()
        'no-new-func': 'error',                 // Disallow Function constructor
        'no-script-url': 'error'                // Disallow script URLs
    },

    // Override rules for specific files
    overrides: [
        {
            // Main process files
            files: ['main.js', 'src/main/**/*.js'],
            env: {
                browser: false,
                node: true
            },
            rules: {
                'no-console': 'off'             // Allow console in main process
            }
        },
        {
            // Renderer process files
            files: ['src/**/*.js', '!src/main/**/*.js'],
            env: {
                browser: true,
                node: false
            },
            globals: {
                require: 'readonly',
                module: 'readonly',
                exports: 'readonly'
            }
        },
        {
            // Test files
            files: ['**/*.test.js', '**/*.spec.js', 'tests/**/*.js'],
            env: {
                jest: true,
                mocha: true
            },
            rules: {
                'no-unused-expressions': 'off'  // Allow chai expressions
            }
        },
        {
            // Configuration files
            files: ['.eslintrc.js', 'webpack.config.js', 'jest.config.js'],
            env: {
                node: true
            },
            rules: {
                'no-console': 'off'
            }
        }
    ],

    // Ignore patterns
    ignorePatterns: [
        'node_modules/',
        'dist/',
        'build/',
        'coverage/',
        '*.min.js',
        'vendor/',
        'assets/'
    ]
};
