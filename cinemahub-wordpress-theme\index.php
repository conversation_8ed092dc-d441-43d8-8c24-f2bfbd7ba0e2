<?php
/**
 * The main template file
 *
 * @package CinemaHub Pro
 */

get_header(); ?>

<div style="margin-top: 80px; padding: 2rem; text-align: center; color: white; min-height: 70vh;">
    <h1 style="font-size: 3rem; margin-bottom: 2rem; background: linear-gradient(135deg, #dc2626 0%, #fbbf24 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
        مرحباً بك في CinemaHub Pro
    </h1>

    <p style="font-size: 1.2rem; color: #94a3b8; margin-bottom: 3rem; max-width: 600px; margin-left: auto; margin-right: auto;">
        الموقع يعمل بنجاح! يمكنك الآن إضافة المحتوى والبدء في استخدام الموقع.
    </p>

    <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap; margin-bottom: 3rem;">
        <a href="<?php echo admin_url('post-new.php?post_type=movie'); ?>" style="background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); color: white; padding: 1rem 2rem; border-radius: 25px; text-decoration: none; font-weight: 600; display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-plus"></i>
            إضافة فيلم جديد
        </a>
        <a href="<?php echo admin_url('post-new.php?post_type=series'); ?>" style="background: #1e293b; color: white; padding: 1rem 2rem; border-radius: 25px; text-decoration: none; font-weight: 600; display: flex; align-items: center; gap: 0.5rem; border: 2px solid #334155;">
            <i class="fas fa-plus"></i>
            إضافة مسلسل جديد
        </a>
    </div>

    <div style="background: #1e293b; padding: 2rem; border-radius: 15px; margin: 2rem auto; max-width: 800px; text-align: right;">
        <h3 style="color: #dc2626; margin-bottom: 1rem; text-align: center;">
            <i class="fas fa-info-circle"></i>
            خطوات البدء السريع
        </h3>

        <ol style="color: #cbd5e1; line-height: 1.8; padding-right: 1rem;">
            <li>أضف أول فيلم أو مسلسل باستخدام الأزرار أعلاه</li>
            <li>ارفع صورة البوستر للحصول على مظهر أفضل</li>
            <li>املأ التفاصيل مثل التقييم وسنة الإنتاج</li>
            <li>انشر المحتوى وشاهد النتيجة في الصفحة الرئيسية</li>
            <li>كرر العملية لإضافة المزيد من المحتوى</li>
        </ol>
    </div>

    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-top: 3rem; max-width: 800px; margin-left: auto; margin-right: auto;">
        <div style="background: #0f172a; padding: 1.5rem; border-radius: 15px; border: 2px solid #334155;">
            <i class="fas fa-film" style="font-size: 2rem; color: #dc2626; margin-bottom: 1rem;"></i>
            <h4 style="color: white; margin-bottom: 0.5rem;">إدارة الأفلام</h4>
            <p style="color: #94a3b8; font-size: 0.9rem;">أضف وأدر مجموعة أفلامك بسهولة</p>
        </div>

        <div style="background: #0f172a; padding: 1.5rem; border-radius: 15px; border: 2px solid #334155;">
            <i class="fas fa-tv" style="font-size: 2rem; color: #dc2626; margin-bottom: 1rem;"></i>
            <h4 style="color: white; margin-bottom: 0.5rem;">إدارة المسلسلات</h4>
            <p style="color: #94a3b8; font-size: 0.9rem;">نظم مسلسلاتك مع تفاصيل المواسم والحلقات</p>
        </div>

        <div style="background: #0f172a; padding: 1.5rem; border-radius: 15px; border: 2px solid #334155;">
            <i class="fas fa-search" style="font-size: 2rem; color: #dc2626; margin-bottom: 1rem;"></i>
            <h4 style="color: white; margin-bottom: 0.5rem;">بحث متقدم</h4>
            <p style="color: #94a3b8; font-size: 0.9rem;">ابحث في المحتوى بسهولة وسرعة</p>
        </div>
    </div>
</div>

<?php get_footer(); ?>