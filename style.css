/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d1b1b 100%);
    color: #ffffff;
    line-height: 1.6;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    border-bottom: 2px solid #dc2626;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
    gap: 2rem;
}

.logo h1 {
    color: #dc2626;
    font-size: 1.8rem;
    font-weight: 700;
}

.logo i {
    margin-left: 0.5rem;
}

.nav {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex: 1;
    justify-content: center;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.nav-link:hover {
    background: #dc2626;
    color: #ffffff;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid #dc2626;
    border-radius: 8px;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu a {
    display: block;
    padding: 0.75rem 1rem;
    color: #ffffff;
    text-decoration: none;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(220, 38, 38, 0.2);
}

.dropdown-menu a:last-child {
    border-bottom: none;
}

.dropdown-menu a:hover {
    background: #dc2626;
    color: #ffffff;
}

.auth-buttons {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 0.5rem 1.5rem;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #dc2626;
    color: #ffffff;
}

.btn-primary:hover {
    background: #b91c1c;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: #ffffff;
    border: 2px solid #dc2626;
}

.btn-outline:hover {
    background: #dc2626;
    color: #ffffff;
}

/* Hero Section */
.hero {
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), 
                url('https://via.placeholder.com/1920x800/1a1a1a/ffffff?text=CinemaHub+Background') center/cover;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin-top: 80px;
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: #cccccc;
}

.search-box {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 50px;
    overflow: hidden;
    border: 2px solid #dc2626;
}

.search-box input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    background: transparent;
    color: #ffffff;
    font-size: 1rem;
    outline: none;
}

.search-box input::placeholder {
    color: #cccccc;
}

.search-box button {
    padding: 1rem 1.5rem;
    background: #dc2626;
    border: none;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-box button:hover {
    background: #b91c1c;
}

/* Categories */
.categories {
    padding: 4rem 0;
    background: rgba(0, 0, 0, 0.3);
}

.categories h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #dc2626;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.category-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(220, 38, 38, 0.3);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.category-card:hover {
    transform: translateY(-10px);
    border-color: #dc2626;
    background: rgba(220, 38, 38, 0.1);
}

.category-card i {
    font-size: 3rem;
    color: #dc2626;
    margin-bottom: 1rem;
}

.category-card h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #ffffff;
}

.category-card p {
    color: #cccccc;
    font-size: 0.9rem;
}

/* Featured Movies */
.featured {
    padding: 4rem 0;
}

.featured h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #dc2626;
}

.movies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.movie-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(220, 38, 38, 0.3);
}

.movie-card:hover {
    transform: translateY(-10px);
    border-color: #dc2626;
}

.movie-poster {
    position: relative;
    overflow: hidden;
}

.movie-poster img {
    width: 100%;
    height: 350px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.movie-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.movie-card:hover .movie-overlay {
    opacity: 1;
}

.play-btn {
    background: #dc2626;
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    color: #ffffff;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-btn:hover {
    background: #b91c1c;
    transform: scale(1.1);
}

.movie-info {
    padding: 1.5rem;
}

.movie-info h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: #ffffff;
}

.movie-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.year {
    color: #cccccc;
    font-size: 0.9rem;
}

.rating {
    color: #fbbf24;
    font-size: 0.9rem;
}

.rating i {
    margin-left: 0.25rem;
}

/* Footer */
.footer {
    background: rgba(0, 0, 0, 0.9);
    padding: 3rem 0 1rem;
    border-top: 2px solid #dc2626;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    color: #dc2626;
    margin-bottom: 1rem;
}

.footer-section p {
    color: #cccccc;
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #cccccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #dc2626;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: #dc2626;
    color: #ffffff;
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #b91c1c;
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(220, 38, 38, 0.3);
    color: #cccccc;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav {
        flex-direction: column;
        gap: 1rem;
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .category-grid {
        grid-template-columns: 1fr;
    }
    
    .movies-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}
