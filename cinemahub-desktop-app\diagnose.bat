@echo off
title CinemaHub Pro - Diagnosis

echo ========================================
echo   CinemaHub Pro - System Diagnosis
echo ========================================
echo.

echo Checking all requirements...
echo.

REM Check Node.js
echo 1. Node.js:
node --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('node --version') do echo   Version: %%i - OK
) else (
    echo   Status: NOT INSTALLED
    echo   Solution: Download from https://nodejs.org/
)

echo.

REM Check npm
echo 2. npm:
npm --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('npm --version') do echo   Version: %%i - OK
) else (
    echo   Status: NOT AVAILABLE
)

echo.

REM Check current directory
echo 3. Current directory:
echo   %cd%
if exist "main.js" (
    echo   main.js: FOUND
) else (
    echo   main.js: MISSING - You are in wrong folder!
)

if exist "package.json" (
    echo   package.json: FOUND
) else (
    echo   package.json: MISSING
)

if exist "src\index.html" (
    echo   src\index.html: FOUND
) else (
    echo   src\index.html: MISSING
)

echo.

REM Check assets
echo 4. Assets:
if exist "assets\icon.png" (
    echo   icon.png: FOUND
) else (
    echo   icon.png: MISSING
)

if exist "assets\icon.ico" (
    echo   icon.ico: FOUND
) else (
    echo   icon.ico: MISSING
)

echo.

REM Check dependencies
echo 5. Dependencies:
if exist "node_modules" (
    echo   node_modules: FOUND
    if exist "node_modules\electron" (
        echo   electron: INSTALLED
    ) else (
        echo   electron: MISSING
    )
    if exist "node_modules\electron-builder" (
        echo   electron-builder: INSTALLED
    ) else (
        echo   electron-builder: MISSING
    )
) else (
    echo   node_modules: NOT FOUND
    echo   Solution: Run 'npm install'
)

echo.

REM Check disk space
echo 6. Disk space:
for /f "tokens=3" %%a in ('dir /-c ^| find "bytes free"') do (
    set /a freespace=%%a/1024/1024
    echo   Free space: !freespace! MB
    if !freespace! LSS 2048 (
        echo   WARNING: Low disk space! Need at least 2GB
    )
)

echo.

REM Check internet
echo 7. Internet connection:
ping google.com -n 1 >nul 2>&1
if %errorlevel% equ 0 (
    echo   Status: CONNECTED
) else (
    echo   Status: NO CONNECTION
    echo   Warning: Internet needed for building
)

echo.

REM Check Windows version
echo 8. Windows version:
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo   Version: %VERSION%

echo.

REM Summary
echo ========================================
echo              SUMMARY
echo ========================================

set /a issues=0

node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo - Node.js is missing
    set /a issues+=1
)

if not exist "main.js" (
    echo - Wrong directory or missing files
    set /a issues+=1
)

if not exist "assets\icon.png" (
    echo - Icon file is missing
    set /a issues+=1
)

if not exist "node_modules" (
    echo - Dependencies not installed
    set /a issues+=1
)

if %issues% equ 0 (
    echo.
    echo STATUS: READY TO BUILD!
    echo.
    echo Next steps:
    echo 1. Run: fix-and-build.bat
    echo 2. Wait 5-10 minutes
    echo 3. Check dist folder for installer
) else (
    echo.
    echo STATUS: %issues% ISSUES FOUND
    echo.
    echo Please fix the issues above first:
    echo.
    if not exist "main.js" (
        echo - Make sure you are in the cinemahub-desktop-app folder
    )
    node --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo - Install Node.js from https://nodejs.org/
    )
    if not exist "assets\icon.png" (
        echo - Open assets\download-icon.html to create icon
    )
    if not exist "node_modules" (
        echo - Run: npm install
    )
)

echo.
echo Press any key to exit...
pause >nul
