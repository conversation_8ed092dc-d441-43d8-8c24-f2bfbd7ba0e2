<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="أفلام الرعب - CinemaHub Pro | شاهد أفضل أفلام الرعب والإثارة بجودة عالية">
    <meta name="keywords" content="أفلام رعب, أفلام إثارة, أفلام مخيفة, هرور, رعب نفسي">
    <title>أفلام الرعب - CinemaHub Pro | سينما هاب برو</title>
    
    <!-- External CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
    /* ===== CSS Variables ===== */
    :root {
        --primary-color: #e50914;
        --primary-dark: #b20710;
        --primary-light: #ff1e2d;
        --secondary-color: #221f1f;
        --dark-color: #141414;
        --darker-color: #0a0a0a;
        --light-color: #ffffff;
        --gray-color: #757575;
        --gray-light: #b3b3b3;
        --gray-dark: #333333;
        --horror-color: #8b0000;
        --horror-light: #dc143c;
        
        --gradient-primary: linear-gradient(135deg, #e50914 0%, #b20710 100%);
        --gradient-horror: linear-gradient(135deg, #8b0000 0%, #dc143c 100%);
        --gradient-dark: linear-gradient(135deg, #141414 0%, #0a0a0a 100%);
        --gradient-overlay: linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.9) 100%);
        
        --font-family: 'Cairo', sans-serif;
        --border-radius: 0.5rem;
        --border-radius-lg: 0.75rem;
        --border-radius-xl: 1rem;
        --border-radius-2xl: 1.5rem;
        --border-radius-3xl: 2rem;
        
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        --shadow-glow: 0 0 20px rgba(139, 0, 0, 0.3);
        
        --transition: all 0.3s ease;
        --z-fixed: 1030;
    }

    /* ===== Base Styles ===== */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    html {
        scroll-behavior: smooth;
        font-size: 16px;
    }

    body {
        font-family: var(--font-family);
        background: var(--dark-color);
        color: var(--light-color);
        line-height: 1.6;
        direction: rtl;
        overflow-x: hidden;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* ===== Header ===== */
    .header {
        background: rgba(20, 20, 20, 0.95);
        backdrop-filter: blur(20px);
        position: fixed;
        top: 0;
        width: 100%;
        z-index: var(--z-fixed);
        transition: var(--transition);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .navbar {
        padding: 1rem 0;
    }

    .navbar-brand {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: var(--light-color) !important;
        transition: var(--transition);
    }

    .logo-container {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .logo-icon {
        position: relative;
        width: 60px;
        height: 60px;
        background: var(--gradient-primary);
        border-radius: var(--border-radius-xl);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: var(--transition);
    }

    .logo-icon .fa-film {
        font-size: 1.8rem;
        color: var(--light-color);
    }

    .logo-play {
        position: absolute;
        top: -8px;
        right: -8px;
        font-size: 1.4rem;
        color: #ffd700;
    }

    .logo-text {
        display: flex;
        flex-direction: column;
    }

    .brand-text {
        font-size: 1.5rem;
        font-weight: 800;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        line-height: 1;
    }

    .brand-subtitle {
        font-size: 0.875rem;
        color: var(--gray-color);
        margin-top: -2px;
        font-weight: 500;
    }

    /* Navigation */
    .navbar-nav .nav-link {
        color: var(--light-color) !important;
        font-weight: 600;
        margin: 0 0.75rem;
        padding: 0.75rem 1.25rem !important;
        border-radius: var(--border-radius-lg);
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .navbar-nav .nav-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: var(--gradient-primary);
        transition: var(--transition);
        z-index: -1;
    }

    .navbar-nav .nav-link:hover::before,
    .navbar-nav .nav-link.active::before {
        left: 0;
    }

    .navbar-nav .nav-link:hover,
    .navbar-nav .nav-link.active {
        color: var(--light-color) !important;
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .navbar-nav .nav-link i {
        margin-left: 0.5rem;
    }

    /* Dropdown Menu */
    .dropdown-menu {
        background: var(--secondary-color);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: var(--border-radius-xl);
        box-shadow: var(--shadow-2xl);
        backdrop-filter: blur(20px);
        padding: 1rem;
        margin-top: 0.5rem;
        min-width: 250px;
    }

    .dropdown-item {
        color: var(--light-color);
        padding: 0.75rem 1rem;
        border-radius: var(--border-radius-lg);
        transition: var(--transition);
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.25rem;
    }

    .dropdown-item:hover {
        background: var(--gradient-primary);
        color: var(--light-color);
        transform: translateX(-5px);
    }

    .dropdown-item i {
        width: 20px;
        text-align: center;
    }

    /* Search Form */
    .search-form {
        margin-right: 1rem;
    }

    .search-container {
        position: relative;
        display: flex;
        align-items: center;
    }

    .search-input {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        color: var(--light-color);
        border-radius: var(--border-radius-3xl);
        padding: 0.875rem 3.5rem 0.875rem 1.25rem;
        width: 350px;
        transition: var(--transition);
        font-weight: 500;
    }

    .search-input:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: var(--primary-color);
        color: var(--light-color);
        outline: none;
        box-shadow: 0 0 0 0.25rem rgba(229, 9, 20, 0.25);
        width: 400px;
    }

    .search-input::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .search-btn {
        position: absolute;
        left: 10px;
        background: var(--gradient-primary);
        border: none;
        color: var(--light-color);
        padding: 0.75rem;
        border-radius: 50%;
        transition: var(--transition);
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
    }

    .search-btn:hover {
        transform: scale(1.1);
        box-shadow: var(--shadow-lg);
    }

    /* Mobile Navigation */
    .navbar-toggler {
        border: none;
        padding: 0.75rem;
        border-radius: var(--border-radius-lg);
        background: rgba(255, 255, 255, 0.1);
    }

    .navbar-toggler:focus {
        box-shadow: none;
    }

    .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    }

    @media (max-width: 991.98px) {
        .navbar-collapse {
            background: var(--secondary-color);
            border-radius: var(--border-radius-xl);
            margin-top: 1rem;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-xl);
        }

        .search-input {
            width: 100%;
        }

        .search-input:focus {
            width: 100%;
        }

        .search-form {
            margin-right: 0;
            margin-top: 1rem;
        }

        .logo-text .brand-subtitle {
            display: none;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
        }

        .brand-text {
            font-size: 1.25rem;
        }

        .navbar-nav .nav-link {
            margin: 0.25rem 0;
        }
    }
    </style>
</head>

<body>
