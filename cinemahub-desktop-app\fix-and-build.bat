@echo off
title CinemaHub Pro - Fix and Build

echo ========================================
echo   CinemaHub Pro - Fix and Build
echo ========================================
echo.

echo Step 1: Checking system...
echo.

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found!
    echo Please install from: https://nodejs.org/
    echo.
    pause
    exit /b 1
)
echo OK: Node.js found

REM Check npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm not found!
    pause
    exit /b 1
)
echo OK: npm found

echo.
echo Step 2: Fixing package.json...

REM Create a simplified package.json for building
echo Creating build configuration...

(
echo {
echo   "name": "cinemahub-pro-desktop",
echo   "version": "1.0.0",
echo   "description": "CinemaHub Pro Desktop Application",
echo   "main": "main.js",
echo   "scripts": {
echo     "start": "electron .",
echo     "build": "electron-builder --win"
echo   },
echo   "devDependencies": {
echo     "electron": "^27.0.0",
echo     "electron-builder": "^24.6.4"
echo   },
echo   "dependencies": {
echo     "axios": "^1.5.0",
echo     "express": "^4.18.2",
echo     "sqlite3": "^5.1.6",
echo     "fs-extra": "^11.1.1",
echo     "chokidar": "^3.5.3",
echo     "electron-store": "^8.1.0"
echo   },
echo   "build": {
echo     "appId": "com.cinemahub.desktop",
echo     "productName": "CinemaHub Pro Desktop",
echo     "directories": {
echo       "output": "dist"
echo     },
echo     "files": [
echo       "main.js",
echo       "src/**/*",
echo       "assets/icon.png",
echo       "package.json"
echo     ],
echo     "win": {
echo       "target": "nsis",
echo       "icon": "assets/icon.png"
echo     },
echo     "nsis": {
echo       "oneClick": false,
echo       "allowToChangeInstallationDirectory": true,
echo       "createDesktopShortcut": true,
echo       "createStartMenuShortcut": true
echo     }
echo   }
echo }
) > package-simple.json

echo OK: Build configuration created

echo.
echo Step 3: Installing/updating dependencies...
echo This may take a few minutes...
echo.

npm install --package-lock-only
if %errorlevel% neq 0 (
    echo Trying alternative installation...
    npm install --no-package-lock
)

echo.
echo Step 4: Building installer...
echo This will take 5-10 minutes...
echo Please be patient and do not close this window!
echo.

REM Use the simplified package.json for building
npx electron-builder --config package-simple.json --win
if %errorlevel% neq 0 (
    echo.
    echo Build failed. Trying alternative method...
    echo.
    
    REM Try with basic configuration
    npx electron-builder --win --config.appId=com.cinemahub.desktop --config.productName="CinemaHub Pro Desktop" --config.directories.output=dist --config.win.target=nsis --config.win.icon=assets/icon.png
    
    if %errorlevel% neq 0 (
        echo.
        echo ERROR: Build failed!
        echo.
        echo Possible solutions:
        echo 1. Check internet connection
        echo 2. Free up disk space (need 2GB+)
        echo 3. Disable antivirus temporarily
        echo 4. Run as administrator
        echo.
        pause
        exit /b 1
    )
)

echo.
echo SUCCESS: Build completed!
echo.

if exist "dist\*.exe" (
    echo Installer files created:
    for %%f in (dist\*.exe) do (
        echo   %%~nxf
    )
    echo.
    echo Files location: %cd%\dist
    echo.
    echo You can now distribute these files to users!
) else (
    echo WARNING: No installer files found
    echo Check the dist folder manually
)

echo.
echo Opening dist folder...
if exist "dist" (
    explorer dist
)

echo.
echo ========================================
echo           BUILD COMPLETE!
echo ========================================
echo.
echo Press any key to exit...
pause >nul
