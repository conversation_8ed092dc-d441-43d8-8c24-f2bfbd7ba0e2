<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحميل أيقونة CinemaHub Pro</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            text-align: center;
            max-width: 600px;
        }
        
        h1 {
            color: #dc2626;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }
        
        .icon-preview {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            display: inline-block;
        }
        
        #iconImage {
            width: 256px;
            height: 256px;
            border: 2px solid #dc2626;
            border-radius: 10px;
            background: white;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
            border: none;
            padding: 20px 40px;
            border-radius: 15px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px;
            box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
            text-decoration: none;
            display: inline-block;
        }
        
        .download-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(220, 38, 38, 0.5);
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.05);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: right;
            line-height: 1.8;
        }
        
        .step {
            margin: 15px 0;
            padding: 15px;
            background: rgba(220, 38, 38, 0.1);
            border-radius: 10px;
            border-right: 4px solid #dc2626;
            font-size: 16px;
        }
        
        .highlight {
            background: rgba(34, 197, 94, 0.2);
            border-color: #22c55e;
            color: #22c55e;
            font-weight: bold;
        }
        
        code {
            background: rgba(0, 0, 0, 0.3);
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #fbbf24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 أيقونة CinemaHub Pro Desktop</h1>
        
        <div class="icon-preview">
            <canvas id="iconCanvas" width="512" height="512" style="width: 256px; height: 256px; border: 2px solid #dc2626; border-radius: 10px; background: white;"></canvas>
        </div>
        
        <a href="#" id="downloadLink" class="download-btn" onclick="downloadIcon()">
            💾 تحميل الأيقونة (icon.png)
        </a>
        
        <div class="instructions">
            <h3>📋 خطوات التثبيت:</h3>
            
            <div class="step highlight">
                <strong>1.</strong> اضغط على زر "تحميل الأيقونة" أعلاه
            </div>
            
            <div class="step">
                <strong>2.</strong> احفظ الملف باسم <code>icon.png</code> في مجلد <code>assets</code> داخل مجلد البرنامج
            </div>
            
            <div class="step">
                <strong>3.</strong> تأكد من أن المسار يكون:
                <br><code>cinemahub-desktop-app/assets/icon.png</code>
            </div>
            
            <div class="step">
                <strong>4.</strong> بعد حفظ الأيقونة، شغل ملف:
                <br><code>create-installer.bat</code> (على Windows)
                <br>أو <code>./create-installer.sh</code> (على Linux/macOS)
            </div>
            
            <div class="step">
                <strong>5.</strong> انتظر انتهاء عملية البناء (5-10 دقائق)
            </div>
            
            <div class="step highlight">
                <strong>6.</strong> ستجد ملف التثبيت في مجلد <code>dist/</code>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: rgba(255, 255, 255, 0.05); border-radius: 15px;">
            <h4 style="color: #fbbf24;">💡 نصائح مهمة:</h4>
            <ul style="text-align: right; line-height: 1.6;">
                <li>تأكد من تثبيت Node.js قبل تشغيل السكريبت</li>
                <li>تأكد من وجود اتصال إنترنت أثناء البناء</li>
                <li>لا تغلق النافذة أثناء عملية البناء</li>
                <li>إذا فشلت العملية، جرب مرة أخرى</li>
            </ul>
        </div>
    </div>

    <script>
        // رسم الأيقونة على Canvas
        function drawIcon() {
            const canvas = document.getElementById('iconCanvas');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, 512, 512);
            
            // Background circle with gradient
            const gradient = ctx.createRadialGradient(256, 256, 0, 256, 256, 256);
            gradient.addColorStop(0, '#dc2626');
            gradient.addColorStop(1, '#b91c1c');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(256, 256, 240, 0, 2 * Math.PI);
            ctx.fill();
            
            // Border
            ctx.strokeStyle = '#991b1b';
            ctx.lineWidth = 8;
            ctx.stroke();
            
            // Film strip background
            ctx.fillStyle = '#1e293b';
            ctx.fillRect(80, 180, 352, 152);
            
            // Film strip gradient overlay
            const filmGradient = ctx.createLinearGradient(0, 180, 0, 332);
            filmGradient.addColorStop(0, '#374151');
            filmGradient.addColorStop(1, '#1e293b');
            ctx.fillStyle = filmGradient;
            ctx.fillRect(80, 180, 352, 152);
            
            // Film strip border
            ctx.strokeStyle = '#0f172a';
            ctx.lineWidth = 4;
            ctx.strokeRect(80, 180, 352, 152);
            
            // Film holes (left side)
            ctx.fillStyle = '#ffffff';
            for (let i = 0; i < 3; i++) {
                ctx.fillRect(100, 200 + i * 40, 20, 20);
            }
            
            // Film holes (right side)
            for (let i = 0; i < 3; i++) {
                ctx.fillRect(392, 200 + i * 40, 20, 20);
            }
            
            // Film frames
            ctx.fillStyle = '#4b5563';
            ctx.strokeStyle = '#374151';
            ctx.lineWidth = 2;
            
            const framePositions = [
                [140, 200], [240, 200], [340, 200],
                [140, 272], [240, 272], [340, 272]
            ];
            
            framePositions.forEach(([x, y]) => {
                ctx.fillRect(x, y, 80, 60);
                ctx.strokeRect(x, y, 80, 60);
            });
            
            // Play button shadow
            ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
            ctx.beginPath();
            ctx.arc(258, 258, 50, 0, 2 * Math.PI);
            ctx.fill();
            
            // Play button background
            ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
            ctx.beginPath();
            ctx.arc(256, 256, 50, 0, 2 * Math.PI);
            ctx.fill();
            
            // Play button triangle
            ctx.fillStyle = '#dc2626';
            ctx.beginPath();
            ctx.moveTo(240, 230);
            ctx.lineTo(240, 282);
            ctx.lineTo(290, 256);
            ctx.closePath();
            ctx.fill();
            
            // Sparkle effects
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            const sparkles = [
                [150, 150], [350, 150], [150, 350], [350, 350]
            ];
            
            sparkles.forEach(([x, y]) => {
                ctx.beginPath();
                ctx.arc(x, y, 3, 0, 2 * Math.PI);
                ctx.fill();
            });
            
            // Text shadow
            ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.font = 'bold 36px Arial, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText('CinemaHub', 258, 122);
            
            ctx.font = '24px Arial, sans-serif';
            ctx.fillText('Pro Desktop', 258, 422);
            
            // Main text
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 36px Arial, sans-serif';
            ctx.fillText('CinemaHub', 256, 120);
            
            ctx.font = '24px Arial, sans-serif';
            ctx.fillText('Pro Desktop', 256, 420);
        }
        
        function downloadIcon() {
            const canvas = document.getElementById('iconCanvas');
            const link = document.createElement('a');
            link.download = 'icon.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
            
            // Show success message
            const successDiv = document.createElement('div');
            successDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                font-weight: bold;
                box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
                z-index: 1000;
                animation: slideIn 0.5s ease;
            `;
            successDiv.innerHTML = '✅ تم تحميل icon.png بنجاح!';
            document.body.appendChild(successDiv);
            
            setTimeout(() => {
                successDiv.remove();
            }, 3000);
        }
        
        // رسم الأيقونة عند تحميل الصفحة
        window.onload = function() {
            drawIcon();
        };
    </script>
    
    <style>
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    </style>
</body>
</html>
