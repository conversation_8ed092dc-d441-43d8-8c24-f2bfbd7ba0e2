<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="أفلام الرعب - CinemaHub Pro | شاهد أفضل أفلام الرعب والإثارة بجودة عالية">
    <meta name="keywords" content="أفلام رعب, أفلام إثارة, أفلام مخيفة, هرور, رعب نفسي">
    <title>أفلام الرعب - CinemaHub Pro | سينما هاب برو</title>
    
    <!-- External CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
    /* ===== CSS Variables ===== */
    :root {
        --primary-color: #e50914;
        --horror-color: #8b0000;
        --horror-light: #dc143c;
        --horror-dark: #4b0000;
        --dark-color: #141414;
        --darker-color: #0a0a0a;
        --light-color: #ffffff;
        --gray-color: #757575;
        --gray-light: #b3b3b3;
        --secondary-color: #221f1f;
        
        --gradient-primary: linear-gradient(135deg, #e50914 0%, #b20710 100%);
        --gradient-horror: linear-gradient(135deg, #8b0000 0%, #dc143c 100%);
        --gradient-horror-dark: linear-gradient(135deg, #4b0000 0%, #8b0000 100%);
        --gradient-dark: linear-gradient(135deg, #141414 0%, #0a0a0a 100%);
        
        --font-family: 'Cairo', sans-serif;
        --border-radius-lg: 0.75rem;
        --border-radius-xl: 1rem;
        --border-radius-2xl: 1.5rem;
        
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        --shadow-horror: 0 0 30px rgba(139, 0, 0, 0.5);
        
        --transition: all 0.3s ease;
        --z-fixed: 1030;
    }

    /* ===== Base Styles ===== */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: var(--font-family);
        background: var(--dark-color);
        color: var(--light-color);
        line-height: 1.6;
        direction: rtl;
        overflow-x: hidden;
    }

    /* ===== Header ===== */
    .header {
        background: rgba(20, 20, 20, 0.95);
        backdrop-filter: blur(20px);
        position: fixed;
        top: 0;
        width: 100%;
        z-index: var(--z-fixed);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        padding: 1rem 0;
    }

    .navbar-brand {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: var(--light-color) !important;
    }

    .logo-container {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .logo-icon {
        width: 50px;
        height: 50px;
        background: var(--gradient-primary);
        border-radius: var(--border-radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .logo-icon .fa-film {
        font-size: 1.5rem;
        color: var(--light-color);
    }

    .brand-text {
        font-size: 1.25rem;
        font-weight: 800;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .back-btn {
        background: var(--gradient-horror);
        color: var(--light-color);
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: var(--border-radius-2xl);
        font-weight: 600;
        transition: var(--transition);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
    }

    .back-btn:hover {
        background: var(--gradient-horror-dark);
        color: var(--light-color);
        transform: translateY(-2px);
        box-shadow: var(--shadow-horror);
    }

    /* ===== Horror Header ===== */
    .horror-header {
        background: linear-gradient(45deg, rgba(139,0,0,0.8) 0%, rgba(0,0,0,0.9) 100%), 
                    url('https://images.unsplash.com/photo-1520637736862-4d197d17c90a?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center/cover;
        height: 60vh;
        display: flex;
        align-items: center;
        margin-top: 80px;
        position: relative;
    }

    .horror-header-content {
        text-align: center;
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
    }

    .horror-title {
        font-size: 2.8rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 3px 3px 10px rgba(0,0,0,0.8);
        background: var(--gradient-horror);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: glow 2s ease-in-out infinite alternate;
    }

    .horror-subtitle {
        font-size: 1.1rem;
        margin-bottom: 1.8rem;
        opacity: 0.9;
        text-shadow: 2px 2px 6px rgba(0,0,0,0.8);
    }

    .horror-stats {
        display: flex;
        justify-content: center;
        gap: 3rem;
        flex-wrap: wrap;
    }

    .stat-item {
        text-align: center;
        background: rgba(139, 0, 0, 0.2);
        padding: 1.5rem 2rem;
        border-radius: var(--border-radius-2xl);
        border: 2px solid rgba(220, 20, 60, 0.3);
        backdrop-filter: blur(10px);
        transition: var(--transition);
    }

    .stat-item:hover {
        background: rgba(139, 0, 0, 0.4);
        border-color: var(--horror-light);
        transform: translateY(-5px);
        box-shadow: var(--shadow-horror);
    }

    .stat-number {
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--horror-light);
        display: block;
        margin-bottom: 0.4rem;
    }

    .stat-label {
        font-size: 0.85rem;
        color: var(--light-color);
        font-weight: 500;
    }

    /* ===== Movies Section ===== */
    .movies-section {
        padding: 4rem 0;
        background: var(--dark-color);
    }

    .section-title {
        font-size: 2.5rem;
        font-weight: 800;
        text-align: center;
        margin-bottom: 3rem;
        color: var(--light-color);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .section-title i {
        color: var(--horror-color);
        animation: float 3s ease-in-out infinite;
    }

    .movies-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 2rem;
    }

    .movie-card {
        background: var(--secondary-color);
        border-radius: var(--border-radius-2xl);
        overflow: hidden;
        transition: var(--transition);
        cursor: pointer;
        border: 2px solid rgba(139, 0, 0, 0.3);
        position: relative;
        box-shadow: var(--shadow-lg);
    }

    .movie-card:hover {
        transform: translateY(-15px) scale(1.03);
        box-shadow: var(--shadow-horror);
        border-color: var(--horror-light);
    }

    .movie-poster {
        position: relative;
        height: 350px;
        overflow: hidden;
    }

    .movie-poster img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: all 0.5s ease;
        filter: brightness(0.8) contrast(1.1);
    }

    .movie-card:hover .movie-poster img {
        transform: scale(1.1);
        filter: brightness(0.6) contrast(1.2);
    }

    .movie-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(139,0,0,0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: var(--transition);
    }

    .movie-card:hover .movie-overlay {
        opacity: 1;
    }

    .play-btn {
        background: var(--gradient-horror);
        color: var(--light-color);
        border: none;
        width: 70px;
        height: 70px;
        border-radius: 50%;
        font-size: 1.8rem;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: var(--transition);
        cursor: pointer;
        box-shadow: var(--shadow-xl);
    }

    .play-btn:hover {
        transform: scale(1.2);
        box-shadow: var(--shadow-horror);
    }

    .horror-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: var(--gradient-horror);
        color: var(--light-color);
        padding: 0.5rem 1rem;
        border-radius: var(--border-radius-lg);
        font-size: 0.875rem;
        font-weight: 700;
        text-transform: uppercase;
    }

    .movie-info {
        padding: 1.5rem;
    }

    .movie-title {
        font-size: 1.125rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: var(--light-color);
        line-height: 1.3;
    }

    .movie-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.875rem;
        color: var(--gray-color);
    }

    .movie-year {
        background: rgba(139, 0, 0, 0.2);
        padding: 0.25rem 0.75rem;
        border-radius: var(--border-radius-lg);
        font-weight: 600;
        border: 1px solid rgba(220, 20, 60, 0.3);
    }

    .movie-rating {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-weight: 600;
    }

    .movie-rating i {
        color: #ffd700;
    }

    /* ===== Animations ===== */
    @keyframes glow {
        0% {
            text-shadow: 0 0 10px rgba(220, 20, 60, 0.5);
        }
        100% {
            text-shadow: 0 0 20px rgba(220, 20, 60, 0.8), 0 0 30px rgba(220, 20, 60, 0.6);
        }
    }

    @keyframes float {
        0%, 100% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-10px);
        }
    }

    /* ===== Responsive ===== */
    @media (max-width: 768px) {
        .horror-title {
            font-size: 2.5rem;
        }

        .horror-stats {
            gap: 1.5rem;
        }

        .movies-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .movie-poster {
            height: 280px;
        }
    }

    @media (max-width: 576px) {
        .horror-title {
            font-size: 2rem;
        }

        .movies-grid {
            gap: 0.75rem;
        }

        .movie-poster {
            height: 220px;
        }
    }
    </style>
</head>

<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <a class="navbar-brand" href="index-pro.html">
                    <div class="logo-container">
                        <div class="logo-icon">
                            <i class="fas fa-film"></i>
                        </div>
                        <span class="brand-text">CinemaHub Pro</span>
                    </div>
                </a>

                <a href="index-pro.html" class="back-btn">
                    <i class="fas fa-arrow-right"></i>
                    العودة للرئيسية
                </a>
            </div>
        </div>
    </header>

    <!-- Horror Header -->
    <section class="horror-header">
        <div class="horror-header-content">
            <h1 class="horror-title">أفلام الرعب</h1>
            <p class="horror-subtitle">استعد لتجربة مرعبة لا تُنسى مع أفضل أفلام الرعب والإثارة</p>
            <div class="horror-stats">
                <div class="stat-item">
                    <span class="stat-number">65+</span>
                    <span class="stat-label">فيلم رعب</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">4K</span>
                    <span class="stat-label">جودة عالية</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">24/7</span>
                    <span class="stat-label">متاح دائماً</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Movies Section -->
    <section class="movies-section">
        <div class="container">
            <h2 class="section-title">
                <i class="fas fa-ghost"></i>
                أفلام الرعب والإثارة
            </h2>

            <div class="movies-grid" id="horror-movies-grid">
                <!-- Horror movies will be loaded here -->
            </div>
        </div>
    </section>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script>
        // Horror Movies Data
        const horrorMovies = [
            {
                id: 1,
                title: 'الراهبة',
                poster_path: 'https://image.tmdb.org/t/p/w500/sFC1ElvoKGdHJIWRpNB3xWJ9lJA.jpg',
                release_date: '2018-09-07',
                vote_average: 5.3,
                genre: 'رعب، غموض'
            },
            {
                id: 2,
                title: 'الهالوين',
                poster_path: 'https://image.tmdb.org/t/p/w500/bXs0zkv2iGVViZEy78teg2ycDBm.jpg',
                release_date: '2018-10-19',
                vote_average: 6.8,
                genre: 'رعب، إثارة'
            },
            {
                id: 3,
                title: 'مكان هادئ',
                poster_path: 'https://image.tmdb.org/t/p/w500/nAU74GmpUk7t5iklEp3bufwDq4n.jpg',
                release_date: '2018-04-06',
                vote_average: 7.5,
                genre: 'رعب، دراما'
            },
            {
                id: 4,
                title: 'الوراثة',
                poster_path: 'https://image.tmdb.org/t/p/w500/yDibcRKVFK2Cfo9LyrKzQNVUgVd.jpg',
                release_date: '2018-06-08',
                vote_average: 7.3,
                genre: 'رعب، غموض'
            },
            {
                id: 5,
                title: 'إت',
                poster_path: 'https://image.tmdb.org/t/p/w500/9E2y5Q7WlCVNEhP5GiVTjhEhx1o.jpg',
                release_date: '2017-09-08',
                vote_average: 7.4,
                genre: 'رعب، إثارة'
            },
            {
                id: 6,
                title: 'الخروج',
                poster_path: 'https://image.tmdb.org/t/p/w500/oBwiJjsxvdSYG8PyV6NwfDpNuPd.jpg',
                release_date: '2017-02-24',
                vote_average: 7.7,
                genre: 'رعب، غموض'
            },
            {
                id: 7,
                title: 'الساحرة',
                poster_path: 'https://image.tmdb.org/t/p/w500/zap5hpFCWSvdWSuPGAQyjUv2wAC.jpg',
                release_date: '2015-02-19',
                vote_average: 6.9,
                genre: 'رعب، غموض'
            },
            {
                id: 8,
                title: 'الضوء',
                poster_path: 'https://image.tmdb.org/t/p/w500/6zbKgwgaaCyyBXE4Sun4oWQfQmi.jpg',
                release_date: '2016-07-22',
                vote_average: 6.3,
                genre: 'رعب، إثارة'
            },
            {
                id: 9,
                title: 'دون تنفس',
                poster_path: 'https://image.tmdb.org/t/p/w500/76daSOTrg4xwNLVoLsYWmpF1gqb.jpg',
                release_date: '2016-08-26',
                vote_average: 7.1,
                genre: 'رعب، إثارة'
            },
            {
                id: 10,
                title: 'الطقوس',
                poster_path: 'https://image.tmdb.org/t/p/w500/zlVHqrlwQAjRVQ4Nt8NaZTzDk3l.jpg',
                release_date: '2017-10-11',
                vote_average: 6.3,
                genre: 'رعب، غموض'
            },
            {
                id: 11,
                title: 'الرجل الخفي',
                poster_path: 'https://image.tmdb.org/t/p/w500/uTALxjQU8e1lhmNjP9nnJ3t2pRU.jpg',
                release_date: '2020-02-28',
                vote_average: 7.1,
                genre: 'رعب، إثارة'
            },
            {
                id: 12,
                title: 'ميدسومار',
                poster_path: 'https://image.tmdb.org/t/p/w500/7LEI8ulZzIcZ6KowdaTJOPNMpJl.jpg',
                release_date: '2019-07-03',
                vote_average: 7.2,
                genre: 'رعب، دراما'
            }
        ];

        // Utility Functions
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} position-fixed`;
            notification.style.cssText = `
                top: 100px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                animation: slideInRight 0.3s ease;
                box-shadow: 0 4px 15px rgba(0,0,0,0.3);
                border-radius: 1rem;
                border: none;
            `;

            const iconMap = {
                'success': 'check-circle',
                'error': 'exclamation-circle',
                'warning': 'exclamation-triangle',
                'info': 'info-circle'
            };

            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${iconMap[type]} me-2"></i>
                    <span>${message}</span>
                    <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOutRight 0.3s ease';
                    setTimeout(() => notification.remove(), 300);
                }
            }, 5000);
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.getFullYear();
        }

        function createHorrorMovieCard(movie) {
            const year = formatDate(movie.release_date);
            const rating = movie.vote_average.toFixed(1);

            return `
                <div class="movie-card" onclick="watchHorrorMovie(${movie.id}, '${movie.title}')">
                    <div class="movie-poster">
                        <img src="${movie.poster_path}" alt="${movie.title}" loading="lazy" onerror="this.src='https://via.placeholder.com/300x450/8b0000/fff?text=Horror+Movie'">
                        <div class="horror-badge">رعب</div>
                        <div class="movie-overlay">
                            <button class="play-btn" onclick="event.stopPropagation(); watchHorrorMovie(${movie.id}, '${movie.title}')">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="movie-info">
                        <h3 class="movie-title">${movie.title}</h3>
                        <div class="movie-meta">
                            <span class="movie-year">${year}</span>
                            <div class="movie-rating">
                                <i class="fas fa-star"></i>
                                <span>${rating}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function loadHorrorMovies() {
            const container = document.getElementById('horror-movies-grid');
            if (!container) return;

            container.innerHTML = horrorMovies.map(movie => createHorrorMovieCard(movie)).join('');
        }

        function watchHorrorMovie(id, title) {
            showNotification(`بدء مشاهدة فيلم الرعب: ${title}`, 'success');
            window.location.href = `watch-pro.html?id=${id}&title=${encodeURIComponent(title)}&type=horror`;
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize AOS
            if (typeof AOS !== 'undefined') {
                AOS.init({
                    duration: 800,
                    easing: 'ease-in-out',
                    once: true,
                    offset: 100
                });
            }

            // Load horror movies
            loadHorrorMovies();

            console.log('Horror movies page loaded successfully!');
        });

        // Add some spooky effects
        document.addEventListener('mousemove', function(e) {
            if (Math.random() < 0.001) { // Very rare random effect
                const ghost = document.createElement('div');
                ghost.innerHTML = '👻';
                ghost.style.cssText = `
                    position: fixed;
                    left: ${e.clientX}px;
                    top: ${e.clientY}px;
                    font-size: 2rem;
                    pointer-events: none;
                    z-index: 9999;
                    animation: ghostFloat 3s ease-out forwards;
                `;

                document.body.appendChild(ghost);

                setTimeout(() => ghost.remove(), 3000);
            }
        });

        // Add ghost animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ghostFloat {
                0% {
                    opacity: 0;
                    transform: translateY(0) scale(0.5);
                }
                50% {
                    opacity: 1;
                    transform: translateY(-50px) scale(1);
                }
                100% {
                    opacity: 0;
                    transform: translateY(-100px) scale(0.5);
                }
            }

            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
