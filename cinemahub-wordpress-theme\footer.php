<!-- Footer -->
<footer class="site-footer">
    <div class="footer-content">
        <p class="footer-text">
            © <?php echo date('Y'); ?> <?php bloginfo('name'); ?> - قالب احترافي لمواقع الأفلام والمسلسلات مع خاصية استعادة المظهر الأصلي
        </p>
        
        <div class="footer-links">
            <?php
            wp_nav_menu(array(
                'theme_location' => 'footer',
                'menu_class' => 'footer-menu',
                'container' => false,
                'fallback_cb' => 'cinemahub_footer_fallback_menu'
            ));
            ?>
        </div>
        
        <div style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid rgba(148, 163, 184, 0.2);">
            <p style="color: #94a3b8; font-size: 0.9rem;">
                <i class="fas fa-code"></i>
                تم التطوير بواسطة فريق CinemaHub Pro
                <span style="margin: 0 1rem;">•</span>
                <i class="fas fa-heart" style="color: #dc2626;"></i>
                صُنع بحب للمحتوى العربي
                <span style="margin: 0 1rem;">•</span>
                <a href="<?php echo get_template_directory_uri(); ?>/restore-original.php" style="color: #dc2626; text-decoration: none;">
                    <i class="fas fa-undo"></i>
                    استعادة المظهر الأصلي
                </a>
            </p>
        </div>
    </div>
</footer>

<?php
// Footer fallback menu
function cinemahub_footer_fallback_menu() {
    echo '<a href="' . home_url() . '">الرئيسية</a>';
    echo '<a href="' . get_post_type_archive_link('movie') . '">الأفلام</a>';
    echo '<a href="' . get_post_type_archive_link('series') . '">المسلسلات</a>';
    echo '<a href="' . admin_url() . '">لوحة التحكم</a>';
    echo '<a href="' . get_template_directory_uri() . '/restore-original.php">استعادة المظهر الأصلي</a>';
}
?>

<!-- Scripts -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects to cards
    const cards = document.querySelectorAll('.feature-card, .movie-card, .series-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.borderColor = '#dc2626';
            this.style.transform = 'translateY(-10px)';
            this.style.boxShadow = '0 20px 40px rgba(0,0,0,0.4)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.borderColor = 'transparent';
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
    
    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Mobile menu toggle
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const mainNav = document.querySelector('.main-nav');
    
    if (mobileMenuBtn && mainNav) {
        mobileMenuBtn.addEventListener('click', function() {
            mainNav.classList.toggle('active');
            this.classList.toggle('active');
        });
    }
    
    // Search functionality
    const searchForm = document.querySelector('.search-form');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            const searchInput = this.querySelector('input[type="search"]');
            if (searchInput && searchInput.value.trim() === '') {
                e.preventDefault();
                searchInput.focus();
            }
        });
    }
    
    // Lazy loading for images
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
    
    // Back to top button
    const backToTopBtn = document.createElement('button');
    backToTopBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
    backToTopBtn.className = 'back-to-top';
    backToTopBtn.style.cssText = `
        position: fixed;
        bottom: 2rem;
        left: 2rem;
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        color: white;
        border: none;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        cursor: pointer;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1000;
        box-shadow: 0 4px 15px rgba(220, 38, 38, 0.4);
    `;
    
    document.body.appendChild(backToTopBtn);
    
    // Show/hide back to top button
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopBtn.style.opacity = '1';
            backToTopBtn.style.visibility = 'visible';
        } else {
            backToTopBtn.style.opacity = '0';
            backToTopBtn.style.visibility = 'hidden';
        }
    });
    
    // Back to top functionality
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    // Add loading animation
    window.addEventListener('load', function() {
        document.body.classList.add('loaded');
    });
});

// AJAX for restore functionality
function restoreOriginalDesign() {
    if (confirm('هل أنت متأكد من استعادة المظهر الأصلي؟ سيتم إنشاء نسخة احتياطية من التخصيصات الحالية.')) {
        const formData = new FormData();
        formData.append('action', 'restore_original_design');
        formData.append('nonce', '<?php echo wp_create_nonce('restore_original_design'); ?>');
        
        fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم استعادة المظهر الأصلي بنجاح!');
                location.reload();
            } else {
                alert('حدث خطأ أثناء استعادة المظهر الأصلي');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء استعادة المظهر الأصلي');
        });
    }
}
</script>

<?php wp_footer(); ?>
</body>
</html>
