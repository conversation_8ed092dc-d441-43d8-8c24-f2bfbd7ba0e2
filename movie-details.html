<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الفيلم - CinemaHub</title>
    
    <!-- External CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
    :root {
        --primary-color: #e50914;
        --secondary-color: #221f1f;
        --dark-color: #141414;
        --light-color: #ffffff;
        --gray-color: #757575;
        --gradient-primary: linear-gradient(135deg, #e50914 0%, #b20710 100%);
        --gradient-dark: linear-gradient(135deg, #141414 0%, #000000 100%);
        --font-family: 'Cairo', sans-serif;
        --transition: all 0.3s ease;
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: var(--font-family);
        background: var(--dark-color);
        color: var(--light-color);
        direction: rtl;
    }

    /* Header */
    .header {
        background: rgba(20, 20, 20, 0.95);
        backdrop-filter: blur(10px);
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 1000;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .navbar-brand {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: var(--light-color) !important;
    }

    .logo-icon {
        width: 40px;
        height: 40px;
        background: var(--gradient-primary);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 10px;
        animation: logoGlow 3s ease-in-out infinite alternate;
    }

    .brand-text {
        font-size: 1.2rem;
        font-weight: 700;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    @keyframes logoGlow {
        0% { box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3); }
        100% { box-shadow: 0 6px 25px rgba(229, 9, 20, 0.6); }
    }

    /* Movie Hero */
    .movie-hero {
        height: 70vh;
        position: relative;
        margin-top: 70px;
        background-size: cover;
        background-position: center;
        display: flex;
        align-items: end;
        background-image: linear-gradient(rgba(0,0,0,0.3), rgba(20,20,20,0.9));
    }

    .movie-hero-content {
        padding: 40px 0;
        width: 100%;
    }

    .movie-poster-large {
        width: 200px;
        height: 300px;
        border-radius: 10px;
        object-fit: cover;
        box-shadow: 0 8px 30px rgba(0,0,0,0.5);
    }

    .movie-info {
        padding-right: 30px;
    }

    .movie-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 15px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    }

    .movie-meta {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 5px;
        background: rgba(255,255,255,0.1);
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.9rem;
    }

    .rating-stars {
        color: #ffd700;
    }

    .movie-description {
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 30px;
        opacity: 0.9;
    }

    .action-buttons {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
    }

    .btn-action {
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: var(--transition);
        border: none;
        cursor: pointer;
    }

    .btn-primary {
        background: var(--primary-color);
        color: var(--light-color);
    }

    .btn-secondary {
        background: rgba(255,255,255,0.2);
        color: var(--light-color);
        backdrop-filter: blur(10px);
    }

    .btn-success {
        background: #28a745;
        color: var(--light-color);
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        color: var(--light-color);
    }

    /* Content Sections */
    .content-section {
        padding: 40px 0;
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 25px;
        color: var(--light-color);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .section-title i {
        color: var(--primary-color);
    }

    /* Servers Section */
    .servers-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .server-card {
        background: var(--secondary-color);
        border-radius: 10px;
        padding: 20px;
        border: 1px solid rgba(255,255,255,0.1);
        transition: var(--transition);
        cursor: pointer;
    }

    .server-card:hover {
        border-color: var(--primary-color);
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(229, 9, 20, 0.2);
    }

    .server-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .server-name {
        font-weight: 600;
        font-size: 1.1rem;
    }

    .server-quality {
        background: var(--primary-color);
        color: var(--light-color);
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .server-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
        font-size: 0.9rem;
        color: var(--gray-color);
    }

    .server-buttons {
        display: flex;
        gap: 10px;
    }

    .btn-server {
        flex: 1;
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: 500;
        text-decoration: none;
        text-align: center;
        transition: var(--transition);
        font-size: 0.9rem;
    }

    .btn-watch {
        background: var(--primary-color);
        color: var(--light-color);
    }

    .btn-download {
        background: #28a745;
        color: var(--light-color);
    }

    .btn-server:hover {
        transform: scale(1.05);
        color: var(--light-color);
    }

    /* Download Links */
    .download-links {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 15px;
    }

    .download-item {
        background: var(--secondary-color);
        border-radius: 8px;
        padding: 15px;
        border: 1px solid rgba(255,255,255,0.1);
        text-align: center;
        transition: var(--transition);
    }

    .download-item:hover {
        border-color: #28a745;
        transform: translateY(-2px);
    }

    .download-quality {
        font-weight: 600;
        margin-bottom: 8px;
        color: var(--light-color);
    }

    .download-size {
        font-size: 0.9rem;
        color: var(--gray-color);
        margin-bottom: 10px;
    }

    .btn-download-link {
        background: #28a745;
        color: var(--light-color);
        padding: 8px 15px;
        border-radius: 15px;
        text-decoration: none;
        font-size: 0.9rem;
        transition: var(--transition);
        display: inline-block;
    }

    .btn-download-link:hover {
        background: #218838;
        color: var(--light-color);
    }

    /* Movie Details */
    .movie-details {
        background: var(--secondary-color);
        border-radius: 10px;
        padding: 25px;
        border: 1px solid rgba(255,255,255,0.1);
    }

    .details-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .detail-item {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .detail-label {
        font-weight: 600;
        color: var(--primary-color);
        font-size: 0.9rem;
    }

    .detail-value {
        color: var(--light-color);
    }

    /* Responsive */
    @media (max-width: 768px) {
        .movie-hero {
            height: auto;
            padding: 20px 0;
        }
        
        .movie-hero-content .row {
            flex-direction: column;
            text-align: center;
        }
        
        .movie-poster-large {
            width: 150px;
            height: 225px;
            margin: 0 auto 20px;
        }
        
        .movie-info {
            padding-right: 0;
        }
        
        .movie-title {
            font-size: 1.8rem;
        }
        
        .servers-grid {
            grid-template-columns: 1fr;
        }
        
        .download-links {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 576px) {
        .download-links {
            grid-template-columns: 1fr;
        }
        
        .action-buttons {
            flex-direction: column;
        }
        
        .btn-action {
            text-align: center;
            justify-content: center;
        }
    }
    </style>
</head>

<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <a class="navbar-brand" href="index-fixed.html">
                    <div class="logo-icon">
                        <i class="fas fa-film" style="color: white; font-size: 18px;"></i>
                    </div>
                    <div class="brand-text">CinemaHub</div>
                </a>
                
                <a href="index-fixed.html" class="btn btn-outline-light btn-sm">
                    <i class="fas fa-arrow-right"></i>
                    العودة للرئيسية
                </a>
            </div>
        </nav>
    </header>

    <!-- Movie Hero Section -->
    <section class="movie-hero" id="movieHero">
        <div class="container">
            <div class="movie-hero-content">
                <div class="row align-items-end">
                    <div class="col-md-3">
                        <img id="moviePoster" class="movie-poster-large" src="" alt="Movie Poster">
                    </div>
                    <div class="col-md-9">
                        <div class="movie-info">
                            <h1 class="movie-title" id="movieTitle">عنوان الفيلم</h1>
                            <div class="movie-meta" id="movieMeta">
                                <!-- Meta info will be populated by JavaScript -->
                            </div>
                            <p class="movie-description" id="movieDescription">
                                وصف الفيلم سيتم تحميله هنا...
                            </p>
                            <div class="action-buttons">
                                <button class="btn-action btn-primary" onclick="watchMovie()">
                                    <i class="fas fa-play"></i>
                                    مشاهدة الآن
                                </button>
                                <button class="btn-action btn-success" onclick="scrollToDownload()">
                                    <i class="fas fa-download"></i>
                                    تحميل
                                </button>
                                <button class="btn-action btn-secondary" onclick="addToFavorites()">
                                    <i class="fas fa-heart"></i>
                                    إضافة للمفضلة
                                </button>
                                <button class="btn-action btn-secondary" onclick="shareMovie()">
                                    <i class="fas fa-share"></i>
                                    مشاركة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="container">
        <!-- Watch Servers Section -->
        <section class="content-section">
            <h2 class="section-title">
                <i class="fas fa-server"></i>
                سيرفرات المشاهدة
            </h2>
            <div class="servers-grid">
                <div class="server-card" onclick="openServer('server1')">
                    <div class="server-header">
                        <div class="server-name">سيرفر 1 - سريع</div>
                        <div class="server-quality">HD</div>
                    </div>
                    <div class="server-info">
                        <span><i class="fas fa-signal"></i> سرعة عالية</span>
                        <span><i class="fas fa-globe"></i> عالمي</span>
                    </div>
                    <div class="server-buttons">
                        <a href="#" class="btn-server btn-watch" onclick="watchOnServer('server1')">
                            <i class="fas fa-play"></i> مشاهدة
                        </a>
                    </div>
                </div>

                <div class="server-card" onclick="openServer('server2')">
                    <div class="server-header">
                        <div class="server-name">سيرفر 2 - مستقر</div>
                        <div class="server-quality">Full HD</div>
                    </div>
                    <div class="server-info">
                        <span><i class="fas fa-shield-alt"></i> آمن</span>
                        <span><i class="fas fa-clock"></i> 24/7</span>
                    </div>
                    <div class="server-buttons">
                        <a href="#" class="btn-server btn-watch" onclick="watchOnServer('server2')">
                            <i class="fas fa-play"></i> مشاهدة
                        </a>
                    </div>
                </div>

                <div class="server-card" onclick="openServer('server3')">
                    <div class="server-header">
                        <div class="server-name">سيرفر 3 - VIP</div>
                        <div class="server-quality">4K</div>
                    </div>
                    <div class="server-info">
                        <span><i class="fas fa-crown"></i> مميز</span>
                        <span><i class="fas fa-rocket"></i> فائق السرعة</span>
                    </div>
                    <div class="server-buttons">
                        <a href="#" class="btn-server btn-watch" onclick="watchOnServer('server3')">
                            <i class="fas fa-play"></i> مشاهدة
                        </a>
                    </div>
                </div>

                <div class="server-card" onclick="openServer('server4')">
                    <div class="server-header">
                        <div class="server-name">سيرفر 4 - محلي</div>
                        <div class="server-quality">HD</div>
                    </div>
                    <div class="server-info">
                        <span><i class="fas fa-map-marker-alt"></i> محلي</span>
                        <span><i class="fas fa-language"></i> مدبلج</span>
                    </div>
                    <div class="server-buttons">
                        <a href="#" class="btn-server btn-watch" onclick="watchOnServer('server4')">
                            <i class="fas fa-play"></i> مشاهدة
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Download Section -->
        <section class="content-section" id="downloadSection">
            <h2 class="section-title">
                <i class="fas fa-download"></i>
                روابط التحميل
            </h2>
            <div class="download-links">
                <div class="download-item">
                    <div class="download-quality">720p HD</div>
                    <div class="download-size">1.2 GB</div>
                    <a href="#" class="btn-download-link" onclick="downloadMovie('720p')">
                        <i class="fas fa-download"></i> تحميل
                    </a>
                </div>

                <div class="download-item">
                    <div class="download-quality">1080p Full HD</div>
                    <div class="download-size">2.5 GB</div>
                    <a href="#" class="btn-download-link" onclick="downloadMovie('1080p')">
                        <i class="fas fa-download"></i> تحميل
                    </a>
                </div>

                <div class="download-item">
                    <div class="download-quality">4K Ultra HD</div>
                    <div class="download-size">8.2 GB</div>
                    <a href="#" class="btn-download-link" onclick="downloadMovie('4k')">
                        <i class="fas fa-download"></i> تحميل
                    </a>
                </div>

                <div class="download-item">
                    <div class="download-quality">480p موبايل</div>
                    <div class="download-size">650 MB</div>
                    <a href="#" class="btn-download-link" onclick="downloadMovie('480p')">
                        <i class="fas fa-download"></i> تحميل
                    </a>
                </div>

                <div class="download-item">
                    <div class="download-quality">مدبلج عربي</div>
                    <div class="download-size">1.8 GB</div>
                    <a href="#" class="btn-download-link" onclick="downloadMovie('dubbed')">
                        <i class="fas fa-download"></i> تحميل
                    </a>
                </div>

                <div class="download-item">
                    <div class="download-quality">مترجم عربي</div>
                    <div class="download-size">2.1 GB</div>
                    <a href="#" class="btn-download-link" onclick="downloadMovie('subtitled')">
                        <i class="fas fa-download"></i> تحميل
                    </a>
                </div>
            </div>
        </section>

        <!-- Movie Details Section -->
        <section class="content-section">
            <h2 class="section-title">
                <i class="fas fa-info-circle"></i>
                تفاصيل الفيلم
            </h2>
            <div class="movie-details">
                <div class="details-grid" id="movieDetails">
                    <!-- Details will be populated by JavaScript -->
                </div>
            </div>
        </section>
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Enhanced movie data with multiple movies
        const moviesDatabase = {
            1: {
                title: 'أفاتار: طريق الماء',
                originalTitle: 'Avatar: The Way of Water',
                poster: 'https://image.tmdb.org/t/p/w500/s16H6tpK2utvwDtzZ8Qy4qm5Emw.jpg',
                backdrop: 'https://image.tmdb.org/t/p/w1280/s16H6tpK2utvwDtzZ8Qy4qm5Emw.jpg',
                year: '2022',
                duration: '192 دقيقة',
                rating: '7.7',
                genre: 'خيال علمي، مغامرة، دراما',
                director: 'جيمس كاميرون',
                cast: 'سام ورثينغتون، زوي سالدانا، سيغورني ويفر',
                country: 'الولايات المتحدة',
                language: 'الإنجليزية',
                description: 'بعد أكثر من عقد من الأحداث الأولى، تعلم عائلة سولي (جيك ونيتيري وأطفالهم) المشاكل التي تتبعهم والأطوال التي سيذهبون إليها للحفاظ على سلامة بعضهم البعض، والمعارك التي يخوضونها للبقاء على قيد الحياة، والمآسي التي يتحملونها.',
                type: 'movie'
            },
            2: {
                title: 'الرجل العنكبوت: لا طريق للعودة',
                originalTitle: 'Spider-Man: No Way Home',
                poster: 'https://image.tmdb.org/t/p/w500/1g0dhYtq4irTY1GPXvft6k4YLjm.jpg',
                backdrop: 'https://image.tmdb.org/t/p/w1280/1g0dhYtq4irTY1GPXvft6k4YLjm.jpg',
                year: '2021',
                duration: '148 دقيقة',
                rating: '8.4',
                genre: 'أكشن، مغامرة، خيال علمي',
                director: 'جون واتس',
                cast: 'توم هولاند، زيندايا، بنديكت كامبرباتش',
                country: 'الولايات المتحدة',
                language: 'الإنجليزية',
                description: 'بيتر باركر يكشف هويته كرجل عنكبوت ولا يعود قادراً على فصل حياته الطبيعية عن المخاطر العالية لكونه بطلاً خارقاً. عندما يطلب المساعدة من دكتور سترينج، تصبح المخاطر أكثر خطورة.',
                type: 'movie'
            },
            3: {
                title: 'توب غان: مافريك',
                originalTitle: 'Top Gun: Maverick',
                poster: 'https://image.tmdb.org/t/p/w500/62HCnUTziyWcpDaBO2i1DX17ljH.jpg',
                backdrop: 'https://image.tmdb.org/t/p/w1280/62HCnUTziyWcpDaBO2i1DX17ljH.jpg',
                year: '2022',
                duration: '130 دقيقة',
                rating: '8.3',
                genre: 'أكشن، دراما',
                director: 'جوزيف كوسينسكي',
                cast: 'توم كروز، مايلز تيلر، جينيفر كونيلي',
                country: 'الولايات المتحدة',
                language: 'الإنجليزية',
                description: 'بعد أكثر من ثلاثين عاماً من الخدمة كواحد من أفضل الطيارين البحريين، يعود بيت "مافريك" ميتشل إلى المكان الذي ينتمي إليه، ويدفع حدود ما يمكن للطيار المقاتل أن يكونه.',
                type: 'movie'
            }
        };

        const seriesDatabase = {
            1: {
                title: 'بيت التنين',
                originalTitle: 'House of the Dragon',
                poster: 'https://image.tmdb.org/t/p/w500/z2yahl2uefxDCl0nogcRBstwruJ.jpg',
                backdrop: 'https://image.tmdb.org/t/p/w1280/z2yahl2uefxDCl0nogcRBstwruJ.jpg',
                year: '2022',
                duration: '10 حلقات',
                rating: '8.5',
                genre: 'دراما، فانتازيا، أكشن',
                director: 'ميغيل سابوتشنيك',
                cast: 'بادي كونسيدين، مات سميث، إيما دارسي',
                country: 'الولايات المتحدة',
                language: 'الإنجليزية',
                description: 'تدور أحداث المسلسل قبل 200 عام من أحداث صراع العروش، ويحكي قصة آل تارغاريان والحرب الأهلية التي مزقت العائلة.',
                type: 'tv'
            },
            2: {
                title: 'أشياء غريبة',
                originalTitle: 'Stranger Things',
                poster: 'https://image.tmdb.org/t/p/w500/49WJfeN0moxb9IPfGn8AIqMGskD.jpg',
                backdrop: 'https://image.tmdb.org/t/p/w1280/49WJfeN0moxb9IPfGn8AIqMGskD.jpg',
                year: '2016',
                duration: '4 مواسم',
                rating: '8.7',
                genre: 'دراما، خيال علمي، رعب',
                director: 'الأخوان دافر',
                cast: 'مايلي بوبي براون، فين وولفهارد، ديفيد هاربور',
                country: 'الولايات المتحدة',
                language: 'الإنجليزية',
                description: 'عندما يختفي صبي صغير، تكشف مدينته الصغيرة عن مؤامرة تشمل تجارب سرية وقوى خارقة للطبيعة مرعبة وفتاة صغيرة غريبة واحدة.',
                type: 'tv'
            }
        };

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadMovieData();
            initializeFavorites();

            // Add CSS animations
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }

                @keyframes slideOutRight {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }

                .server-card:hover {
                    transform: translateY(-5px) scale(1.02);
                    box-shadow: 0 10px 30px rgba(229, 9, 20, 0.3);
                }

                .download-item:hover {
                    transform: translateY(-3px);
                    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
                }

                .btn-action:hover {
                    transform: translateY(-2px) scale(1.05);
                }

                .movie-poster-large {
                    transition: transform 0.3s ease;
                }

                .movie-poster-large:hover {
                    transform: scale(1.05);
                }
            `;
            document.head.appendChild(style);

            console.log('Movie details page initialized successfully!');
        });

        function loadMovieData() {
            // Get parameters from URL
            const urlParams = new URLSearchParams(window.location.search);
            const movieId = urlParams.get('id') || '1';
            const movieType = urlParams.get('type') || 'movie';
            const movieTitle = urlParams.get('title');

            // Get movie data from appropriate database
            let movieData;
            if (movieType === 'movie') {
                movieData = moviesDatabase[movieId] || moviesDatabase[1];
            } else {
                movieData = seriesDatabase[movieId] || seriesDatabase[1];
            }

            // Set movie data
            document.getElementById('movieTitle').textContent = movieData.title;
            document.getElementById('moviePoster').src = movieData.poster;
            document.getElementById('movieDescription').textContent = movieData.description;

            // Set background
            document.getElementById('movieHero').style.backgroundImage =
                `linear-gradient(rgba(0,0,0,0.3), rgba(20,20,20,0.9)), url('${movieData.backdrop}')`;

            // Set meta info
            const metaHTML = `
                <div class="meta-item">
                    <i class="fas fa-calendar"></i>
                    <span>${movieData.year}</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-clock"></i>
                    <span>${movieData.duration}</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-star rating-stars"></i>
                    <span>${movieData.rating}/10</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-tags"></i>
                    <span>${movieData.genre}</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-${movieType === 'movie' ? 'film' : 'tv'}"></i>
                    <span>${movieType === 'movie' ? 'فيلم' : 'مسلسل'}</span>
                </div>
            `;
            document.getElementById('movieMeta').innerHTML = metaHTML;

            // Set details
            const detailsHTML = `
                <div class="detail-item">
                    <div class="detail-label">العنوان الأصلي</div>
                    <div class="detail-value">${movieData.originalTitle}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">${movieType === 'movie' ? 'المخرج' : 'المنتج'}</div>
                    <div class="detail-value">${movieData.director}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">الممثلون</div>
                    <div class="detail-value">${movieData.cast}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">البلد</div>
                    <div class="detail-value">${movieData.country}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">اللغة</div>
                    <div class="detail-value">${movieData.language}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">النوع</div>
                    <div class="detail-value">${movieData.genre}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">النوع</div>
                    <div class="detail-value">${movieType === 'movie' ? 'فيلم سينمائي' : 'مسلسل تلفزيوني'}</div>
                </div>
            `;
            document.getElementById('movieDetails').innerHTML = detailsHTML;

            // Update page title
            document.title = `${movieData.title} - CinemaHub`;

            // Store current movie data globally
            window.currentMovieData = movieData;
            window.currentMovieType = movieType;
        }

        // Enhanced interaction functions
        function watchMovie() {
            const urlParams = new URLSearchParams(window.location.search);
            const movieId = urlParams.get('id') || '1';
            const movieType = urlParams.get('type') || 'movie';
            const movieTitle = window.currentMovieData ? window.currentMovieData.title : 'فيلم';

            showNotification(`بدء مشاهدة: ${movieTitle}`, 'success');

            // Redirect to watch page
            window.location.href = `watch.html?id=${movieId}&title=${encodeURIComponent(movieTitle)}&type=${movieType}`;
        }

        function watchOnServer(serverId) {
            const urlParams = new URLSearchParams(window.location.search);
            const movieId = urlParams.get('id') || '1';
            const movieType = urlParams.get('type') || 'movie';
            const movieTitle = window.currentMovieData ? window.currentMovieData.title : 'فيلم';

            showNotification(`مشاهدة على ${getServerName(serverId)}`, 'info');

            // Redirect to watch page with server parameter
            window.location.href = `watch.html?id=${movieId}&title=${encodeURIComponent(movieTitle)}&type=${movieType}&server=${serverId}`;
        }

        function getServerName(serverId) {
            const serverNames = {
                'server1': 'السيرفر السريع',
                'server2': 'السيرفر المستقر',
                'server3': 'السيرفر المميز',
                'server4': 'السيرفر المحلي'
            };
            return serverNames[serverId] || serverId;
        }

        function downloadMovie(quality) {
            const movieTitle = window.currentMovieData ? window.currentMovieData.title : 'الفيلم';
            showNotification(`بدء تحميل ${movieTitle} بجودة ${quality}`, 'success');

            // Simulate download process
            setTimeout(() => {
                showNotification(`تم بدء التحميل بنجاح!`, 'success');
            }, 1000);
        }

        function scrollToDownload() {
            const downloadSection = document.getElementById('downloadSection');
            if (downloadSection) {
                downloadSection.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        }

        function addToFavorites() {
            const movieTitle = window.currentMovieData ? window.currentMovieData.title : 'الفيلم';

            // Check if already in favorites
            let favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
            const movieId = new URLSearchParams(window.location.search).get('id') || '1';

            if (favorites.includes(movieId)) {
                // Remove from favorites
                favorites = favorites.filter(id => id !== movieId);
                localStorage.setItem('favorites', JSON.stringify(favorites));
                showNotification(`تم إزالة ${movieTitle} من المفضلة`, 'info');
                updateFavoriteButton(false);
            } else {
                // Add to favorites
                favorites.push(movieId);
                localStorage.setItem('favorites', JSON.stringify(favorites));
                showNotification(`تم إضافة ${movieTitle} للمفضلة!`, 'success');
                updateFavoriteButton(true);
            }
        }

        function updateFavoriteButton(isFavorite) {
            const favoriteBtn = document.querySelector('[onclick="addToFavorites()"]');
            if (favoriteBtn) {
                const icon = favoriteBtn.querySelector('i');
                if (isFavorite) {
                    icon.className = 'fas fa-heart';
                    favoriteBtn.style.background = '#dc3545';
                } else {
                    icon.className = 'far fa-heart';
                    favoriteBtn.style.background = 'rgba(255, 255, 255, 0.2)';
                }
            }
        }

        function shareMovie() {
            const movieTitle = window.currentMovieData ? window.currentMovieData.title : 'فيلم رائع';
            const movieDescription = window.currentMovieData ? window.currentMovieData.description : 'شاهد هذا الفيلم الرائع';

            if (navigator.share) {
                navigator.share({
                    title: `${movieTitle} - CinemaHub`,
                    text: movieDescription,
                    url: window.location.href
                }).then(() => {
                    showNotification('تم مشاركة الفيلم بنجاح!', 'success');
                }).catch(() => {
                    fallbackShare();
                });
            } else {
                fallbackShare();
            }
        }

        function fallbackShare() {
            const url = window.location.href;
            if (navigator.clipboard) {
                navigator.clipboard.writeText(url).then(() => {
                    showNotification('تم نسخ رابط الفيلم!', 'success');
                }).catch(() => {
                    showShareModal(url);
                });
            } else {
                showShareModal(url);
            }
        }

        function showShareModal(url) {
            const modal = document.createElement('div');
            modal.className = 'modal fade show';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content" style="background: var(--secondary-color); color: var(--light-color);">
                        <div class="modal-header">
                            <h5 class="modal-title">مشاركة الفيلم</h5>
                            <button type="button" class="btn-close btn-close-white" onclick="this.closest('.modal').remove()"></button>
                        </div>
                        <div class="modal-body">
                            <p>انسخ الرابط أدناه لمشاركة الفيلم:</p>
                            <div class="input-group">
                                <input type="text" class="form-control" value="${url}" readonly>
                                <button class="btn btn-primary" onclick="navigator.clipboard.writeText('${url}'); showNotification('تم النسخ!', 'success'); this.closest('.modal').remove();">نسخ</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        function openServer(serverId) {
            showNotification(`فتح ${getServerName(serverId)}`, 'info');
            // Here you could open the server in a new tab or modal
        }

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} position-fixed`;
            notification.style.cssText = `
                top: 100px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                animation: slideInRight 0.3s ease;
                box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            `;

            const iconMap = {
                'success': 'check-circle',
                'error': 'exclamation-circle',
                'warning': 'exclamation-triangle',
                'info': 'info-circle'
            };

            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${iconMap[type]} me-2"></i>
                    <span>${message}</span>
                    <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOutRight 0.3s ease';
                    setTimeout(() => notification.remove(), 300);
                }
            }, 5000);
        }

        // Initialize favorites button state
        function initializeFavorites() {
            const favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
            const movieId = new URLSearchParams(window.location.search).get('id') || '1';
            updateFavoriteButton(favorites.includes(movieId));
        }

        function openServer(serverId) {
            console.log(`Opening server: ${serverId}`);
        }
    </script>
</body>
</html>
