# 🚀 كيفية إنشاء ملف التثبيت - دليل سريع

## 📋 **المتطلبات الأساسية**

### **1. تثبيت Node.js**
- **Windows**: حمل من https://nodejs.org/ وثبته
- **macOS**: `brew install node` أو حمل من الموقع
- **Ubuntu/Debian**: `sudo apt install nodejs npm`
- **CentOS/RHEL**: `sudo yum install nodejs npm`

### **2. التحقق من التثبيت**
```bash
node --version  # يجب أن يظهر رقم الإصدار
npm --version   # يجب أن يظهر رقم الإصدار
```

## 🛠️ **إنشاء ملف التثبيت**

### **الطريقة الأولى: استخدام السكريبت التلقائي**

#### **على Windows:**
1. **انقر نقراً مزدوجاً** على ملف `create-installer.bat`
2. **انتظر** حتى انتهاء العملية (5-10 دقائق)
3. **ستجد الملفات** في مجلد `dist/`

#### **على Linux/macOS:**
```bash
# اجعل الملف قابل للتنفيذ
chmod +x create-installer.sh

# شغل السكريبت
./create-installer.sh
```

### **الطريقة الثانية: الأوامر اليدوية**

#### **1. تثبيت التبعيات**
```bash
cd cinemahub-desktop-app
npm install
```

#### **2. بناء ملف التثبيت**
```bash
# لجميع المنصات (يستغرق وقت أطول)
npm run build

# أو لمنصة محددة:
npm run build-win    # Windows
npm run build-mac    # macOS
npm run build-linux  # Linux
```

## 📁 **الملفات المُنتجة**

بعد انتهاء البناء، ستجد في مجلد `dist/`:

### **Windows:**
- `CinemaHub-Pro-Desktop-Setup-1.0.0.exe` - ملف التثبيت
- `CinemaHub-Pro-Desktop-1.0.0-Portable.exe` - النسخة المحمولة

### **macOS:**
- `CinemaHub-Pro-Desktop-1.0.0.dmg` - ملف التثبيت
- `CinemaHub-Pro-Desktop-1.0.0-mac.zip` - أرشيف مضغوط

### **Linux:**
- `CinemaHub-Pro-Desktop-1.0.0-x86_64.AppImage` - نسخة محمولة
- `CinemaHub-Pro-Desktop-1.0.0-amd64.deb` - حزمة Ubuntu/Debian
- `CinemaHub-Pro-Desktop-1.0.0-x86_64.rpm` - حزمة CentOS/RHEL

## 🎯 **للمستخدمين النهائيين**

### **Windows:**
1. حمل ملف `.exe`
2. انقر نقراً مزدوجاً
3. اتبع تعليمات التثبيت

### **macOS:**
1. حمل ملف `.dmg`
2. انقر نقراً مزدوجاً
3. اسحب التطبيق لمجلد Applications

### **Linux:**
1. حمل ملف `.AppImage`
2. اجعله قابل للتنفيذ: `chmod +x filename.AppImage`
3. شغله: `./filename.AppImage`

## 🆘 **حل المشاكل**

### **مشكلة: "node is not recognized"**
- **الحل**: تأكد من تثبيت Node.js وإعادة تشغيل Terminal/CMD

### **مشكلة: "npm install failed"**
- **الحل**: 
  ```bash
  rm -rf node_modules
  rm package-lock.json
  npm install
  ```

### **مشكلة: "Build failed"**
- **الحل**: تأكد من:
  - وجود اتصال إنترنت
  - وجود مساحة كافية على القرص (5GB على الأقل)
  - عدم تشغيل برامج مكافحة الفيروسات أثناء البناء

### **مشكلة: "Icon not found"**
- **الحل**: تأكد من وجود ملفات الأيقونات في مجلد `assets/`

## 📊 **معلومات إضافية**

### **أحجام الملفات المتوقعة:**
- Windows Installer: ~180 MB
- Windows Portable: ~175 MB
- macOS DMG: ~185 MB
- Linux AppImage: ~190 MB

### **وقت البناء المتوقع:**
- منصة واحدة: 3-5 دقائق
- جميع المنصات: 10-15 دقيقة

### **متطلبات النظام للبناء:**
- RAM: 4GB على الأقل
- مساحة القرص: 5GB فارغة
- اتصال إنترنت مستقر

## 🎉 **تهانينا!**

الآن لديك ملفات تثبيت جاهزة يمكن توزيعها للمستخدمين!

**الخطوات التالية:**
1. اختبر الملفات على أجهزة مختلفة
2. ارفعها على خدمة تخزين سحابي
3. شارك روابط التحميل مع المستخدمين

**روابط مفيدة:**
- [دليل التوزيع الكامل](DISTRIBUTION.md)
- [دليل البدء السريع](QUICK-START.md)
- [دليل المستخدم](README.md)
