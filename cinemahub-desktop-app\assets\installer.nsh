; CinemaHub Pro Desktop - NSIS Installer Script
; هذا الملف يحتوي على إعدادات إضافية لـ installer على Windows

; إضافة معلومات إضافية للبرنامج
!define PRODUCT_NAME "CinemaHub Pro Desktop"
!define PRODUCT_VERSION "1.0.0"
!define PRODUCT_PUBLISHER "CinemaHub Team"
!define PRODUCT_WEB_SITE "https://cinemahub-pro.com"
!define PRODUCT_DIR_REGKEY "Software\Microsoft\Windows\CurrentVersion\App Paths\${PRODUCT_NAME}.exe"
!define PRODUCT_UNINST_KEY "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"

; إعدادات إضافية
!define MUI_ABORTWARNING
!define MUI_ICON "icon.ico"
!define MUI_UNICON "icon.ico"
!define MUI_WELCOMEFINISHPAGE_BITMAP "installer-welcome.bmp"
!define MUI_UNWELCOMEFINISHPAGE_BITMAP "installer-welcome.bmp"

; صفحات الـ installer
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "license.txt"
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; صفحات الـ uninstaller
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; اللغات المدعومة
!insertmacro MUI_LANGUAGE "English"
!insertmacro MUI_LANGUAGE "Arabic"

; معلومات إضافية للملف
VIProductVersion "*******"
VIAddVersionKey /LANG=${LANG_ENGLISH} "ProductName" "${PRODUCT_NAME}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "Comments" "Professional desktop application for managing movie and TV series websites"
VIAddVersionKey /LANG=${LANG_ENGLISH} "CompanyName" "${PRODUCT_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "LegalTrademarks" "CinemaHub Pro is a trademark of ${PRODUCT_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "LegalCopyright" "© ${PRODUCT_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "FileDescription" "${PRODUCT_NAME}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "FileVersion" "${PRODUCT_VERSION}"

; إعدادات التثبيت
Section "MainSection" SEC01
  ; إنشاء مجلد البرنامج
  SetOutPath "$INSTDIR"
  SetOverwrite ifnewer
  
  ; نسخ الملفات الأساسية
  File /r "*.*"
  
  ; إنشاء اختصارات
  CreateDirectory "$SMPROGRAMS\${PRODUCT_NAME}"
  CreateShortCut "$SMPROGRAMS\${PRODUCT_NAME}\${PRODUCT_NAME}.lnk" "$INSTDIR\${PRODUCT_NAME}.exe"
  CreateShortCut "$SMPROGRAMS\${PRODUCT_NAME}\Uninstall.lnk" "$INSTDIR\uninst.exe"
  CreateShortCut "$DESKTOP\${PRODUCT_NAME}.lnk" "$INSTDIR\${PRODUCT_NAME}.exe"
  
  ; تسجيل البرنامج في Windows
  WriteRegStr HKLM "${PRODUCT_DIR_REGKEY}" "" "$INSTDIR\${PRODUCT_NAME}.exe"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "DisplayName" "$(^Name)"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "UninstallString" "$INSTDIR\uninst.exe"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "DisplayIcon" "$INSTDIR\${PRODUCT_NAME}.exe"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "DisplayVersion" "${PRODUCT_VERSION}"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "URLInfoAbout" "${PRODUCT_WEB_SITE}"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "Publisher" "${PRODUCT_PUBLISHER}"
SectionEnd

; وصف الأقسام
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC01} "البرنامج الأساسي وجميع الملفات المطلوبة"
!insertmacro MUI_FUNCTION_DESCRIPTION_END

; دالة إلغاء التثبيت
Section Uninstall
  ; حذف الملفات
  Delete "$INSTDIR\${PRODUCT_NAME}.exe"
  Delete "$INSTDIR\uninst.exe"
  
  ; حذف الاختصارات
  Delete "$SMPROGRAMS\${PRODUCT_NAME}\*.*"
  Delete "$DESKTOP\${PRODUCT_NAME}.lnk"
  
  ; حذف المجلدات
  RMDir "$SMPROGRAMS\${PRODUCT_NAME}"
  RMDir /r "$INSTDIR"
  
  ; حذف مفاتيح التسجيل
  DeleteRegKey HKLM "${PRODUCT_UNINST_KEY}"
  DeleteRegKey HKLM "${PRODUCT_DIR_REGKEY}"
  
  SetAutoClose true
SectionEnd

; دالة التحقق من إصدار Windows
Function .onInit
  ; التحقق من إصدار Windows
  ${If} ${AtLeastWin7}
    ; Windows 7 أو أحدث - متوافق
  ${Else}
    MessageBox MB_OK|MB_ICONSTOP "هذا البرنامج يتطلب Windows 7 أو إصدار أحدث"
    Abort
  ${EndIf}
  
  ; التحقق من وجود .NET Framework (إذا كان مطلوب)
  ; يمكن إضافة فحوصات إضافية هنا
FunctionEnd

; دالة ما بعد التثبيت
Function .onInstSuccess
  ; رسالة نجاح التثبيت
  MessageBox MB_YESNO "تم تثبيت ${PRODUCT_NAME} بنجاح!$\n$\nهل تريد تشغيل البرنامج الآن؟" IDNO NoLaunch
    Exec "$INSTDIR\${PRODUCT_NAME}.exe"
  NoLaunch:
FunctionEnd

; دالة ما قبل إلغاء التثبيت
Function un.onInit
  MessageBox MB_ICONQUESTION|MB_YESNO|MB_DEFBUTTON2 "هل أنت متأكد من إلغاء تثبيت ${PRODUCT_NAME} وجميع مكوناته؟" IDYES +2
  Abort
FunctionEnd

; دالة ما بعد إلغاء التثبيت
Function un.onUninstSuccess
  HideWindow
  MessageBox MB_ICONINFORMATION|MB_OK "تم إلغاء تثبيت ${PRODUCT_NAME} بنجاح من جهاز الكمبيوتر."
FunctionEnd
