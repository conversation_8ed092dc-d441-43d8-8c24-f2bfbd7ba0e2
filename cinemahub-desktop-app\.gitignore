# CinemaHub Pro Desktop - Git Ignore File

# ===== Node.js =====
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.production
.env.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# ===== Electron =====
# Electron build output
dist/
build/
out/

# Electron packager output
*-darwin-x64/
*-linux-ia32/
*-linux-x64/
*-win32-ia32/
*-win32-x64/

# Electron builder output
dist_electron/

# ===== Database =====
# SQLite databases
*.db
*.sqlite
*.sqlite3
*.db-journal

# Database backups
*.backup
backups/

# ===== Logs =====
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# ===== OS Generated Files =====
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===== IDEs and Editors =====
# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr

# WebStorm
.idea/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===== Build Tools =====
# Webpack
.webpack/

# Rollup
.rollup.cache/

# Parcel
.parcel-cache/

# ===== Testing =====
# Jest
coverage/
.nyc_output/

# Cypress
cypress/videos/
cypress/screenshots/

# ===== Certificates and Keys =====
# SSL certificates
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx

# SSH keys
id_rsa
id_rsa.pub
id_ed25519
id_ed25519.pub

# ===== Configuration Files =====
# Local configuration
config/local.json
config/development.json
config/production.json

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# ===== User Data =====
# User preferences
user-preferences.json
app-settings.json

# User data directory
userData/

# ===== Temporary Files =====
# Temporary directories
tmp/
temp/
.tmp/

# Cache directories
.cache/
cache/

# ===== Archives =====
*.zip
*.tar.gz
*.rar
*.7z

# ===== Media Files (if not needed in repo) =====
# Uncomment if you don't want to track media files
# *.jpg
# *.jpeg
# *.png
# *.gif
# *.bmp
# *.tiff
# *.ico
# *.mp4
# *.avi
# *.mov
# *.wmv
# *.flv
# *.mp3
# *.wav
# *.flac

# ===== Documentation Build =====
docs/_build/
site/

# ===== Package Managers =====
# Yarn
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# pnpm
.pnpm-debug.log*

# ===== Electron Specific =====
# Auto-updater
*.blockmap
*.yml

# Code signing
*.p12
*.mobileprovision

# ===== Custom Project Files =====
# Project specific ignores
TODO.md
NOTES.md
.scratch/

# Backup files
*.bak
*.backup
*.old

# ===== Security =====
# Don't commit sensitive files
secrets.json
private-keys/
certificates/

# ===== Performance =====
# Profiling files
*.prof
*.heapsnapshot

# ===== Miscellaneous =====
# Lock files (uncomment if you want to ignore them)
# package-lock.json
# yarn.lock

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
