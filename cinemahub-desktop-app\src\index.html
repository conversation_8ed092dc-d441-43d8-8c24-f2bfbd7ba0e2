<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CinemaHub Pro Desktop - لوحة الإدارة</title>
    
    <!-- External CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-content">
            <div class="loading-logo">
                <i class="fas fa-film"></i>
                <h2>CinemaHub Pro Desktop</h2>
            </div>
            <div class="spinner"></div>
            <p>جاري تحميل لوحة الإدارة...</p>
        </div>
    </div>

    <!-- Main Application -->
    <div class="app-container" id="appContainer">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-film"></i>
                    <span>CinemaHub Pro</span>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item active" data-page="dashboard">
                        <a href="#" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li class="nav-item" data-page="movies">
                        <a href="#" class="nav-link">
                            <i class="fas fa-film"></i>
                            <span>إدارة الأفلام</span>
                        </a>
                    </li>
                    <li class="nav-item" data-page="series">
                        <a href="#" class="nav-link">
                            <i class="fas fa-tv"></i>
                            <span>إدارة المسلسلات</span>
                        </a>
                    </li>
                    <li class="nav-item" data-page="categories">
                        <a href="#" class="nav-link">
                            <i class="fas fa-th-large"></i>
                            <span>التصنيفات</span>
                        </a>
                    </li>
                    <li class="nav-item" data-page="website">
                        <a href="#" class="nav-link">
                            <i class="fas fa-globe"></i>
                            <span>إدارة الموقع</span>
                        </a>
                    </li>
                    <li class="nav-item" data-page="sync">
                        <a href="#" class="nav-link">
                            <i class="fas fa-sync-alt"></i>
                            <span>المزامنة</span>
                        </a>
                    </li>
                    <li class="nav-item" data-page="settings">
                        <a href="#" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <span class="user-name">المدير</span>
                        <span class="user-role">مدير النظام</span>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="main-header">
                <div class="header-left">
                    <h1 class="page-title" id="pageTitle">لوحة التحكم</h1>
                    <div class="breadcrumb">
                        <span>الرئيسية</span>
                        <i class="fas fa-chevron-left"></i>
                        <span id="currentPage">لوحة التحكم</span>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="header-actions">
                        <button class="btn btn-outline-primary" id="previewBtn">
                            <i class="fas fa-eye"></i>
                            معاينة الموقع
                        </button>
                        <button class="btn btn-primary" id="syncBtn">
                            <i class="fas fa-sync-alt"></i>
                            مزامنة
                        </button>
                    </div>
                    
                    <div class="status-indicator">
                        <div class="status-dot online" id="statusDot"></div>
                        <span id="statusText">متصل</span>
                    </div>
                </div>
            </header>

            <!-- Content Area -->
            <div class="content-area" id="contentArea">
                <!-- Dashboard Page -->
                <div class="page-content active" id="dashboard-page">
                    <div class="row">
                        <!-- Stats Cards -->
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="stat-card">
                                <div class="stat-icon movies">
                                    <i class="fas fa-film"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 id="moviesCount">0</h3>
                                    <p>إجمالي الأفلام</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="stat-card">
                                <div class="stat-icon series">
                                    <i class="fas fa-tv"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 id="seriesCount">0</h3>
                                    <p>إجمالي المسلسلات</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="stat-card">
                                <div class="stat-icon categories">
                                    <i class="fas fa-th-large"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 id="categoriesCount">0</h3>
                                    <p>التصنيفات</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="stat-card">
                                <div class="stat-icon sync">
                                    <i class="fas fa-sync-alt"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 id="lastSync">--</h3>
                                    <p>آخر مزامنة</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-clock"></i> النشاط الأخير</h5>
                                </div>
                                <div class="card-body">
                                    <div class="activity-list" id="activityList">
                                        <div class="activity-item">
                                            <div class="activity-icon">
                                                <i class="fas fa-plus text-success"></i>
                                            </div>
                                            <div class="activity-content">
                                                <p>تم إضافة فيلم جديد: "فيلم تجريبي"</p>
                                                <small>منذ 5 دقائق</small>
                                            </div>
                                        </div>
                                        
                                        <div class="activity-item">
                                            <div class="activity-icon">
                                                <i class="fas fa-edit text-warning"></i>
                                            </div>
                                            <div class="activity-content">
                                                <p>تم تحديث معلومات مسلسل "مسلسل تجريبي"</p>
                                                <small>منذ 15 دقيقة</small>
                                            </div>
                                        </div>
                                        
                                        <div class="activity-item">
                                            <div class="activity-icon">
                                                <i class="fas fa-sync text-info"></i>
                                            </div>
                                            <div class="activity-content">
                                                <p>تم مزامنة البيانات مع الموقع</p>
                                                <small>منذ ساعة</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-chart-pie"></i> إحصائيات سريعة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="quick-stats">
                                        <div class="quick-stat-item">
                                            <span class="label">أفلام الأكشن:</span>
                                            <span class="value">25</span>
                                        </div>
                                        <div class="quick-stat-item">
                                            <span class="label">أفلام الكوميديا:</span>
                                            <span class="value">18</span>
                                        </div>
                                        <div class="quick-stat-item">
                                            <span class="label">مسلسلات تركية:</span>
                                            <span class="value">12</span>
                                        </div>
                                        <div class="quick-stat-item">
                                            <span class="label">مسلسلات كورية:</span>
                                            <span class="value">8</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-tools"></i> إجراءات سريعة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="quick-actions">
                                        <button class="btn btn-primary btn-block mb-2" onclick="showAddMovieModal()">
                                            <i class="fas fa-plus"></i> إضافة فيلم جديد
                                        </button>
                                        <button class="btn btn-success btn-block mb-2" onclick="showAddSeriesModal()">
                                            <i class="fas fa-plus"></i> إضافة مسلسل جديد
                                        </button>
                                        <button class="btn btn-info btn-block mb-2" onclick="exportData()">
                                            <i class="fas fa-download"></i> تصدير البيانات
                                        </button>
                                        <button class="btn btn-warning btn-block" onclick="createBackup()">
                                            <i class="fas fa-shield-alt"></i> نسخة احتياطية
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Movies Page -->
                <div class="page-content" id="movies-page">
                    <div class="page-header">
                        <h2>إدارة الأفلام</h2>
                        <button class="btn btn-primary" onclick="showAddMovieModal()">
                            <i class="fas fa-plus"></i> إضافة فيلم جديد
                        </button>
                    </div>
                    
                    <div class="content-card">
                        <div class="card-header">
                            <div class="search-bar">
                                <input type="text" class="form-control" placeholder="البحث في الأفلام..." id="moviesSearch">
                                <button class="btn btn-outline-secondary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <div class="filter-options">
                                <select class="form-select" id="moviesFilter">
                                    <option value="">جميع التصنيفات</option>
                                    <option value="action">أكشن</option>
                                    <option value="comedy">كوميديا</option>
                                    <option value="horror">رعب</option>
                                    <option value="drama">دراما</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="card-body">
                            <div class="movies-grid" id="moviesGrid">
                                <!-- Movies will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Series Page -->
                <div class="page-content" id="series-page">
                    <div class="page-header">
                        <h2>إدارة المسلسلات</h2>
                        <button class="btn btn-primary" onclick="showAddSeriesModal()">
                            <i class="fas fa-plus"></i> إضافة مسلسل جديد
                        </button>
                    </div>
                    
                    <div class="content-card">
                        <div class="card-header">
                            <div class="search-bar">
                                <input type="text" class="form-control" placeholder="البحث في المسلسلات..." id="seriesSearch">
                                <button class="btn btn-outline-secondary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <div class="filter-options">
                                <select class="form-select" id="seriesFilter">
                                    <option value="">جميع التصنيفات</option>
                                    <option value="turkish">تركي</option>
                                    <option value="korean">كوري</option>
                                    <option value="american">أمريكي</option>
                                    <option value="arabic">عربي</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="card-body">
                            <div class="series-grid" id="seriesGrid">
                                <!-- Series will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Website Management Page -->
                <div class="page-content" id="website-page">
                    <div class="page-header">
                        <h2>إدارة الموقع</h2>
                        <div class="header-actions">
                            <button class="btn btn-success" id="publishBtn">
                                <i class="fas fa-upload"></i> نشر التحديثات
                            </button>
                            <button class="btn btn-info" id="previewWebsite">
                                <i class="fas fa-eye"></i> معاينة
                            </button>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="content-card">
                                <div class="card-header">
                                    <h5>محرر الموقع</h5>
                                </div>
                                <div class="card-body">
                                    <div class="editor-container">
                                        <div class="editor-toolbar">
                                            <button class="btn btn-sm btn-outline-secondary" onclick="openFile()">
                                                <i class="fas fa-folder-open"></i> فتح ملف
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="saveFile()">
                                                <i class="fas fa-save"></i> حفظ
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="formatCode()">
                                                <i class="fas fa-code"></i> تنسيق
                                            </button>
                                        </div>
                                        <textarea class="code-editor" id="codeEditor" placeholder="اختر ملف لتحريره..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4">
                            <div class="content-card">
                                <div class="card-header">
                                    <h5>ملفات المشروع</h5>
                                </div>
                                <div class="card-body">
                                    <div class="file-tree" id="fileTree">
                                        <!-- File tree will be loaded here -->
                                    </div>
                                </div>
                            </div>
                            
                            <div class="content-card mt-4">
                                <div class="card-header">
                                    <h5>معلومات الملف</h5>
                                </div>
                                <div class="card-body">
                                    <div class="file-info" id="fileInfo">
                                        <p>لم يتم اختيار ملف</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sync Page -->
                <div class="page-content" id="sync-page">
                    <div class="page-header">
                        <h2>المزامنة والنشر</h2>
                        <button class="btn btn-primary" onclick="startSync()">
                            <i class="fas fa-sync-alt"></i> بدء المزامنة
                        </button>
                    </div>
                    
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="content-card">
                                <div class="card-header">
                                    <h5>حالة المزامنة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="sync-status" id="syncStatus">
                                        <div class="status-item">
                                            <span class="label">الحالة:</span>
                                            <span class="value ready">جاهز</span>
                                        </div>
                                        <div class="status-item">
                                            <span class="label">آخر مزامنة:</span>
                                            <span class="value">لم يتم بعد</span>
                                        </div>
                                        <div class="status-item">
                                            <span class="label">الملفات المحدثة:</span>
                                            <span class="value">0</span>
                                        </div>
                                    </div>
                                    
                                    <div class="progress mt-3" style="display: none;" id="syncProgress">
                                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-6">
                            <div class="content-card">
                                <div class="card-header">
                                    <h5>إعدادات الخادم</h5>
                                </div>
                                <div class="card-body">
                                    <form id="serverSettingsForm">
                                        <div class="mb-3">
                                            <label class="form-label">عنوان الخادم</label>
                                            <input type="text" class="form-control" id="serverUrl" placeholder="https://example.com">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">اسم المستخدم</label>
                                            <input type="text" class="form-control" id="serverUsername">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">كلمة المرور</label>
                                            <input type="password" class="form-control" id="serverPassword">
                                        </div>
                                        <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Page -->
                <div class="page-content" id="settings-page">
                    <div class="page-header">
                        <h2>الإعدادات</h2>
                    </div>
                    
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="content-card">
                                <div class="card-header">
                                    <h5>إعدادات عامة</h5>
                                </div>
                                <div class="card-body">
                                    <form id="generalSettingsForm">
                                        <div class="mb-3">
                                            <label class="form-label">اسم الموقع</label>
                                            <input type="text" class="form-control" id="siteName" value="CinemaHub Pro">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">وصف الموقع</label>
                                            <textarea class="form-control" id="siteDescription" rows="3">أفضل موقع لمشاهدة الأفلام والمسلسلات</textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">لغة الموقع</label>
                                            <select class="form-select" id="siteLanguage">
                                                <option value="ar">العربية</option>
                                                <option value="en">English</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="autoSync">
                                                <label class="form-check-label" for="autoSync">
                                                    مزامنة تلقائية
                                                </label>
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4">
                            <div class="content-card">
                                <div class="card-header">
                                    <h5>معلومات البرنامج</h5>
                                </div>
                                <div class="card-body">
                                    <div class="app-info">
                                        <p><strong>الإصدار:</strong> <span id="appVersion">1.0.0</span></p>
                                        <p><strong>تاريخ البناء:</strong> 2024-01-15</p>
                                        <p><strong>المطور:</strong> CinemaHub Team</p>
                                    </div>
                                    
                                    <div class="mt-4">
                                        <button class="btn btn-outline-primary btn-block mb-2" onclick="checkForUpdates()">
                                            <i class="fas fa-download"></i> التحقق من التحديثات
                                        </button>
                                        <button class="btn btn-outline-secondary btn-block" onclick="showAbout()">
                                            <i class="fas fa-info-circle"></i> حول البرنامج
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modals will be added here -->
    <div id="modalsContainer"></div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3" id="toastContainer"></div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/app.js"></script>
    <script src="js/database.js"></script>
    <script src="js/sync.js"></script>
    <script src="js/modals.js"></script>
</body>
</html>
