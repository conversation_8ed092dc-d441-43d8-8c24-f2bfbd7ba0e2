<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CinemaHub - سينما هاب | شاهد أحدث الأفلام والمسلسلات مجاناً</title>
    
    <!-- Meta Tags -->
    <meta name="description" content="موقعك الأول لمشاهدة أحدث الأفلام والمسلسلات العربية والأجنبية بجودة عالية ومجاناً">
    <meta name="keywords" content="أفلام, مسلسلات, مشاهدة اونلاين, تحميل أفلام, أفلام عربية, مسلسلات تركية">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><rect width='100' height='100' rx='20' fill='%23e50914'/><text x='50' y='65' font-size='60' text-anchor='middle' fill='white'>🎬</text></svg>">
    
    <!-- External CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css" rel="stylesheet">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <style>
    /* ===== CSS Variables ===== */
    :root {
        --primary-color: #e50914;
        --secondary-color: #221f1f;
        --dark-color: #141414;
        --light-color: #ffffff;
        --gray-color: #757575;
        --gradient-primary: linear-gradient(135deg, #e50914 0%, #b20710 100%);
        --gradient-dark: linear-gradient(135deg, #141414 0%, #000000 100%);
        --gradient-overlay: linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.8) 100%);
        --font-family: 'Cairo', sans-serif;
        --transition: all 0.3s ease;
    }

    /* ===== Base Styles ===== */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: var(--font-family);
        background: var(--dark-color);
        color: var(--light-color);
        line-height: 1.6;
        direction: rtl;
    }

    /* ===== Loading Screen ===== */
    .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--dark-color);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease;
    }

    .loading-content {
        text-align: center;
    }

    .loading-logo {
        width: 80px;
        height: 80px;
        background: var(--gradient-primary);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        position: relative;
        animation: pulse 2s ease-in-out infinite;
    }

    .loading-logo i {
        font-size: 40px;
        color: var(--light-color);
    }

    .loading-text {
        font-size: 1.5rem;
        font-weight: 700;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 10px;
    }

    .loading-subtitle {
        color: var(--gray-color);
        margin-bottom: 20px;
    }

    .spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(229, 9, 20, 0.3);
        border-top: 3px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* ===== Header ===== */
    .header {
        background: rgba(20, 20, 20, 0.95);
        backdrop-filter: blur(10px);
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 1000;
        transition: var(--transition);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .navbar {
        padding: 0.5rem 0;
    }

    .navbar-brand {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: var(--light-color) !important;
        transition: var(--transition);
    }

    .navbar-brand:hover {
        transform: scale(1.05);
        color: var(--light-color) !important;
    }

    .logo-container {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .logo-icon {
        position: relative;
        width: 45px;
        height: 45px;
        background: var(--gradient-primary);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: logoGlow 3s ease-in-out infinite alternate;
    }

    .logo-icon .fa-film {
        font-size: 20px;
        color: var(--light-color);
    }

    .logo-play {
        position: absolute;
        top: -5px;
        right: -5px;
        font-size: 16px;
        color: #ffd700;
        animation: playPulse 2s ease-in-out infinite;
    }

    .brand-text {
        font-size: 1.2rem;
        font-weight: 700;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .brand-subtitle {
        font-size: 0.8rem;
        color: var(--gray-color);
        margin-top: -2px;
    }

    @keyframes logoGlow {
        0% { box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3); }
        100% { box-shadow: 0 6px 25px rgba(229, 9, 20, 0.6); }
    }

    @keyframes playPulse {
        0%, 100% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.2); opacity: 0.8; }
    }

    /* ===== Navigation ===== */
    .navbar-nav .nav-link {
        color: var(--light-color) !important;
        font-weight: 500;
        margin: 0 6px;
        padding: 6px 12px !important;
        border-radius: 6px;
        transition: var(--transition);
    }

    .navbar-nav .nav-link:hover,
    .navbar-nav .nav-link.active {
        background: var(--primary-color);
        color: var(--light-color) !important;
    }

    /* ===== Search ===== */
    .search-form {
        margin-right: 20px;
    }

    .search-input {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: var(--light-color);
        border-radius: 20px;
        padding: 8px 40px 8px 16px;
        width: 250px;
        transition: var(--transition);
    }

    .search-input:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: var(--primary-color);
        color: var(--light-color);
        outline: none;
        box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
    }

    .search-input::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .search-btn {
        position: absolute;
        left: 8px;
        background: transparent;
        border: none;
        color: var(--light-color);
        padding: 6px;
        border-radius: 50%;
        transition: var(--transition);
    }

    .search-btn:hover {
        background: var(--primary-color);
    }

    /* ===== Hero Section ===== */
    .hero-section {
        height: 60vh;
        position: relative;
        margin-top: 70px;
        background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.7)), url('https://image.tmdb.org/t/p/w1280/s16H6tpK2utvwDtzZ8Qy4qm5Emw.jpg');
        background-size: cover;
        background-position: center;
        display: flex;
        align-items: center;
    }

    .hero-content {
        max-width: 500px;
        padding: 0 20px;
    }

    .hero-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    }

    .hero-description {
        font-size: 1.1rem;
        margin-bottom: 30px;
        opacity: 0.9;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    }

    .hero-buttons {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
    }

    .btn-hero {
        padding: 12px 24px;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: var(--transition);
    }

    .btn-hero.primary {
        background: var(--primary-color);
        color: var(--light-color);
    }

    .btn-hero.secondary {
        background: rgba(255, 255, 255, 0.2);
        color: var(--light-color);
        backdrop-filter: blur(10px);
    }

    .btn-hero:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        color: var(--light-color);
    }

    /* ===== Main Content ===== */
    .main-content {
        padding: 40px 0;
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-bottom: 10px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--light-color);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .section-title i {
        color: var(--primary-color);
    }

    .view-all-btn {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        transition: var(--transition);
    }

    .view-all-btn:hover {
        color: #ff0a16;
    }

    /* ===== Movies Grid ===== */
    .movies-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }

    .movie-card {
        background: var(--secondary-color);
        border-radius: 8px;
        overflow: hidden;
        transition: var(--transition);
        cursor: pointer;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .movie-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(229, 9, 20, 0.3);
        border-color: var(--primary-color);
    }

    .movie-poster {
        position: relative;
        height: 270px;
        overflow: hidden;
    }

    .movie-poster img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: var(--transition);
    }

    .movie-card:hover .movie-poster img {
        transform: scale(1.05);
    }

    .movie-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: var(--transition);
    }

    .movie-card:hover .movie-overlay {
        opacity: 1;
    }

    .play-btn {
        background: var(--primary-color);
        color: var(--light-color);
        border: none;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        font-size: 1.2rem;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: var(--transition);
    }

    .play-btn:hover {
        background: #ff0a16;
        transform: scale(1.1);
    }

    .quality-badge {
        position: absolute;
        top: 8px;
        right: 8px;
        background: var(--primary-color);
        color: var(--light-color);
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.7rem;
        font-weight: 600;
    }

    .movie-info {
        padding: 15px;
    }

    .movie-title {
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 8px;
        color: var(--light-color);
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .movie-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.8rem;
        color: var(--gray-color);
    }

    .movie-year {
        background: rgba(255, 255, 255, 0.1);
        padding: 2px 6px;
        border-radius: 4px;
    }

    .movie-rating {
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .movie-rating i {
        color: #ffd700;
    }

    /* ===== Responsive ===== */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2rem;
        }
        
        .hero-description {
            font-size: 1rem;
        }
        
        .movies-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .search-input {
            width: 200px;
        }
        
        .logo-icon {
            width: 35px;
            height: 35px;
        }
        
        .brand-text {
            font-size: 1rem;
        }
        
        .brand-subtitle {
            font-size: 0.7rem;
        }
    }

    @media (max-width: 576px) {
        .movies-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }
        
        .hero-buttons {
            flex-direction: column;
            gap: 10px;
        }
        
        .search-input {
            width: 150px;
        }
        
        .brand-subtitle {
            display: none;
        }
    }
    </style>
</head>

<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-content">
            <div class="loading-logo">
                <i class="fas fa-film"></i>
            </div>
            <div class="loading-text">CinemaHub</div>
            <div class="loading-subtitle">سينما هاب</div>
            <div class="spinner"></div>
            <p style="margin-top: 20px; color: var(--gray-color);">جاري تحميل أحدث الأفلام والمسلسلات...</p>
        </div>
    </div>

    <!-- Header -->
    <header class="header">
        <nav class="navbar navbar-expand-lg">
            <div class="container">
                <a class="navbar-brand" href="index.html">
                    <div class="logo-container">
                        <div class="logo-icon">
                            <i class="fas fa-film"></i>
                            <i class="fas fa-play-circle logo-play"></i>
                        </div>
                        <div class="logo-text">
                            <div class="brand-text">CinemaHub</div>
                            <div class="brand-subtitle">سينما هاب</div>
                        </div>
                    </div>
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link active" href="#home">
                                <i class="fas fa-home"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#movies">
                                <i class="fas fa-film"></i>
                                أفلام
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#series">
                                <i class="fas fa-tv"></i>
                                مسلسلات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#top-rated">
                                <i class="fas fa-star"></i>
                                الأعلى تقييماً
                            </a>
                        </li>
                    </ul>

                    <form class="search-form d-flex position-relative">
                        <input class="search-input" type="search" placeholder="ابحث عن فيلم أو مسلسل...">
                        <button class="search-btn" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero-section" id="home">
        <div class="container">
            <div class="hero-content" data-aos="fade-up">
                <h1 class="hero-title">أفاتار: طريق الماء</h1>
                <p class="hero-description">بعد أكثر من عقد من الأحداث الأولى، تعلم عائلة سولي المشاكل التي تتبعهم والأطوال التي سيذهبون إليها للحفاظ على سلامة بعضهم البعض.</p>
                <div class="hero-buttons">
                    <a href="#" class="btn-hero primary">
                        <i class="fas fa-play"></i>
                        مشاهدة الآن
                    </a>
                    <a href="#" class="btn-hero secondary">
                        <i class="fas fa-info-circle"></i>
                        المزيد من المعلومات
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Latest Movies Section -->
            <section id="movies">
                <div class="section-header" data-aos="fade-up">
                    <h2 class="section-title">
                        <i class="fas fa-film"></i>
                        أحدث الأفلام
                    </h2>
                    <a href="#" class="view-all-btn">
                        عرض الكل
                        <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
                <div class="movies-grid" id="latest-movies">
                    <!-- Movies will be populated by JavaScript -->
                </div>
            </section>

            <!-- Latest Series Section -->
            <section id="series">
                <div class="section-header" data-aos="fade-up">
                    <h2 class="section-title">
                        <i class="fas fa-tv"></i>
                        أحدث المسلسلات
                    </h2>
                    <a href="#" class="view-all-btn">
                        عرض الكل
                        <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
                <div class="movies-grid" id="latest-series">
                    <!-- Series will be populated by JavaScript -->
                </div>
            </section>

            <!-- Top Rated Section -->
            <section id="top-rated">
                <div class="section-header" data-aos="fade-up">
                    <h2 class="section-title">
                        <i class="fas fa-star"></i>
                        الأعلى تقييماً
                    </h2>
                    <a href="#" class="view-all-btn">
                        عرض الكل
                        <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
                <div class="movies-grid" id="top-rated-content">
                    <!-- Top rated content will be populated by JavaScript -->
                </div>
            </section>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script>
        // ===== Demo Content =====
        const demoMovies = [
            {
                id: 1,
                title: 'أفاتار: طريق الماء',
                poster_path: 'https://image.tmdb.org/t/p/w500/s16H6tpK2utvwDtzZ8Qy4qm5Emw.jpg',
                release_date: '2022-12-16',
                vote_average: 7.7
            },
            {
                id: 2,
                title: 'الرجل العنكبوت: لا طريق للعودة',
                poster_path: 'https://image.tmdb.org/t/p/w500/1g0dhYtq4irTY1GPXvft6k4YLjm.jpg',
                release_date: '2021-12-17',
                vote_average: 8.4
            },
            {
                id: 3,
                title: 'توب غان: مافريك',
                poster_path: 'https://image.tmdb.org/t/p/w500/62HCnUTziyWcpDaBO2i1DX17ljH.jpg',
                release_date: '2022-05-27',
                vote_average: 8.3
            },
            {
                id: 4,
                title: 'دكتور سترينج في الكون المتعدد',
                poster_path: 'https://image.tmdb.org/t/p/w500/9Gtg2DzBhmYamXBS1hKAhiwbBKS.jpg',
                release_date: '2022-05-06',
                vote_average: 7.3
            },
            {
                id: 5,
                title: 'مينيونز: صعود جرو',
                poster_path: 'https://image.tmdb.org/t/p/w500/wKiOkZTN9lUUUNZLmtnwubZYONg.jpg',
                release_date: '2022-07-01',
                vote_average: 7.3
            },
            {
                id: 6,
                title: 'ثور: الحب والرعد',
                poster_path: 'https://image.tmdb.org/t/p/w500/pIkRyD18kl4FhoCNQuWxWu5cBLM.jpg',
                release_date: '2022-07-08',
                vote_average: 6.8
            },
            {
                id: 7,
                title: 'الوحوش الرائعة: أسرار دمبلدور',
                poster_path: 'https://image.tmdb.org/t/p/w500/jrgifaYeUtTnaH7NF5Drkgjg2MB.jpg',
                release_date: '2022-04-15',
                vote_average: 6.7
            },
            {
                id: 8,
                title: 'جوراسيك وورلد دومينيون',
                poster_path: 'https://image.tmdb.org/t/p/w500/kAVRgw7GgK1CfYEJq8ME6EvRIgU.jpg',
                release_date: '2022-06-10',
                vote_average: 7.0
            }
        ];

        const demoSeries = [
            {
                id: 1,
                name: 'بيت التنين',
                poster_path: 'https://image.tmdb.org/t/p/w500/z2yahl2uefxDCl0nogcRBstwruJ.jpg',
                first_air_date: '2022-08-21',
                vote_average: 8.5
            },
            {
                id: 2,
                name: 'حلقات القوة',
                poster_path: 'https://image.tmdb.org/t/p/w500/mYLOqiStMxDK3fYZFirgrMt8z5d.jpg',
                first_air_date: '2022-09-02',
                vote_average: 7.3
            },
            {
                id: 3,
                name: 'أشياء غريبة',
                poster_path: 'https://image.tmdb.org/t/p/w500/49WJfeN0moxb9IPfGn8AIqMGskD.jpg',
                first_air_date: '2016-07-15',
                vote_average: 8.7
            },
            {
                id: 4,
                name: 'الدب',
                poster_path: 'https://image.tmdb.org/t/p/w500/zPIug5giU8oug6Xes5K1sTfQJxY.jpg',
                first_air_date: '2022-06-23',
                vote_average: 8.3
            },
            {
                id: 5,
                name: 'أوبي وان كينوبي',
                poster_path: 'https://image.tmdb.org/t/p/w500/qJRB789ceLryrLvOKrZqLKr2CGf.jpg',
                first_air_date: '2022-05-27',
                vote_average: 7.2
            },
            {
                id: 6,
                name: 'مون نايت',
                poster_path: 'https://image.tmdb.org/t/p/w500/x6FsYvt33846IQnDSLxSCS3ZXNy.jpg',
                first_air_date: '2022-03-30',
                vote_average: 7.3
            },
            {
                id: 7,
                name: 'يوفوريا',
                poster_path: 'https://image.tmdb.org/t/p/w500/jtnfNzqZwN4E32FGGxx1YZaBWWf.jpg',
                first_air_date: '2019-06-16',
                vote_average: 8.4
            },
            {
                id: 8,
                name: 'ذا بويز',
                poster_path: 'https://image.tmdb.org/t/p/w500/mY7SeH4HFFxW1hiI6cWuwCRKptN.jpg',
                first_air_date: '2019-07-26',
                vote_average: 8.7
            }
        ];

        // ===== Initialize App =====
        document.addEventListener('DOMContentLoaded', function() {
            // Hide loading screen
            setTimeout(() => {
                const loadingScreen = document.getElementById('loadingScreen');
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 500);
            }, 2000);

            // Initialize AOS
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true,
                offset: 100
            });

            // Load content
            loadMovies();
            loadSeries();
            loadTopRated();

            // Initialize header scroll effect
            window.addEventListener('scroll', function() {
                const header = document.querySelector('.header');
                if (window.scrollY > 100) {
                    header.style.background = 'rgba(20, 20, 20, 0.98)';
                } else {
                    header.style.background = 'rgba(20, 20, 20, 0.95)';
                }
            });

            // Search functionality
            const searchForm = document.querySelector('.search-form');
            searchForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const query = document.querySelector('.search-input').value.trim();
                if (query) {
                    alert('البحث عن: ' + query);
                }
            });

            // Smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });

        // ===== Content Loading Functions =====
        function loadMovies() {
            const container = document.getElementById('latest-movies');
            container.innerHTML = demoMovies.map(movie => createMovieCard(movie, 'movie')).join('');
        }

        function loadSeries() {
            const container = document.getElementById('latest-series');
            container.innerHTML = demoSeries.map(series => createMovieCard(series, 'tv')).join('');
        }

        function loadTopRated() {
            const container = document.getElementById('top-rated-content');
            const topRated = [...demoMovies.slice(0, 4), ...demoSeries.slice(0, 4)]
                .sort((a, b) => b.vote_average - a.vote_average);
            container.innerHTML = topRated.map(item => createMovieCard(item, item.title ? 'movie' : 'tv')).join('');
        }

        function createMovieCard(item, type) {
            const title = item.title || item.name;
            const releaseDate = item.release_date || item.first_air_date;
            const year = releaseDate ? releaseDate.split('-')[0] : 'غير محدد';
            const rating = item.vote_average ? item.vote_average.toFixed(1) : 'N/A';
            const qualityBadge = type === 'movie' ? 'HD' : 'مسلسل';

            return `
                <div class="movie-card" data-aos="fade-up" onclick="openMovieDetails('${item.id}', '${title}')">
                    <div class="movie-poster">
                        <img src="${item.poster_path}" alt="${title}" loading="lazy">
                        <div class="quality-badge">${qualityBadge}</div>
                        <div class="movie-overlay">
                            <button class="play-btn" onclick="event.stopPropagation(); playMovie('${title}', '${item.id}')">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="movie-info">
                        <h3 class="movie-title">${title}</h3>
                        <div class="movie-meta">
                            <span class="movie-year">${year}</span>
                            <div class="movie-rating">
                                <i class="fas fa-star"></i>
                                <span>${rating}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function playMovie(title, movieId) {
            // Redirect to movie details page
            window.location.href = `movie-details.html?id=${movieId}&title=${encodeURIComponent(title)}`;
        }

        function openMovieDetails(movieId, title) {
            // Redirect to movie details page
            window.location.href = `movie-details.html?id=${movieId}&title=${encodeURIComponent(title)}`;
        }
    </script>
</body>
</html>
