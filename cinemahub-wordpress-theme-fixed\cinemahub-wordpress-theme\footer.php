<!-- Footer -->
<footer class="footer">
    <div class="container">
        <div class="footer-content">
            <!-- About Section -->
            <div class="footer-section">
                <h4><?php bloginfo('name'); ?></h4>
                <p><?php bloginfo('description'); ?></p>
                <p>أفضل موقع لمشاهدة الأفلام والمسلسلات العربية والأجنبية بجودة عالية ومجاناً.</p>
                
                <!-- Social Media Links -->
                <div class="social-links" style="margin-top: 1rem;">
                    <a href="#" style="color: var(--gray-400); font-size: 1.2rem; margin-left: 1rem; transition: var(--transition);" onmouseover="this.style.color='#1877f2'" onmouseout="this.style.color='var(--gray-400)'">
                        <i class="fab fa-facebook"></i>
                    </a>
                    <a href="#" style="color: var(--gray-400); font-size: 1.2rem; margin-left: 1rem; transition: var(--transition);" onmouseover="this.style.color='#1da1f2'" onmouseout="this.style.color='var(--gray-400)'">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" style="color: var(--gray-400); font-size: 1.2rem; margin-left: 1rem; transition: var(--transition);" onmouseover="this.style.color='#e4405f'" onmouseout="this.style.color='var(--gray-400)'">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" style="color: var(--gray-400); font-size: 1.2rem; margin-left: 1rem; transition: var(--transition);" onmouseover="this.style.color='#ff0000'" onmouseout="this.style.color='var(--gray-400)'">
                        <i class="fab fa-youtube"></i>
                    </a>
                </div>
            </div>
            
            <!-- Quick Links -->
            <div class="footer-section">
                <h4>روابط سريعة</h4>
                <ul class="footer-links">
                    <li><a href="<?php echo esc_url(home_url('/')); ?>"><i class="fas fa-home"></i> الرئيسية</a></li>
                    <li><a href="<?php echo get_post_type_archive_link('movie'); ?>"><i class="fas fa-film"></i> الأفلام</a></li>
                    <li><a href="<?php echo get_post_type_archive_link('series'); ?>"><i class="fas fa-tv"></i> المسلسلات</a></li>
                    <li><a href="<?php echo esc_url(home_url('/top-rated')); ?>"><i class="fas fa-star"></i> الأعلى تقييماً</a></li>
                    <li><a href="<?php echo esc_url(home_url('/contact')); ?>"><i class="fas fa-envelope"></i> اتصل بنا</a></li>
                </ul>
            </div>
            
            <!-- Categories -->
            <div class="footer-section">
                <h4>التصنيفات</h4>
                <ul class="footer-links">
                    <?php
                    // Get movie genres for footer
                    $footer_genres = get_terms(array(
                        'taxonomy' => 'movie_genre',
                        'hide_empty' => false,
                        'number' => 5
                    ));
                    
                    if (!empty($footer_genres)) :
                        foreach ($footer_genres as $genre) :
                    ?>
                        <li><a href="<?php echo get_term_link($genre); ?>"><i class="fas fa-tag"></i> <?php echo esc_html($genre->name); ?></a></li>
                    <?php 
                        endforeach;
                    else :
                        // Default categories if no terms exist
                        $default_footer_categories = array(
                            array('name' => 'أفلام الأكشن', 'icon' => 'fas fa-fist-raised'),
                            array('name' => 'أفلام الكوميديا', 'icon' => 'fas fa-laugh'),
                            array('name' => 'أفلام الرعب', 'icon' => 'fas fa-skull-crossbones'),
                            array('name' => 'مسلسلات تركية', 'icon' => 'fas fa-flag'),
                            array('name' => 'مسلسلات كورية', 'icon' => 'fas fa-yin-yang')
                        );
                        
                        foreach ($default_footer_categories as $category) :
                    ?>
                        <li><a href="#"><i class="<?php echo esc_attr($category['icon']); ?>"></i> <?php echo esc_html($category['name']); ?></a></li>
                    <?php 
                        endforeach;
                    endif; 
                    ?>
                </ul>
            </div>
            
            <!-- Widget Area -->
            <div class="footer-section">
                <?php if (is_active_sidebar('footer-4')) : ?>
                    <?php dynamic_sidebar('footer-4'); ?>
                <?php else : ?>
                    <h4>معلومات إضافية</h4>
                    <ul class="footer-links">
                        <li><a href="<?php echo esc_url(home_url('/privacy-policy')); ?>"><i class="fas fa-shield-alt"></i> سياسة الخصوصية</a></li>
                        <li><a href="<?php echo esc_url(home_url('/terms-of-service')); ?>"><i class="fas fa-file-contract"></i> شروط الاستخدام</a></li>
                        <li><a href="<?php echo esc_url(home_url('/dmca')); ?>"><i class="fas fa-copyright"></i> DMCA</a></li>
                        <li><a href="<?php echo esc_url(home_url('/sitemap')); ?>"><i class="fas fa-sitemap"></i> خريطة الموقع</a></li>
                        <li><a href="<?php echo esc_url(home_url('/rss')); ?>"><i class="fas fa-rss"></i> RSS Feed</a></li>
                    </ul>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Footer Bottom -->
        <div class="footer-bottom">
            <p>
                © <?php echo date('Y'); ?> <?php bloginfo('name'); ?>. جميع الحقوق محفوظة.
                <?php if (function_exists('wp_get_theme')) : ?>
                    | تم التطوير بواسطة <a href="#" style="color: var(--primary-color);">CinemaHub Team</a>
                <?php endif; ?>
            </p>
        </div>
    </div>
</footer>

<!-- Back to Top Button -->
<button id="backToTop" class="back-to-top" style="position: fixed; bottom: 2rem; left: 2rem; width: 50px; height: 50px; background: var(--gradient-primary); border: none; border-radius: 50%; color: white; font-size: 1.2rem; cursor: pointer; opacity: 0; visibility: hidden; transition: all 0.3s ease; z-index: 1000; box-shadow: var(--shadow-lg);">
    <i class="fas fa-arrow-up"></i>
</button>

<style>
/* Back to Top Button */
.back-to-top.show {
    opacity: 1 !important;
    visibility: visible !important;
}

.back-to-top:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: var(--shadow-glow);
}

/* Footer Animations */
.footer-section {
    animation: fadeInUp 0.6s ease;
}

.footer-section:nth-child(2) {
    animation-delay: 0.1s;
}

.footer-section:nth-child(3) {
    animation-delay: 0.2s;
}

.footer-section:nth-child(4) {
    animation-delay: 0.3s;
}

/* Social Links Hover Effects */
.social-links a {
    display: inline-block;
    transition: all 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-3px) scale(1.2);
}

/* Footer Links Hover Effects */
.footer-links a:hover {
    color: var(--primary-color) !important;
    transform: translateX(-5px);
}

.footer-links a:hover i {
    color: var(--primary-color);
    transform: scale(1.2);
}

/* Footer Responsive */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
    
    .back-to-top {
        bottom: 1rem !important;
        left: 1rem !important;
        width: 45px !important;
        height: 45px !important;
        font-size: 1rem !important;
    }
}

/* Widget Styles */
.footer-section .widget {
    margin-bottom: 1rem;
}

.footer-section .widget-title {
    color: var(--light-color);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-section .widget ul {
    list-style: none;
    padding: 0;
}

.footer-section .widget li {
    margin-bottom: 0.5rem;
}

.footer-section .widget a {
    color: var(--gray-400);
    text-decoration: none;
    transition: var(--transition);
    font-size: var(--font-size-sm);
}

.footer-section .widget a:hover {
    color: var(--primary-color);
    transform: translateX(-3px);
}
</style>

<script>
// Back to Top Button Functionality
document.addEventListener('DOMContentLoaded', function() {
    const backToTopBtn = document.getElementById('backToTop');
    
    if (backToTopBtn) {
        // Show/hide button based on scroll position
        window.addEventListener('scroll', function() {
            if (window.scrollY > 300) {
                backToTopBtn.classList.add('show');
            } else {
                backToTopBtn.classList.remove('show');
            }
        });
        
        // Smooth scroll to top when clicked
        backToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
    
    // Header scroll effect
    const header = document.getElementById('header');
    if (header) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });
    }
    
    // Initialize AOS if available
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });
    }
    
    // Movie cards hover effects
    const movieCards = document.querySelectorAll('.movie-card');
    movieCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    console.log('CinemaHub Pro WordPress Theme loaded successfully!');
});
</script>

<?php wp_footer(); ?>
</body>
</html>
