<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="مشاهدة الأفلام والمسلسلات - CinemaHub Pro">
    <title>مشاهدة - CinemaHub Pro | سينما هاب برو</title>
    
    <!-- External CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
    /* ===== CSS Variables ===== */
    :root {
        --primary-color: #e50914;
        --primary-dark: #b20710;
        --secondary-color: #221f1f;
        --dark-color: #141414;
        --darker-color: #0a0a0a;
        --light-color: #ffffff;
        --gray-color: #757575;
        --gray-light: #b3b3b3;
        --success-color: #28a745;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
        
        --gradient-primary: linear-gradient(135deg, #e50914 0%, #b20710 100%);
        --gradient-dark: linear-gradient(135deg, #141414 0%, #0a0a0a 100%);
        
        --font-family: 'Cairo', sans-serif;
        --border-radius: 0.5rem;
        --border-radius-lg: 0.75rem;
        --border-radius-xl: 1rem;
        --border-radius-2xl: 1.5rem;
        
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        
        --transition: all 0.3s ease;
        --z-fixed: 1030;
    }

    /* ===== Base Styles ===== */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    html {
        scroll-behavior: smooth;
        font-size: 16px;
    }

    body {
        font-family: var(--font-family);
        background: var(--dark-color);
        color: var(--light-color);
        line-height: 1.6;
        direction: rtl;
        overflow-x: hidden;
    }

    /* ===== Header ===== */
    .header {
        background: rgba(20, 20, 20, 0.95);
        backdrop-filter: blur(20px);
        position: fixed;
        top: 0;
        width: 100%;
        z-index: var(--z-fixed);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        padding: 1rem 0;
    }

    .navbar-brand {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: var(--light-color) !important;
    }

    .logo-container {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .logo-icon {
        width: 50px;
        height: 50px;
        background: var(--gradient-primary);
        border-radius: var(--border-radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .logo-icon .fa-film {
        font-size: 1.5rem;
        color: var(--light-color);
    }

    .brand-text {
        font-size: 1.25rem;
        font-weight: 800;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .back-btn {
        background: var(--gradient-primary);
        color: var(--light-color);
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: var(--border-radius-xl);
        font-weight: 600;
        transition: var(--transition);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
    }

    .back-btn:hover {
        background: var(--primary-dark);
        color: var(--light-color);
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    /* ===== Watch Container ===== */
    .watch-container {
        margin-top: 80px;
        padding: 2rem 0;
        min-height: calc(100vh - 80px);
    }

    .movie-info-header {
        background: var(--gradient-dark);
        padding: 2rem;
        border-radius: var(--border-radius-2xl);
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .movie-title {
        font-size: 2rem;
        font-weight: 800;
        margin-bottom: 1rem;
        color: var(--light-color);
    }

    .movie-meta {
        display: flex;
        gap: 2rem;
        flex-wrap: wrap;
        margin-bottom: 1rem;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(255, 255, 255, 0.1);
        padding: 0.5rem 1rem;
        border-radius: var(--border-radius-xl);
        font-size: 0.875rem;
        font-weight: 500;
    }

    .meta-item i {
        color: var(--primary-color);
    }

    /* ===== Video Player ===== */
    .video-container {
        background: var(--secondary-color);
        border-radius: var(--border-radius-2xl);
        overflow: hidden;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.1);
        box-shadow: var(--shadow-2xl);
    }

    .video-player {
        width: 100%;
        height: 500px;
        background: #000;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .video-placeholder {
        text-align: center;
        color: var(--gray-light);
    }

    .video-placeholder i {
        font-size: 4rem;
        margin-bottom: 1rem;
        color: var(--primary-color);
    }

    .video-placeholder h3 {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .video-placeholder p {
        font-size: 1rem;
        opacity: 0.8;
    }

    /* ===== Server Selection ===== */
    .server-selection {
        background: var(--secondary-color);
        padding: 2rem;
        border-radius: var(--border-radius-2xl);
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .server-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        color: var(--light-color);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .server-title i {
        color: var(--primary-color);
    }

    .servers-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .server-btn {
        background: var(--gradient-dark);
        border: 2px solid rgba(255, 255, 255, 0.1);
        color: var(--light-color);
        padding: 1.5rem 1rem;
        border-radius: var(--border-radius-xl);
        font-weight: 600;
        transition: var(--transition);
        cursor: pointer;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .server-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: var(--gradient-primary);
        transition: var(--transition);
        z-index: 1;
    }

    .server-btn:hover::before,
    .server-btn.active::before {
        left: 0;
    }

    .server-btn:hover,
    .server-btn.active {
        border-color: var(--primary-color);
        transform: translateY(-3px);
        box-shadow: var(--shadow-lg);
    }

    .server-btn .server-content {
        position: relative;
        z-index: 2;
    }

    .server-name {
        font-size: 1.125rem;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .server-status {
        font-size: 0.875rem;
        opacity: 0.8;
    }

    .server-status.online {
        color: var(--success-color);
    }

    .server-status.offline {
        color: var(--danger-color);
    }

    /* ===== Quality Selection ===== */
    .quality-selection {
        background: var(--secondary-color);
        padding: 2rem;
        border-radius: var(--border-radius-2xl);
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .quality-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        color: var(--light-color);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .quality-title i {
        color: var(--primary-color);
    }

    .quality-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }

    .quality-btn {
        background: var(--gradient-dark);
        border: 2px solid rgba(255, 255, 255, 0.1);
        color: var(--light-color);
        padding: 1rem;
        border-radius: var(--border-radius-lg);
        font-weight: 600;
        transition: var(--transition);
        cursor: pointer;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .quality-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: var(--gradient-primary);
        transition: var(--transition);
        z-index: 1;
    }

    .quality-btn:hover::before,
    .quality-btn.active::before {
        left: 0;
    }

    .quality-btn:hover,
    .quality-btn.active {
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .quality-btn .quality-content {
        position: relative;
        z-index: 2;
    }

    .quality-name {
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }

    .quality-size {
        font-size: 0.75rem;
        opacity: 0.8;
    }

    /* ===== Download Section ===== */
    .download-section {
        background: var(--secondary-color);
        padding: 2rem;
        border-radius: var(--border-radius-2xl);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .download-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        color: var(--light-color);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .download-title i {
        color: var(--primary-color);
    }

    .download-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .download-btn {
        background: var(--gradient-primary);
        color: var(--light-color);
        border: none;
        padding: 1rem 1.5rem;
        border-radius: var(--border-radius-xl);
        font-weight: 600;
        transition: var(--transition);
        cursor: pointer;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .download-btn:hover {
        background: var(--primary-dark);
        color: var(--light-color);
        transform: translateY(-3px);
        box-shadow: var(--shadow-lg);
    }

    /* ===== Responsive Design ===== */
    @media (max-width: 768px) {
        .video-player {
            height: 300px;
        }

        .movie-meta {
            flex-direction: column;
            gap: 1rem;
        }

        .servers-grid,
        .quality-grid,
        .download-grid {
            grid-template-columns: 1fr;
        }

        .movie-title {
            font-size: 1.5rem;
        }

        .server-selection,
        .quality-selection,
        .download-section {
            padding: 1.5rem;
        }
    }

    @media (max-width: 576px) {
        .video-player {
            height: 250px;
        }

        .movie-info-header {
            padding: 1.5rem;
        }

        .server-selection,
        .quality-selection,
        .download-section {
            padding: 1rem;
        }
    }
    </style>
</head>

<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <a class="navbar-brand" href="index-pro.html">
                    <div class="logo-container">
                        <div class="logo-icon">
                            <i class="fas fa-film"></i>
                        </div>
                        <span class="brand-text">CinemaHub Pro</span>
                    </div>
                </a>

                <a href="index-pro.html" class="back-btn">
                    <i class="fas fa-arrow-right"></i>
                    العودة للرئيسية
                </a>
            </div>
        </div>
    </header>

    <!-- Watch Container -->
    <div class="watch-container">
        <div class="container">
            <!-- Movie Info Header -->
            <div class="movie-info-header">
                <h1 class="movie-title" id="movieTitle">أفاتار: طريق الماء</h1>
                <div class="movie-meta">
                    <div class="meta-item">
                        <i class="fas fa-calendar"></i>
                        <span>2022</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-star"></i>
                        <span>7.7/10</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-clock"></i>
                        <span>3 ساعات 12 دقيقة</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-film"></i>
                        <span>خيال علمي، مغامرة</span>
                    </div>
                </div>
                <p class="movie-description">بعد أكثر من عقد من الأحداث الأولى، تعلم عائلة سولي المشاكل التي تتبعهم والأطوال التي سيذهبون إليها للحفاظ على سلامة بعضهم البعض.</p>
            </div>

            <!-- Video Player -->
            <div class="video-container">
                <div class="video-player" id="videoPlayer">
                    <div class="video-placeholder">
                        <i class="fas fa-play-circle"></i>
                        <h3>اختر السيرفر والجودة لبدء المشاهدة</h3>
                        <p>يرجى اختيار السيرفر المفضل والجودة المناسبة من الأسفل</p>
                    </div>
                </div>
            </div>

            <!-- Server Selection -->
            <div class="server-selection">
                <h3 class="server-title">
                    <i class="fas fa-server"></i>
                    اختر السيرفر
                </h3>
                <div class="servers-grid">
                    <button class="server-btn active" onclick="selectServer(1, this)">
                        <div class="server-content">
                            <div class="server-name">
                                <i class="fas fa-bolt"></i>
                                سيرفر سريع
                            </div>
                            <div class="server-status online">متاح - سرعة عالية</div>
                        </div>
                    </button>

                    <button class="server-btn" onclick="selectServer(2, this)">
                        <div class="server-content">
                            <div class="server-name">
                                <i class="fas fa-shield-alt"></i>
                                سيرفر مستقر
                            </div>
                            <div class="server-status online">متاح - آمن ومستقر</div>
                        </div>
                    </button>

                    <button class="server-btn" onclick="selectServer(3, this)">
                        <div class="server-content">
                            <div class="server-name">
                                <i class="fas fa-crown"></i>
                                سيرفر VIP
                            </div>
                            <div class="server-status online">متاح - جودة مميزة</div>
                        </div>
                    </button>

                    <button class="server-btn" onclick="selectServer(4, this)">
                        <div class="server-content">
                            <div class="server-name">
                                <i class="fas fa-globe"></i>
                                سيرفر محلي
                            </div>
                            <div class="server-status online">متاح - محتوى مدبلج</div>
                        </div>
                    </button>
                </div>
            </div>

            <!-- Quality Selection -->
            <div class="quality-selection">
                <h3 class="quality-title">
                    <i class="fas fa-cog"></i>
                    اختر الجودة
                </h3>
                <div class="quality-grid">
                    <button class="quality-btn" onclick="selectQuality('480p', this)">
                        <div class="quality-content">
                            <div class="quality-name">480p موبايل</div>
                            <div class="quality-size">650 MB</div>
                        </div>
                    </button>

                    <button class="quality-btn active" onclick="selectQuality('720p', this)">
                        <div class="quality-content">
                            <div class="quality-name">720p HD</div>
                            <div class="quality-size">1.2 GB</div>
                        </div>
                    </button>

                    <button class="quality-btn" onclick="selectQuality('1080p', this)">
                        <div class="quality-content">
                            <div class="quality-name">1080p Full HD</div>
                            <div class="quality-size">2.5 GB</div>
                        </div>
                    </button>

                    <button class="quality-btn" onclick="selectQuality('4k', this)">
                        <div class="quality-content">
                            <div class="quality-name">4K Ultra HD</div>
                            <div class="quality-size">8.2 GB</div>
                        </div>
                    </button>

                    <button class="quality-btn" onclick="selectQuality('dubbed', this)">
                        <div class="quality-content">
                            <div class="quality-name">مدبلج عربي</div>
                            <div class="quality-size">1.8 GB</div>
                        </div>
                    </button>

                    <button class="quality-btn" onclick="selectQuality('subtitled', this)">
                        <div class="quality-content">
                            <div class="quality-name">مترجم عربي</div>
                            <div class="quality-size">2.1 GB</div>
                        </div>
                    </button>
                </div>
            </div>

            <!-- Download Section -->
            <div class="download-section">
                <h3 class="download-title">
                    <i class="fas fa-download"></i>
                    تحميل الفيلم
                </h3>
                <div class="download-grid">
                    <a href="#" class="download-btn" onclick="downloadMovie('480p')">
                        <i class="fas fa-download"></i>
                        تحميل 480p
                    </a>

                    <a href="#" class="download-btn" onclick="downloadMovie('720p')">
                        <i class="fas fa-download"></i>
                        تحميل 720p HD
                    </a>

                    <a href="#" class="download-btn" onclick="downloadMovie('1080p')">
                        <i class="fas fa-download"></i>
                        تحميل 1080p Full HD
                    </a>

                    <a href="#" class="download-btn" onclick="downloadMovie('4k')">
                        <i class="fas fa-download"></i>
                        تحميل 4K Ultra HD
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Global variables
        let currentServer = 1;
        let currentQuality = '720p';
        let movieData = {};

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadMovieData();
            initializePlayer();
        });

        // Load movie data from URL parameters
        function loadMovieData() {
            const urlParams = new URLSearchParams(window.location.search);
            const movieId = urlParams.get('id');
            const movieTitle = urlParams.get('title');
            const movieType = urlParams.get('type') || 'movie';

            if (movieTitle) {
                document.getElementById('movieTitle').textContent = decodeURIComponent(movieTitle);
                document.title = `مشاهدة ${decodeURIComponent(movieTitle)} - CinemaHub Pro`;
            }

            movieData = {
                id: movieId,
                title: movieTitle,
                type: movieType
            };
        }

        // Initialize video player
        function initializePlayer() {
            updateVideoPlayer();
        }

        // Select server
        function selectServer(serverId, element) {
            // Remove active class from all server buttons
            document.querySelectorAll('.server-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Add active class to selected server
            element.classList.add('active');

            currentServer = serverId;
            updateVideoPlayer();

            showNotification(`تم اختيار السيرفر ${serverId}`, 'success');
        }

        // Select quality
        function selectQuality(quality, element) {
            // Remove active class from all quality buttons
            document.querySelectorAll('.quality-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Add active class to selected quality
            element.classList.add('active');

            currentQuality = quality;
            updateVideoPlayer();

            showNotification(`تم اختيار جودة ${quality}`, 'success');
        }

        // Update video player
        function updateVideoPlayer() {
            const videoPlayer = document.getElementById('videoPlayer');

            // Simulate video loading
            videoPlayer.innerHTML = `
                <div class="video-placeholder">
                    <i class="fas fa-spinner fa-spin"></i>
                    <h3>جاري تحميل الفيديو...</h3>
                    <p>السيرفر: ${currentServer} | الجودة: ${currentQuality}</p>
                </div>
            `;

            // Simulate loading delay
            setTimeout(() => {
                // Create video element (placeholder for now)
                videoPlayer.innerHTML = `
                    <div style="width: 100%; height: 100%; background: #000; display: flex; align-items: center; justify-content: center; flex-direction: column;">
                        <div style="text-align: center; color: white;">
                            <i class="fas fa-play-circle" style="font-size: 4rem; color: #e50914; margin-bottom: 1rem;"></i>
                            <h3>مشغل الفيديو</h3>
                            <p>السيرفر ${currentServer} | الجودة ${currentQuality}</p>
                            <button onclick="startPlayback()" style="background: #e50914; color: white; border: none; padding: 1rem 2rem; border-radius: 0.5rem; margin-top: 1rem; cursor: pointer; font-weight: 600;">
                                <i class="fas fa-play"></i> بدء التشغيل
                            </button>
                        </div>
                    </div>
                `;
            }, 1500);
        }

        // Start playback
        function startPlayback() {
            const videoPlayer = document.getElementById('videoPlayer');

            // In a real implementation, this would load the actual video
            videoPlayer.innerHTML = `
                <div style="width: 100%; height: 100%; background: #000; display: flex; align-items: center; justify-content: center;">
                    <div style="text-align: center; color: white;">
                        <i class="fas fa-film" style="font-size: 4rem; color: #e50914; margin-bottom: 1rem;"></i>
                        <h3>الفيديو قيد التشغيل</h3>
                        <p>هنا سيتم عرض الفيديو الفعلي</p>
                        <div style="margin-top: 2rem;">
                            <button onclick="pausePlayback()" style="background: #e50914; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.5rem; margin: 0 0.5rem; cursor: pointer;">
                                <i class="fas fa-pause"></i>
                            </button>
                            <button onclick="stopPlayback()" style="background: #dc3545; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.5rem; margin: 0 0.5rem; cursor: pointer;">
                                <i class="fas fa-stop"></i>
                            </button>
                            <button onclick="toggleFullscreen()" style="background: #17a2b8; color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.5rem; margin: 0 0.5rem; cursor: pointer;">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;

            showNotification('بدء تشغيل الفيديو', 'success');
        }

        // Pause playback
        function pausePlayback() {
            showNotification('تم إيقاف الفيديو مؤقتاً', 'info');
        }

        // Stop playback
        function stopPlayback() {
            updateVideoPlayer();
            showNotification('تم إيقاف الفيديو', 'warning');
        }

        // Toggle fullscreen
        function toggleFullscreen() {
            const videoPlayer = document.getElementById('videoPlayer');

            if (!document.fullscreenElement) {
                videoPlayer.requestFullscreen().catch(err => {
                    showNotification('لا يمكن تفعيل وضع ملء الشاشة', 'error');
                });
            } else {
                document.exitFullscreen();
            }
        }

        // Download movie
        function downloadMovie(quality) {
            showNotification(`بدء تحميل الفيلم بجودة ${quality}`, 'success');

            // In a real implementation, this would trigger the actual download
            setTimeout(() => {
                showNotification('رابط التحميل جاهز!', 'info');
            }, 2000);
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} position-fixed`;
            notification.style.cssText = `
                top: 100px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                animation: slideInRight 0.3s ease;
                box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            `;

            const iconMap = {
                'success': 'check-circle',
                'error': 'exclamation-circle',
                'warning': 'exclamation-triangle',
                'info': 'info-circle'
            };

            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${iconMap[type]} me-2"></i>
                    <span>${message}</span>
                    <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOutRight 0.3s ease';
                    setTimeout(() => notification.remove(), 300);
                }
            }, 5000);
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case ' ':
                    e.preventDefault();
                    // Toggle play/pause
                    break;
                case 'f':
                case 'F':
                    toggleFullscreen();
                    break;
                case 'Escape':
                    if (document.fullscreenElement) {
                        document.exitFullscreen();
                    }
                    break;
            }
        });

        console.log('Watch page loaded successfully!');
    </script>
</body>
</html>
