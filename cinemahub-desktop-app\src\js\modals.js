/**
 * CinemaHub Pro Desktop Application
 * Modals and Dialog Management
 * 
 * @version 1.0.0
 * <AUTHOR> Team
 */

class ModalManager {
    constructor() {
        this.activeModals = new Map();
        this.init();
    }

    /**
     * Initialize modal manager
     */
    init() {
        // Create modals container if it doesn't exist
        if (!document.getElementById('modalsContainer')) {
            const container = document.createElement('div');
            container.id = 'modalsContainer';
            document.body.appendChild(container);
        }

        // Handle escape key to close modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeTopModal();
            }
        });

        console.log('Modal Manager initialized');
    }

    /**
     * Show add movie modal
     */
    showAddMovieModal() {
        const modalId = 'addMovieModal';
        const modalHTML = `
            <div class="modal fade" id="${modalId}" tabindex="-1" data-bs-backdrop="static">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content" style="background: var(--surface-color); border: 1px solid rgba(148, 163, 184, 0.2);">
                        <div class="modal-header" style="border-bottom: 1px solid rgba(148, 163, 184, 0.1);">
                            <h5 class="modal-title" style="color: var(--light-color);">
                                <i class="fas fa-plus text-primary me-2"></i>
                                إضافة فيلم جديد
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="addMovieForm">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label">عنوان الفيلم *</label>
                                            <input type="text" class="form-control" name="title" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">الوصف</label>
                                            <textarea class="form-control" name="description" rows="4"></textarea>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">سنة الإنتاج</label>
                                                    <input type="number" class="form-control" name="year" min="1900" max="2030">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">المدة (دقيقة)</label>
                                                    <input type="number" class="form-control" name="duration" min="1">
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">التقييم</label>
                                                    <input type="number" class="form-control" name="rating" min="0" max="10" step="0.1">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">التصنيف</label>
                                                    <select class="form-select" name="genre">
                                                        <option value="">اختر التصنيف</option>
                                                        <option value="action">أكشن</option>
                                                        <option value="comedy">كوميديا</option>
                                                        <option value="horror">رعب</option>
                                                        <option value="drama">دراما</option>
                                                        <option value="romance">رومانسي</option>
                                                        <option value="sci-fi">خيال علمي</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">البلد</label>
                                                    <input type="text" class="form-control" name="country">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">المخرج</label>
                                                    <input type="text" class="form-control" name="director">
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">طاقم التمثيل</label>
                                            <input type="text" class="form-control" name="cast" placeholder="مفصولة بفواصل">
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">رابط الإعلان</label>
                                                    <input type="url" class="form-control" name="trailer_url">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">رابط المشاهدة</label>
                                                    <input type="url" class="form-control" name="watch_url">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">صورة البوستر</label>
                                            <div class="poster-upload-area" style="border: 2px dashed rgba(148, 163, 184, 0.3); border-radius: var(--border-radius-lg); padding: 2rem; text-align: center; cursor: pointer; transition: var(--transition);" onclick="document.getElementById('posterInput').click()">
                                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-2"></i>
                                                <p class="text-muted mb-0">اضغط لرفع صورة البوستر</p>
                                                <small class="text-muted">أو اسحب الصورة هنا</small>
                                            </div>
                                            <input type="file" id="posterInput" name="poster" accept="image/*" style="display: none;">
                                            <input type="hidden" name="poster_url">
                                        </div>
                                        
                                        <div class="poster-preview" style="display: none;">
                                            <img id="posterPreview" src="" alt="معاينة البوستر" style="width: 100%; border-radius: var(--border-radius-lg); box-shadow: var(--shadow-lg);">
                                            <button type="button" class="btn btn-sm btn-outline-danger mt-2 w-100" onclick="modalManager.clearPosterPreview()">
                                                <i class="fas fa-trash"></i> إزالة الصورة
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer" style="border-top: 1px solid rgba(148, 163, 184, 0.1);">
                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                            <button type="button" class="btn btn-primary" onclick="modalManager.saveMovie()">
                                <i class="fas fa-save"></i> حفظ الفيلم
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.showModal(modalId, modalHTML);
        this.setupPosterUpload();
    }

    /**
     * Show add series modal
     */
    showAddSeriesModal() {
        const modalId = 'addSeriesModal';
        const modalHTML = `
            <div class="modal fade" id="${modalId}" tabindex="-1" data-bs-backdrop="static">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content" style="background: var(--surface-color); border: 1px solid rgba(148, 163, 184, 0.2);">
                        <div class="modal-header" style="border-bottom: 1px solid rgba(148, 163, 184, 0.1);">
                            <h5 class="modal-title" style="color: var(--light-color);">
                                <i class="fas fa-plus text-primary me-2"></i>
                                إضافة مسلسل جديد
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="addSeriesForm">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label">عنوان المسلسل *</label>
                                            <input type="text" class="form-control" name="title" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">الوصف</label>
                                            <textarea class="form-control" name="description" rows="4"></textarea>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">سنة الإنتاج</label>
                                                    <input type="number" class="form-control" name="year" min="1900" max="2030">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">التقييم</label>
                                                    <input type="number" class="form-control" name="rating" min="0" max="10" step="0.1">
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label class="form-label">عدد المواسم</label>
                                                    <input type="number" class="form-control" name="seasons" min="1">
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label class="form-label">عدد الحلقات</label>
                                                    <input type="number" class="form-control" name="episodes" min="1">
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label class="form-label">الحالة</label>
                                                    <select class="form-select" name="status">
                                                        <option value="ongoing">مستمر</option>
                                                        <option value="completed">مكتمل</option>
                                                        <option value="cancelled">ملغي</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">التصنيف</label>
                                                    <select class="form-select" name="genre">
                                                        <option value="">اختر التصنيف</option>
                                                        <option value="turkish">تركي</option>
                                                        <option value="korean">كوري</option>
                                                        <option value="american">أمريكي</option>
                                                        <option value="arabic">عربي</option>
                                                        <option value="indian">هندي</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">البلد</label>
                                                    <input type="text" class="form-control" name="country">
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">رابط الإعلان</label>
                                                    <input type="url" class="form-control" name="trailer_url">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">رابط المشاهدة</label>
                                                    <input type="url" class="form-control" name="watch_url">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">صورة البوستر</label>
                                            <div class="poster-upload-area" style="border: 2px dashed rgba(148, 163, 184, 0.3); border-radius: var(--border-radius-lg); padding: 2rem; text-align: center; cursor: pointer; transition: var(--transition);" onclick="document.getElementById('seriesPosterInput').click()">
                                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-2"></i>
                                                <p class="text-muted mb-0">اضغط لرفع صورة البوستر</p>
                                                <small class="text-muted">أو اسحب الصورة هنا</small>
                                            </div>
                                            <input type="file" id="seriesPosterInput" name="poster" accept="image/*" style="display: none;">
                                            <input type="hidden" name="poster_url">
                                        </div>
                                        
                                        <div class="series-poster-preview" style="display: none;">
                                            <img id="seriesPosterPreview" src="" alt="معاينة البوستر" style="width: 100%; border-radius: var(--border-radius-lg); box-shadow: var(--shadow-lg);">
                                            <button type="button" class="btn btn-sm btn-outline-danger mt-2 w-100" onclick="modalManager.clearSeriesPosterPreview()">
                                                <i class="fas fa-trash"></i> إزالة الصورة
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer" style="border-top: 1px solid rgba(148, 163, 184, 0.1);">
                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                            <button type="button" class="btn btn-primary" onclick="modalManager.saveSeries()">
                                <i class="fas fa-save"></i> حفظ المسلسل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.showModal(modalId, modalHTML);
        this.setupSeriesPosterUpload();
    }

    /**
     * Show confirmation dialog
     */
    showConfirmDialog(title, message, onConfirm, onCancel = null) {
        const modalId = 'confirmDialog';
        const modalHTML = `
            <div class="modal fade" id="${modalId}" tabindex="-1" data-bs-backdrop="static">
                <div class="modal-dialog">
                    <div class="modal-content" style="background: var(--surface-color); border: 1px solid rgba(148, 163, 184, 0.2);">
                        <div class="modal-header" style="border-bottom: 1px solid rgba(148, 163, 184, 0.1);">
                            <h5 class="modal-title" style="color: var(--light-color);">
                                <i class="fas fa-question-circle text-warning me-2"></i>
                                ${title}
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p style="color: var(--gray-300); margin-bottom: 0;">${message}</p>
                        </div>
                        <div class="modal-footer" style="border-top: 1px solid rgba(148, 163, 184, 0.1);">
                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" onclick="modalManager.handleCancel('${modalId}')">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                            <button type="button" class="btn btn-danger" onclick="modalManager.handleConfirm('${modalId}')">
                                <i class="fas fa-check"></i> تأكيد
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.showModal(modalId, modalHTML);
        
        // Store callbacks
        this.activeModals.set(modalId, { onConfirm, onCancel });
    }

    /**
     * Show generic modal
     */
    showModal(modalId, modalHTML) {
        // Remove existing modal if present
        this.closeModal(modalId);
        
        // Add modal to container
        const container = document.getElementById('modalsContainer');
        container.insertAdjacentHTML('beforeend', modalHTML);
        
        // Show modal
        const modalElement = document.getElementById(modalId);
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
        
        // Store modal instance
        this.activeModals.set(modalId, modal);
        
        // Handle modal hidden event
        modalElement.addEventListener('hidden.bs.modal', () => {
            this.closeModal(modalId);
        });
    }

    /**
     * Close modal
     */
    closeModal(modalId) {
        const modalElement = document.getElementById(modalId);
        if (modalElement) {
            const modal = this.activeModals.get(modalId);
            if (modal && typeof modal.hide === 'function') {
                modal.hide();
            }
            modalElement.remove();
            this.activeModals.delete(modalId);
        }
    }

    /**
     * Close top modal
     */
    closeTopModal() {
        const modals = document.querySelectorAll('.modal.show');
        if (modals.length > 0) {
            const topModal = modals[modals.length - 1];
            const modalId = topModal.id;
            this.closeModal(modalId);
        }
    }

    /**
     * Setup poster upload for movies
     */
    setupPosterUpload() {
        const input = document.getElementById('posterInput');
        const uploadArea = document.querySelector('.poster-upload-area');
        const preview = document.querySelector('.poster-preview');
        const previewImg = document.getElementById('posterPreview');

        if (input && uploadArea) {
            input.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    this.handlePosterUpload(file, preview, previewImg, uploadArea);
                }
            });

            // Drag and drop
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.style.borderColor = 'var(--primary-color)';
                uploadArea.style.backgroundColor = 'rgba(220, 38, 38, 0.1)';
            });

            uploadArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                uploadArea.style.borderColor = 'rgba(148, 163, 184, 0.3)';
                uploadArea.style.backgroundColor = 'transparent';
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.style.borderColor = 'rgba(148, 163, 184, 0.3)';
                uploadArea.style.backgroundColor = 'transparent';
                
                const file = e.dataTransfer.files[0];
                if (file && file.type.startsWith('image/')) {
                    this.handlePosterUpload(file, preview, previewImg, uploadArea);
                }
            });
        }
    }

    /**
     * Setup poster upload for series
     */
    setupSeriesPosterUpload() {
        const input = document.getElementById('seriesPosterInput');
        const uploadArea = document.querySelector('.poster-upload-area');
        const preview = document.querySelector('.series-poster-preview');
        const previewImg = document.getElementById('seriesPosterPreview');

        if (input && uploadArea) {
            input.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    this.handlePosterUpload(file, preview, previewImg, uploadArea);
                }
            });
        }
    }

    /**
     * Handle poster upload
     */
    handlePosterUpload(file, preview, previewImg, uploadArea) {
        const reader = new FileReader();
        reader.onload = (e) => {
            previewImg.src = e.target.result;
            preview.style.display = 'block';
            uploadArea.style.display = 'none';
        };
        reader.readAsDataURL(file);
    }

    /**
     * Clear poster preview
     */
    clearPosterPreview() {
        const input = document.getElementById('posterInput');
        const uploadArea = document.querySelector('.poster-upload-area');
        const preview = document.querySelector('.poster-preview');
        
        input.value = '';
        preview.style.display = 'none';
        uploadArea.style.display = 'block';
    }

    /**
     * Clear series poster preview
     */
    clearSeriesPosterPreview() {
        const input = document.getElementById('seriesPosterInput');
        const uploadArea = document.querySelector('.poster-upload-area');
        const preview = document.querySelector('.series-poster-preview');
        
        input.value = '';
        preview.style.display = 'none';
        uploadArea.style.display = 'block';
    }

    /**
     * Save movie
     */
    async saveMovie() {
        try {
            const form = document.getElementById('addMovieForm');
            const formData = new FormData(form);
            const movieData = Object.fromEntries(formData.entries());

            // Validate required fields
            if (!movieData.title) {
                throw new Error('عنوان الفيلم مطلوب');
            }

            // Show loading
            const saveBtn = document.querySelector('#addMovieModal .btn-primary');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
            saveBtn.disabled = true;

            // Save to database (placeholder)
            console.log('Saving movie:', movieData);
            
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Close modal
            this.closeModal('addMovieModal');
            
            // Show success message
            if (window.app) {
                window.app.showToast('تم حفظ الفيلم بنجاح', 'success');
                window.app.loadMoviesData(); // Refresh movies list
            }

        } catch (error) {
            console.error('Failed to save movie:', error);
            if (window.app) {
                window.app.showToast(error.message || 'خطأ في حفظ الفيلم', 'error');
            }
        }
    }

    /**
     * Save series
     */
    async saveSeries() {
        try {
            const form = document.getElementById('addSeriesForm');
            const formData = new FormData(form);
            const seriesData = Object.fromEntries(formData.entries());

            // Validate required fields
            if (!seriesData.title) {
                throw new Error('عنوان المسلسل مطلوب');
            }

            // Show loading
            const saveBtn = document.querySelector('#addSeriesModal .btn-primary');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
            saveBtn.disabled = true;

            // Save to database (placeholder)
            console.log('Saving series:', seriesData);
            
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Close modal
            this.closeModal('addSeriesModal');
            
            // Show success message
            if (window.app) {
                window.app.showToast('تم حفظ المسلسل بنجاح', 'success');
                window.app.loadSeriesData(); // Refresh series list
            }

        } catch (error) {
            console.error('Failed to save series:', error);
            if (window.app) {
                window.app.showToast(error.message || 'خطأ في حفظ المسلسل', 'error');
            }
        }
    }

    /**
     * Handle confirm dialog
     */
    handleConfirm(modalId) {
        const modal = this.activeModals.get(modalId);
        if (modal && modal.onConfirm) {
            modal.onConfirm();
        }
        this.closeModal(modalId);
    }

    /**
     * Handle cancel dialog
     */
    handleCancel(modalId) {
        const modal = this.activeModals.get(modalId);
        if (modal && modal.onCancel) {
            modal.onCancel();
        }
        this.closeModal(modalId);
    }
}

// Create global instance
const modalManager = new ModalManager();

// Global functions for HTML onclick events
window.modalManager = modalManager;
window.showAddMovieModal = () => modalManager.showAddMovieModal();
window.showAddSeriesModal = () => modalManager.showAddSeriesModal();
