<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحويل الأيقونة إلى PNG</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            text-align: center;
            max-width: 600px;
        }
        
        h1 {
            color: #dc2626;
            margin-bottom: 30px;
        }
        
        .icon-preview {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            display: inline-block;
        }
        
        #iconDisplay {
            width: 256px;
            height: 256px;
            border: 2px solid #dc2626;
            border-radius: 10px;
            background: white;
        }
        
        button {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: right;
            line-height: 1.8;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: rgba(220, 38, 38, 0.1);
            border-radius: 8px;
            border-right: 4px solid #dc2626;
        }
        
        .success {
            background: rgba(34, 197, 94, 0.2);
            border-color: #22c55e;
            color: #22c55e;
        }
        
        canvas {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 تحويل أيقونة CinemaHub Pro إلى PNG</h1>
        
        <div class="icon-preview">
            <div id="iconDisplay"></div>
        </div>
        
        <div>
            <button onclick="loadAndConvert()">🔄 تحميل وتحويل الأيقونة</button>
            <button onclick="downloadPNG()" id="downloadBtn" style="display:none;">💾 تحميل PNG</button>
        </div>
        
        <canvas id="canvas" width="512" height="512"></canvas>
        
        <div class="instructions">
            <h3>📋 التعليمات:</h3>
            <div class="step">
                <strong>1.</strong> اضغط على "تحميل وتحويل الأيقونة"
            </div>
            <div class="step">
                <strong>2.</strong> اضغط على "تحميل PNG" عندما يظهر الزر
            </div>
            <div class="step">
                <strong>3.</strong> احفظ الملف باسم <code>icon.png</code> في مجلد <code>assets</code>
            </div>
            <div class="step">
                <strong>4.</strong> شغل <code>create-installer.bat</code> لإنشاء ملف التثبيت
            </div>
        </div>
    </div>

    <script>
        const svgContent = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="bgGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b91c1c;stop-opacity:1" />
    </radialGradient>
    <linearGradient id="filmGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#374151;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e293b;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <circle cx="256" cy="256" r="240" fill="url(#bgGradient)" stroke="#991b1b" stroke-width="8"/>
  
  <rect x="80" y="180" width="352" height="152" fill="#1e293b" rx="16"/>
  <rect x="80" y="180" width="352" height="152" fill="url(#filmGradient)" rx="16"/>
  <rect x="80" y="180" width="352" height="152" fill="none" stroke="#0f172a" stroke-width="4" rx="16"/>
  
  <rect x="100" y="200" width="20" height="20" fill="#ffffff" rx="4"/>
  <rect x="100" y="240" width="20" height="20" fill="#ffffff" rx="4"/>
  <rect x="100" y="280" width="20" height="20" fill="#ffffff" rx="4"/>
  
  <rect x="392" y="200" width="20" height="20" fill="#ffffff" rx="4"/>
  <rect x="392" y="240" width="20" height="20" fill="#ffffff" rx="4"/>
  <rect x="392" y="280" width="20" height="20" fill="#ffffff" rx="4"/>
  
  <rect x="140" y="200" width="80" height="60" fill="#4b5563" stroke="#374151" stroke-width="2" rx="4"/>
  <rect x="240" y="200" width="80" height="60" fill="#4b5563" stroke="#374151" stroke-width="2" rx="4"/>
  <rect x="340" y="200" width="80" height="60" fill="#4b5563" stroke="#374151" stroke-width="2" rx="4"/>
  
  <rect x="140" y="272" width="80" height="60" fill="#4b5563" stroke="#374151" stroke-width="2" rx="4"/>
  <rect x="240" y="272" width="80" height="60" fill="#4b5563" stroke="#374151" stroke-width="2" rx="4"/>
  <rect x="340" y="272" width="80" height="60" fill="#4b5563" stroke="#374151" stroke-width="2" rx="4"/>
  
  <circle cx="258" cy="258" r="50" fill="rgba(0,0,0,0.3)"/>
  <circle cx="256" cy="256" r="50" fill="#ffffff" opacity="0.95"/>
  <polygon points="240,230 240,282 290,256" fill="#dc2626"/>
  
  <circle cx="150" cy="150" r="3" fill="#ffffff" opacity="0.8"/>
  <circle cx="350" cy="150" r="3" fill="#ffffff" opacity="0.8"/>
  <circle cx="150" cy="350" r="3" fill="#ffffff" opacity="0.8"/>
  <circle cx="350" cy="350" r="3" fill="#ffffff" opacity="0.8"/>
  
  <text x="258" y="122" text-anchor="middle" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="rgba(0,0,0,0.5)">CinemaHub</text>
  <text x="258" y="422" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="normal" fill="rgba(0,0,0,0.5)">Pro Desktop</text>
  
  <text x="256" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="#ffffff">CinemaHub</text>
  <text x="256" y="420" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="normal" fill="#ffffff">Pro Desktop</text>
</svg>`;

        function loadAndConvert() {
            // Display SVG in preview
            document.getElementById('iconDisplay').innerHTML = svgContent.replace('width="512" height="512"', 'width="256" height="256"');
            
            // Convert to PNG
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            const img = new Image();
            const svgBlob = new Blob([svgContent], {type: 'image/svg+xml;charset=utf-8'});
            const url = URL.createObjectURL(svgBlob);
            
            img.onload = function() {
                ctx.drawImage(img, 0, 0, 512, 512);
                URL.revokeObjectURL(url);
                
                // Show download button
                document.getElementById('downloadBtn').style.display = 'inline-block';
                
                showSuccess('تم تحويل الأيقونة بنجاح! يمكنك الآن تحميلها.');
            };
            
            img.src = url;
        }
        
        function downloadPNG() {
            const canvas = document.getElementById('canvas');
            const link = document.createElement('a');
            link.download = 'icon.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
            
            showSuccess('تم تحميل icon.png بنجاح! احفظه في مجلد assets');
        }
        
        function showSuccess(message) {
            const existingSuccess = document.querySelector('.success-message');
            if (existingSuccess) {
                existingSuccess.remove();
            }
            
            const successDiv = document.createElement('div');
            successDiv.className = 'step success success-message';
            successDiv.innerHTML = `<strong>✅</strong> ${message}`;
            
            document.querySelector('.instructions').appendChild(successDiv);
            
            setTimeout(() => {
                if (successDiv.parentNode) {
                    successDiv.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
