<?php
/**
 * Search results template
 *
 * @package CinemaHub Pro
 */

get_header(); ?>

<div style="margin-top: 80px; padding: 2rem 0;">
    <div class="container">
        <div class="section-header" style="margin-bottom: 3rem; text-align: center;">
            <h1 class="section-title" style="font-size: 2.5rem; margin-bottom: 1rem;">
                <i class="fas fa-search"></i>
                نتائج البحث
            </h1>
            
            <?php if (get_search_query()) : ?>
                <p style="color: var(--gray-400); font-size: 1.2rem;">
                    نتائج البحث عن: "<span style="color: var(--primary-color); font-weight: 600;"><?php echo get_search_query(); ?></span>"
                </p>
            <?php endif; ?>
        </div>

        <!-- Search Form -->
        <div style="max-width: 600px; margin: 0 auto 3rem auto;">
            <form role="search" method="get" action="<?php echo home_url('/'); ?>" style="position: relative;">
                <input type="search" name="s" placeholder="ابحث عن فيلم أو مسلسل..." value="<?php echo get_search_query(); ?>" style="width: 100%; padding: 1.5rem 4rem 1.5rem 2rem; border: 2px solid var(--border-color); border-radius: 50px; background: var(--surface-color); color: var(--light-color); font-size: 1.1rem; outline: none; transition: all 0.3s ease;">
                <button type="submit" style="position: absolute; left: 1.5rem; top: 50%; transform: translateY(-50%); background: var(--gradient-primary); border: none; color: white; font-size: 1.2rem; cursor: pointer; padding: 1rem; border-radius: 50%; width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>

        <?php if (have_posts()) : ?>
            <!-- Results Count -->
            <div style="margin-bottom: 2rem; text-align: center;">
                <p style="color: var(--gray-400); font-size: 1rem;">
                    تم العثور على <span style="color: var(--primary-color); font-weight: 600;"><?php echo $wp_query->found_posts; ?></span> نتيجة
                </p>
            </div>

            <!-- Search Results -->
            <div class="search-results">
                <?php while (have_posts()) : the_post(); ?>
                    <?php
                    $post_type = get_post_type();
                    $rating = '';
                    $year = '';
                    $meta_info = '';
                    
                    if ($post_type === 'movie') {
                        $rating = get_post_meta(get_the_ID(), '_movie_rating', true);
                        $year = get_post_meta(get_the_ID(), '_movie_year', true);
                        $duration = get_post_meta(get_the_ID(), '_movie_duration', true);
                        if ($duration) {
                            $meta_info = $duration . ' دقيقة';
                        }
                    } elseif ($post_type === 'series') {
                        $rating = get_post_meta(get_the_ID(), '_series_rating', true);
                        $year = get_post_meta(get_the_ID(), '_series_year', true);
                        $seasons = get_post_meta(get_the_ID(), '_series_seasons', true);
                        $episodes = get_post_meta(get_the_ID(), '_series_episodes', true);
                        if ($seasons && $episodes) {
                            $meta_info = $seasons . ' موسم، ' . $episodes . ' حلقة';
                        } elseif ($seasons) {
                            $meta_info = $seasons . ' موسم';
                        }
                    }
                    ?>
                    
                    <div class="search-result-item" style="display: flex; gap: 2rem; margin-bottom: 2rem; padding: 2rem; background: var(--dark-color); border-radius: 15px; transition: all 0.3s ease; border: 2px solid transparent;">
                        <div class="result-poster" style="flex-shrink: 0;">
                            <?php if (has_post_thumbnail()) : ?>
                                <a href="<?php the_permalink(); ?>">
                                    <?php the_post_thumbnail('medium', array('style' => 'width: 150px; height: 200px; object-fit: cover; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.3);')); ?>
                                </a>
                            <?php else : ?>
                                <a href="<?php the_permalink(); ?>">
                                    <div style="width: 150px; height: 200px; background: var(--gradient-primary); border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.3);">
                                        <i class="fas fa-<?php echo $post_type === 'series' ? 'tv' : 'film'; ?>"></i>
                                    </div>
                                </a>
                            <?php endif; ?>
                        </div>
                        
                        <div class="result-content" style="flex: 1; min-width: 0;">
                            <div class="result-header" style="margin-bottom: 1rem;">
                                <h2 style="margin: 0 0 0.5rem 0; font-size: 1.5rem; font-weight: 700;">
                                    <a href="<?php the_permalink(); ?>" style="color: var(--light-color); text-decoration: none; transition: color 0.3s ease;">
                                        <?php the_title(); ?>
                                    </a>
                                </h2>
                                
                                <div class="result-type" style="display: inline-block; background: var(--gradient-primary); color: white; padding: 0.25rem 0.75rem; border-radius: 15px; font-size: 0.8rem; font-weight: 600; margin-bottom: 0.5rem;">
                                    <?php echo $post_type === 'series' ? 'مسلسل' : 'فيلم'; ?>
                                </div>
                            </div>
                            
                            <div class="result-meta" style="display: flex; flex-wrap: wrap; gap: 1rem; margin-bottom: 1rem;">
                                <?php if ($year) : ?>
                                    <span style="color: var(--gray-400); display: flex; align-items: center; gap: 0.25rem;">
                                        <i class="fas fa-calendar"></i>
                                        <?php echo esc_html($year); ?>
                                    </span>
                                <?php endif; ?>
                                
                                <?php if ($meta_info) : ?>
                                    <span style="color: var(--gray-400); display: flex; align-items: center; gap: 0.25rem;">
                                        <i class="fas fa-<?php echo $post_type === 'series' ? 'list' : 'clock'; ?>"></i>
                                        <?php echo esc_html($meta_info); ?>
                                    </span>
                                <?php endif; ?>
                                
                                <?php if ($rating) : ?>
                                    <span style="color: var(--warning-color); display: flex; align-items: center; gap: 0.25rem; font-weight: 600;">
                                        <i class="fas fa-star"></i>
                                        <?php echo esc_html($rating); ?>/10
                                    </span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="result-excerpt" style="color: var(--gray-300); line-height: 1.6; margin-bottom: 1.5rem;">
                                <?php if (get_the_excerpt()) : ?>
                                    <?php the_excerpt(); ?>
                                <?php else : ?>
                                    <?php echo wp_trim_words(get_the_content(), 30, '...'); ?>
                                <?php endif; ?>
                            </div>
                            
                            <div class="result-genres" style="margin-bottom: 1rem;">
                                <?php
                                $taxonomy = $post_type === 'series' ? 'series_genre' : 'movie_genre';
                                $genres = get_the_terms(get_the_ID(), $taxonomy);
                                if ($genres && !is_wp_error($genres)) :
                                    foreach (array_slice($genres, 0, 3) as $genre) :
                                ?>
                                    <span style="background: rgba(220, 38, 38, 0.1); color: var(--primary-color); padding: 0.25rem 0.75rem; border-radius: 15px; font-size: 0.8rem; margin-left: 0.5rem; display: inline-block; margin-bottom: 0.25rem;">
                                        <?php echo esc_html($genre->name); ?>
                                    </span>
                                <?php 
                                    endforeach;
                                endif; 
                                ?>
                            </div>
                            
                            <div class="result-actions">
                                <a href="<?php the_permalink(); ?>" style="background: var(--gradient-primary); color: white; padding: 0.75rem 1.5rem; border-radius: 25px; text-decoration: none; font-weight: 600; display: inline-flex; align-items: center; gap: 0.5rem; transition: all 0.3s ease;">
                                    <i class="fas fa-play"></i>
                                    <?php echo $post_type === 'series' ? 'مشاهدة المسلسل' : 'مشاهدة الفيلم'; ?>
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endwhile; ?>
            </div>

            <!-- Pagination -->
            <div class="pagination-wrapper" style="text-align: center; margin-top: 3rem;">
                <?php
                the_posts_pagination(array(
                    'prev_text' => '<i class="fas fa-arrow-right"></i> السابق',
                    'next_text' => 'التالي <i class="fas fa-arrow-left"></i>',
                ));
                ?>
            </div>

        <?php else : ?>
            <!-- No Results -->
            <div style="text-align: center; padding: 4rem 2rem; color: var(--gray-400);">
                <i class="fas fa-search" style="font-size: 4rem; margin-bottom: 2rem; color: var(--primary-color); opacity: 0.5;"></i>
                <h3 style="font-size: 1.5rem; margin-bottom: 1rem; color: var(--light-color);">لم يتم العثور على نتائج</h3>
                
                <?php if (get_search_query()) : ?>
                    <p style="margin-bottom: 2rem; font-size: 1.1rem;">
                        لم نتمكن من العثور على أي نتائج للبحث عن "<span style="color: var(--primary-color); font-weight: 600;"><?php echo get_search_query(); ?></span>"
                    </p>
                <?php else : ?>
                    <p style="margin-bottom: 2rem; font-size: 1.1rem;">
                        يرجى إدخال كلمة للبحث
                    </p>
                <?php endif; ?>
                
                <div style="max-width: 400px; margin: 0 auto;">
                    <h4 style="margin-bottom: 1rem; color: var(--light-color);">نصائح للبحث:</h4>
                    <ul style="text-align: right; color: var(--gray-400); line-height: 1.8;">
                        <li>تأكد من كتابة الكلمات بشكل صحيح</li>
                        <li>جرب كلمات مختلفة أو أكثر عمومية</li>
                        <li>ابحث باستخدام اسم الفيلم أو المسلسل</li>
                        <li>جرب البحث باستخدام اسم الممثل أو المخرج</li>
                    </ul>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.search-result-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.search-result-item h2 a:hover {
    color: var(--primary-color);
}

.result-actions a:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(220, 38, 38, 0.3);
}

.search-form input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

@media (max-width: 768px) {
    .search-result-item {
        flex-direction: column;
        text-align: center;
    }
    
    .result-poster {
        align-self: center;
    }
    
    .result-poster img,
    .result-poster div {
        width: 120px !important;
        height: 160px !important;
    }
    
    .result-meta {
        justify-content: center;
    }
}
</style>

<?php get_footer(); ?>
