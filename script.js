// CinemaHub JavaScript

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initializeHeader();
    initializeSearch();
    initializeMovieCards();
    initializeScrollEffects();
    initializeDropdowns();
});

// Header functionality
function initializeHeader() {
    const header = document.querySelector('.header');
    
    // Header scroll effect
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            header.style.background = 'rgba(0, 0, 0, 0.98)';
        } else {
            header.style.background = 'rgba(0, 0, 0, 0.95)';
        }
    });
}

// Search functionality
function initializeSearch() {
    const searchInput = document.querySelector('.search-box input');
    const searchButton = document.querySelector('.search-box button');
    
    // Search on button click
    searchButton.addEventListener('click', function() {
        performSearch(searchInput.value);
    });
    
    // Search on Enter key
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch(searchInput.value);
        }
    });
}

// Perform search function
function performSearch(query) {
    if (query.trim() === '') {
        alert('يرجى إدخال كلمة البحث');
        return;
    }
    
    console.log('البحث عن:', query);
    // Here you would implement actual search functionality
    alert(`البحث عن: ${query}`);
}

// Movie cards functionality
function initializeMovieCards() {
    const movieCards = document.querySelectorAll('.movie-card');
    const categoryCards = document.querySelectorAll('.category-card');
    
    // Movie card click events
    movieCards.forEach(card => {
        const playBtn = card.querySelector('.play-btn');
        
        if (playBtn) {
            playBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                const movieTitle = card.querySelector('h3').textContent;
                alert(`تشغيل: ${movieTitle}`);
            });
        }
        
        // Card hover effects
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Category card click events
    categoryCards.forEach(card => {
        card.addEventListener('click', function() {
            const categoryTitle = this.querySelector('h3').textContent;
            alert(`الانتقال إلى: ${categoryTitle}`);
        });
    });
}

// Scroll effects
function initializeScrollEffects() {
    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    document.querySelectorAll('.category-card, .movie-card').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Dropdown functionality
function initializeDropdowns() {
    const dropdowns = document.querySelectorAll('.dropdown');
    
    dropdowns.forEach(dropdown => {
        const menu = dropdown.querySelector('.dropdown-menu');
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!dropdown.contains(e.target)) {
                menu.style.opacity = '0';
                menu.style.visibility = 'hidden';
                menu.style.transform = 'translateY(-10px)';
            }
        });
        
        // Dropdown menu item clicks
        const menuItems = dropdown.querySelectorAll('.dropdown-menu a');
        menuItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const itemText = this.textContent;
                alert(`الانتقال إلى: ${itemText}`);
            });
        });
    });
}

// Auth buttons functionality
document.addEventListener('DOMContentLoaded', function() {
    const loginBtn = document.querySelector('.btn-outline');
    const registerBtn = document.querySelector('.btn-primary');
    
    if (loginBtn) {
        loginBtn.addEventListener('click', function() {
            alert('صفحة تسجيل الدخول');
        });
    }
    
    if (registerBtn) {
        registerBtn.addEventListener('click', function() {
            alert('صفحة إنشاء حساب جديد');
        });
    }
});

// Utility functions
function showLoading() {
    // Show loading spinner
    console.log('Loading...');
}

function hideLoading() {
    // Hide loading spinner
    console.log('Loading complete');
}

// Error handling
window.addEventListener('error', function(e) {
    console.error('خطأ في الصفحة:', e.error);
});

// Performance optimization
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Optimized scroll handler
const optimizedScrollHandler = debounce(function() {
    // Handle scroll events efficiently
}, 100);

window.addEventListener('scroll', optimizedScrollHandler);

// Mobile menu toggle (if needed)
function toggleMobileMenu() {
    const nav = document.querySelector('.nav');
    nav.classList.toggle('mobile-active');
}

// Add to favorites functionality
function addToFavorites(movieId) {
    // Add movie to favorites
    console.log('تم إضافة الفيلم للمفضلة:', movieId);
    alert('تم إضافة الفيلم للمفضلة');
}

// Rating functionality
function rateMovie(movieId, rating) {
    // Rate movie
    console.log(`تقييم الفيلم ${movieId}: ${rating} نجوم`);
    alert(`تم تقييم الفيلم بـ ${rating} نجوم`);
}

// Share functionality
function shareMovie(movieId, title) {
    if (navigator.share) {
        navigator.share({
            title: title,
            text: `شاهد ${title} على CinemaHub`,
            url: window.location.href
        });
    } else {
        // Fallback for browsers that don't support Web Share API
        const url = window.location.href;
        navigator.clipboard.writeText(url).then(() => {
            alert('تم نسخ الرابط');
        });
    }
}

// Initialize tooltips
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.textContent = this.getAttribute('data-tooltip');
            document.body.appendChild(tooltip);
            
            const rect = this.getBoundingClientRect();
            tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
            tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
        });
        
        element.addEventListener('mouseleave', function() {
            const tooltip = document.querySelector('.tooltip');
            if (tooltip) {
                tooltip.remove();
            }
        });
    });
}

// Call initialize tooltips
initializeTooltips();
