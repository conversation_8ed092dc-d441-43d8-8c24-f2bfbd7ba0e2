# دليل التطوير - CinemaHub Pro Desktop

دليل شامل للمطورين الذين يريدون المساهمة في تطوير البرنامج أو تخصيصه.

## 🏗️ هيكل المشروع

### الملفات الرئيسية
```
cinemahub-desktop-app/
├── main.js                 # العملية الرئيسية (Main Process)
├── package.json           # إعدادات المشروع والتبعيات
├── package-lock.json      # قفل إصدارات التبعيات
├── README.md              # دليل المستخدم
├── BUILD.md               # دليل البناء
├── DEVELOPMENT.md         # هذا الملف
├── .gitignore            # ملفات مستبعدة من Git
└── .eslintrc.js          # إعدادات ESLint
```

### مجلد المصدر (src/)
```
src/
├── index.html            # الواجهة الرئيسية
├── css/
│   └── style.css        # أنماط التصميم
└── js/
    ├── app.js           # منطق التطبيق الرئيسي
    ├── database.js      # إدارة قاعدة البيانات
    ├── sync.js          # إدارة المزامنة
    └── modals.js        # النوافذ المنبثقة
```

### مجلد الأصول (assets/)
```
assets/
├── icon.png             # أيقونة عامة
├── icon.ico             # أيقونة Windows
├── icon.icns            # أيقونة macOS
└── README.md            # دليل الأصول
```

## 🛠️ إعداد بيئة التطوير

### 1. المتطلبات الأساسية
```bash
# Node.js (الإصدار 16 أو أحدث)
node --version

# npm
npm --version

# Git
git --version
```

### 2. تحميل المشروع
```bash
# استنساخ المشروع
git clone <repository-url>
cd cinemahub-desktop-app

# تثبيت التبعيات
npm install
```

### 3. إعداد محرر النصوص

#### Visual Studio Code (مستحسن)
```json
// .vscode/settings.json
{
  "editor.tabSize": 4,
  "editor.insertSpaces": true,
  "editor.formatOnSave": true,
  "files.encoding": "utf8",
  "files.eol": "\n",
  "eslint.enable": true,
  "eslint.autoFixOnSave": true
}
```

#### الإضافات المستحسنة
- ESLint
- Prettier
- Auto Rename Tag
- Bracket Pair Colorizer
- GitLens
- Arabic Language Pack

### 4. إعداد Git Hooks
```bash
# تثبيت husky للـ git hooks
npm install --save-dev husky

# إعداد pre-commit hook
npx husky add .husky/pre-commit "npm run lint"
```

## 🧩 معمارية التطبيق

### العملية الرئيسية (Main Process)
```javascript
// main.js
const { app, BrowserWindow, Menu, ipcMain } = require('electron');

// إنشاء النافذة الرئيسية
function createMainWindow() {
    const mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false
        }
    });
    
    mainWindow.loadFile('src/index.html');
}
```

### عملية العرض (Renderer Process)
```javascript
// src/js/app.js
class CinemaHubApp {
    constructor() {
        this.init();
    }
    
    async init() {
        // تهيئة التطبيق
    }
}
```

### إدارة قاعدة البيانات
```javascript
// src/js/database.js
class DatabaseManager {
    constructor() {
        this.db = null;
    }
    
    async init() {
        // تهيئة قاعدة البيانات
    }
}
```

## 📝 معايير الكود

### JavaScript Style Guide
```javascript
// استخدم const للثوابت
const API_URL = 'https://api.example.com';

// استخدم let للمتغيرات
let currentUser = null;

// استخدم async/await بدلاً من callbacks
async function fetchData() {
    try {
        const response = await fetch(API_URL);
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error fetching data:', error);
        throw error;
    }
}

// استخدم arrow functions للدوال القصيرة
const users = data.map(item => item.user);

// استخدم template literals
const message = `Hello, ${user.name}!`;

// استخدم destructuring
const { name, email } = user;
```

### CSS Style Guide
```css
/* استخدم CSS Variables */
:root {
    --primary-color: #dc2626;
    --secondary-color: #1e293b;
}

/* استخدم BEM methodology */
.movie-card {
    /* Block */
}

.movie-card__title {
    /* Element */
}

.movie-card--featured {
    /* Modifier */
}

/* استخدم mobile-first approach */
.container {
    width: 100%;
}

@media (min-width: 768px) {
    .container {
        max-width: 1200px;
    }
}
```

### HTML Style Guide
```html
<!-- استخدم semantic HTML -->
<main class="main-content">
    <section class="movies-section">
        <header class="section-header">
            <h2>الأفلام الجديدة</h2>
        </header>
        <article class="movie-card">
            <!-- محتوى الفيلم -->
        </article>
    </section>
</main>

<!-- استخدم ARIA attributes للوصولية -->
<button aria-label="إغلاق النافذة" class="close-btn">
    <i class="fas fa-times" aria-hidden="true"></i>
</button>
```

## 🔧 أدوات التطوير

### ESLint Configuration
```javascript
// .eslintrc.js
module.exports = {
    env: {
        browser: true,
        es2021: true,
        node: true
    },
    extends: [
        'eslint:recommended'
    ],
    parserOptions: {
        ecmaVersion: 12,
        sourceType: 'module'
    },
    rules: {
        'indent': ['error', 4],
        'linebreak-style': ['error', 'unix'],
        'quotes': ['error', 'single'],
        'semi': ['error', 'always']
    }
};
```

### Prettier Configuration
```json
// .prettierrc
{
    "tabWidth": 4,
    "useTabs": false,
    "semi": true,
    "singleQuote": true,
    "quoteProps": "as-needed",
    "trailingComma": "es5",
    "bracketSpacing": true,
    "arrowParens": "avoid",
    "endOfLine": "lf"
}
```

### Package.json Scripts
```json
{
    "scripts": {
        "start": "electron .",
        "dev": "electron . --dev",
        "test": "jest",
        "lint": "eslint src/**/*.js",
        "lint:fix": "eslint src/**/*.js --fix",
        "format": "prettier --write src/**/*.{js,css,html}",
        "build": "electron-builder",
        "build-win": "electron-builder --win",
        "build-mac": "electron-builder --mac",
        "build-linux": "electron-builder --linux"
    }
}
```

## 🧪 الاختبار

### إعداد Jest
```javascript
// jest.config.js
module.exports = {
    testEnvironment: 'node',
    collectCoverage: true,
    coverageDirectory: 'coverage',
    coverageReporters: ['text', 'lcov', 'html'],
    testMatch: [
        '**/tests/**/*.test.js',
        '**/src/**/*.test.js'
    ]
};
```

### مثال على اختبار وحدة
```javascript
// tests/database.test.js
const DatabaseManager = require('../src/js/database');

describe('DatabaseManager', () => {
    let db;
    
    beforeEach(async () => {
        db = new DatabaseManager();
        await db.init();
    });
    
    afterEach(async () => {
        await db.close();
    });
    
    test('should create movies table', async () => {
        const tables = await db.all("SELECT name FROM sqlite_master WHERE type='table'");
        const movieTable = tables.find(table => table.name === 'movies');
        expect(movieTable).toBeDefined();
    });
    
    test('should add movie', async () => {
        const movieData = {
            title: 'Test Movie',
            year: 2024,
            rating: 8.5
        };
        
        const result = await db.addMovie(movieData);
        expect(result.id).toBeDefined();
        expect(result.changes).toBe(1);
    });
});
```

## 🐛 التصحيح (Debugging)

### Chrome DevTools
```javascript
// في main.js
if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
}
```

### Console Logging
```javascript
// استخدم مستويات مختلفة من الـ logging
console.log('Info message');
console.warn('Warning message');
console.error('Error message');
console.debug('Debug message');

// استخدم console.table للبيانات المنظمة
console.table(movies);

// استخدم console.time لقياس الأداء
console.time('Database Query');
await db.getMovies();
console.timeEnd('Database Query');
```

### Electron DevTools Extensions
```javascript
// في main.js
const { session } = require('electron');

app.whenReady().then(() => {
    // تثبيت React DevTools (إذا كنت تستخدم React)
    session.defaultSession.loadExtension(
        path.join(__dirname, 'extensions/react-devtools')
    );
});
```

## 📚 إضافة ميزات جديدة

### 1. إضافة صفحة جديدة
```html
<!-- في index.html -->
<div class="page-content" id="new-page">
    <div class="page-header">
        <h2>الصفحة الجديدة</h2>
    </div>
    <div class="content-card">
        <!-- محتوى الصفحة -->
    </div>
</div>
```

```javascript
// في app.js
async loadNewPageData() {
    // تحميل بيانات الصفحة الجديدة
}
```

### 2. إضافة API جديد
```javascript
// في database.js
async getNewData(filters = {}) {
    let sql = 'SELECT * FROM new_table';
    const params = [];
    
    // إضافة فلاتر
    if (filters.search) {
        sql += ' WHERE title LIKE ?';
        params.push(`%${filters.search}%`);
    }
    
    return await this.all(sql, params);
}
```

### 3. إضافة نافذة منبثقة جديدة
```javascript
// في modals.js
showNewModal() {
    const modalId = 'newModal';
    const modalHTML = `
        <div class="modal fade" id="${modalId}">
            <!-- محتوى النافذة -->
        </div>
    `;
    
    this.showModal(modalId, modalHTML);
}
```

## 🔄 سير العمل (Workflow)

### Git Workflow
```bash
# إنشاء فرع جديد للميزة
git checkout -b feature/new-feature

# إجراء التغييرات
git add .
git commit -m "Add new feature"

# دفع الفرع
git push origin feature/new-feature

# إنشاء Pull Request
# مراجعة الكود
# دمج الفرع
```

### Commit Messages
```bash
# استخدم conventional commits
git commit -m "feat: add movie search functionality"
git commit -m "fix: resolve database connection issue"
git commit -m "docs: update README with new features"
git commit -m "style: improve button hover effects"
git commit -m "refactor: optimize database queries"
git commit -m "test: add unit tests for sync manager"
```

## 📊 مراقبة الأداء

### Memory Usage
```javascript
// مراقبة استخدام الذاكرة
setInterval(() => {
    const memUsage = process.memoryUsage();
    console.log('Memory Usage:', {
        rss: Math.round(memUsage.rss / 1024 / 1024) + ' MB',
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + ' MB',
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + ' MB'
    });
}, 30000);
```

### Performance Monitoring
```javascript
// قياس أداء العمليات
async function performanceWrapper(fn, name) {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();
    console.log(`${name} took ${end - start} milliseconds`);
    return result;
}

// الاستخدام
const movies = await performanceWrapper(
    () => db.getMovies(),
    'Get Movies'
);
```

## 🚀 النشر والتوزيع

### Continuous Integration
```yaml
# .github/workflows/build.yml
name: Build and Test

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '16'
      - run: npm install
      - run: npm test
      - run: npm run lint

  build:
    needs: test
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '16'
      - run: npm install
      - run: npm run build
```

### Release Process
```bash
# 1. تحديث رقم الإصدار
npm version patch  # أو minor أو major

# 2. إنشاء changelog
git log --oneline --since="last-release" > CHANGELOG.md

# 3. بناء للإنتاج
npm run build

# 4. إنشاء release
git tag v1.0.1
git push origin v1.0.1

# 5. نشر على GitHub Releases
gh release create v1.0.1 dist/* --title "Release v1.0.1"
```

## 📖 الموارد المفيدة

### الوثائق
- [Electron Documentation](https://www.electronjs.org/docs)
- [Node.js Documentation](https://nodejs.org/docs)
- [SQLite Documentation](https://sqlite.org/docs.html)

### الأدوات
- [Electron Forge](https://www.electronforge.io/)
- [Electron Builder](https://www.electron.build/)
- [Spectron](https://github.com/electron-userland/spectron) (للاختبار)

### المجتمع
- [Electron Discord](https://discord.gg/electron)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/electron)
- [GitHub Discussions](https://github.com/electron/electron/discussions)

---

**نصائح للمطورين الجدد:**
1. ابدأ بفهم معمارية Electron
2. تعلم الفرق بين Main Process و Renderer Process
3. استخدم أدوات التصحيح بفعالية
4. اكتب اختبارات للكود الجديد
5. اتبع معايير الكود المحددة
6. وثق التغييرات التي تقوم بها
