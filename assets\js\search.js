// ===== Search Page JavaScript =====

class SearchManager {
    constructor() {
        this.currentPage = 1;
        this.totalPages = 1;
        this.currentQuery = '';
        this.currentFilters = {};
        this.searchTimeout = null;
        this.resultsPerPage = 20;
        
        this.initializeSearch();
    }

    initializeSearch() {
        this.setupEventListeners();
        this.populateFilters();
        this.handleURLParams();
        
        // Show advanced search by default
        document.getElementById('advancedSearch').style.display = 'block';
    }

    setupEventListeners() {
        // Search form submission
        document.getElementById('advancedSearchForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.performSearch();
        });

        // Header search form
        document.getElementById('headerSearchForm').addEventListener('submit', (e) => {
            e.preventDefault();
            const query = document.getElementById('headerSearchInput').value.trim();
            if (query) {
                document.getElementById('searchQuery').value = query;
                this.performSearch();
            }
        });

        // Clear filters button
        document.getElementById('clearFilters').addEventListener('click', () => {
            this.clearFilters();
        });

        // Toggle advanced search
        document.getElementById('toggleAdvanced').addEventListener('click', () => {
            this.toggleAdvancedSearch();
        });

        // Real-time search on input
        document.getElementById('searchQuery').addEventListener('input', (e) => {
            clearTimeout(this.searchTimeout);
            const query = e.target.value.trim();
            
            if (query.length > 2) {
                this.searchTimeout = setTimeout(() => {
                    this.performSearch();
                }, 500);
            } else if (query.length === 0) {
                this.clearResults();
            }
        });

        // Filter change events
        ['contentType', 'releaseYear', 'genreFilter', 'ratingFilter', 'sortBy', 'languageFilter'].forEach(id => {
            document.getElementById(id).addEventListener('change', () => {
                if (this.currentQuery) {
                    this.performSearch();
                }
            });
        });
    }

    async populateFilters() {
        try {
            // Populate years
            this.populateYears();
            
            // Populate genres
            await this.populateGenres();
            
        } catch (error) {
            console.error('Error populating filters:', error);
        }
    }

    populateYears() {
        const yearSelect = document.getElementById('releaseYear');
        const currentYear = new Date().getFullYear();
        
        for (let year = currentYear; year >= 1950; year--) {
            const option = document.createElement('option');
            option.value = year;
            option.textContent = year;
            yearSelect.appendChild(option);
        }
    }

    async populateGenres() {
        try {
            const [movieGenres, tvGenres] = await Promise.all([
                contentManager.getGenres('movie'),
                contentManager.getGenres('tv')
            ]);

            const genreSelect = document.getElementById('genreFilter');
            const allGenres = new Map();

            // Combine movie and TV genres
            [...movieGenres.genres, ...tvGenres.genres].forEach(genre => {
                allGenres.set(genre.id, genre.name);
            });

            // Sort genres alphabetically
            const sortedGenres = Array.from(allGenres.entries()).sort((a, b) => a[1].localeCompare(b[1]));

            sortedGenres.forEach(([id, name]) => {
                const option = document.createElement('option');
                option.value = id;
                option.textContent = name;
                genreSelect.appendChild(option);
            });

        } catch (error) {
            console.error('Error loading genres:', error);
        }
    }

    handleURLParams() {
        const urlParams = new URLSearchParams(window.location.search);
        const query = urlParams.get('q');
        
        if (query) {
            document.getElementById('searchQuery').value = query;
            document.getElementById('headerSearchInput').value = query;
            this.performSearch();
        }
    }

    async performSearch(page = 1) {
        const query = document.getElementById('searchQuery').value.trim();
        const contentType = document.getElementById('contentType').value;
        const releaseYear = document.getElementById('releaseYear').value;
        const genre = document.getElementById('genreFilter').value;
        const rating = document.getElementById('ratingFilter').value;
        const sortBy = document.getElementById('sortBy').value;
        const language = document.getElementById('languageFilter').value;

        if (!query && !genre && !releaseYear && !rating) {
            this.showNoResults('يرجى إدخال كلمة بحث أو اختيار فلتر واحد على الأقل');
            return;
        }

        this.currentQuery = query;
        this.currentPage = page;
        this.currentFilters = {
            contentType,
            releaseYear,
            genre,
            rating,
            sortBy,
            language
        };

        this.showLoading();
        this.hideNoResults();

        try {
            let results;

            if (query) {
                // Text search
                if (contentType === 'movie') {
                    results = await contentManager.searchMovies(query, page);
                } else if (contentType === 'tv') {
                    results = await contentManager.searchTVShows(query, page);
                } else {
                    results = await contentManager.searchMulti(query, page);
                }
            } else {
                // Filter-based discovery
                const discoverParams = {
                    page,
                    sort_by: sortBy
                };

                if (releaseYear) {
                    if (contentType === 'tv') {
                        discoverParams.first_air_date_year = releaseYear;
                    } else {
                        discoverParams.year = releaseYear;
                    }
                }

                if (genre) {
                    discoverParams.with_genres = genre;
                }

                if (rating) {
                    discoverParams['vote_average.gte'] = rating;
                }

                if (language) {
                    discoverParams.with_original_language = language;
                }

                if (contentType === 'tv') {
                    results = await contentManager.discoverTVShows(discoverParams);
                } else {
                    results = await contentManager.discoverMovies(discoverParams);
                }
            }

            this.hideLoading();
            this.displayResults(results);
            this.updatePagination(results.page, results.total_pages);
            this.updateURL();

        } catch (error) {
            console.error('Search error:', error);
            this.hideLoading();
            this.showNoResults('حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.');
        }
    }

    displayResults(results) {
        const resultsContainer = document.getElementById('searchResults');
        
        if (!results.results || results.results.length === 0) {
            this.showNoResults();
            return;
        }

        resultsContainer.innerHTML = `
            <div class="search-results-header mb-4" data-aos="fade-up">
                <h3 class="text-light">
                    <i class="fas fa-search"></i>
                    نتائج البحث (${results.total_results.toLocaleString()} نتيجة)
                </h3>
                <p class="text-muted">الصفحة ${results.page} من ${results.total_pages}</p>
            </div>
            <div class="movies-grid" id="searchResultsGrid">
                <!-- Results will be populated here -->
            </div>
        `;

        const grid = document.getElementById('searchResultsGrid');
        
        results.results.forEach((item, index) => {
            const type = item.media_type || (item.title ? 'movie' : 'tv');
            const card = this.createResultCard(item, type);
            card.setAttribute('data-aos', 'fade-up');
            card.setAttribute('data-aos-delay', (index % 8) * 50);
            grid.appendChild(card);
        });

        // Initialize AOS for new elements
        AOS.refresh();
    }

    createResultCard(item, type) {
        const card = document.createElement('div');
        card.className = 'movie-card';

        const title = item.title || item.name;
        const releaseDate = item.release_date || item.first_air_date;
        const year = releaseDate ? releaseDate.split('-')[0] : 'غير محدد';
        const rating = item.vote_average ? item.vote_average.toFixed(1) : 'N/A';
        const posterPath = item.poster_path ? IMAGE_BASE_URL + item.poster_path : 'assets/images/no-poster.jpg';
        const overview = item.overview ? (item.overview.length > 100 ? item.overview.substring(0, 100) + '...' : item.overview) : 'لا يوجد وصف متاح';

        card.innerHTML = `
            <div class="movie-poster">
                <img src="${posterPath}" alt="${title}" loading="lazy">
                <div class="quality-badge">${type === 'movie' ? 'فيلم' : 'مسلسل'}</div>
                <div class="movie-overlay">
                    <button class="play-btn" onclick="openMovieModal(${item.id}, '${type}')">
                        <i class="fas fa-play"></i>
                    </button>
                </div>
            </div>
            <div class="movie-info">
                <h3 class="movie-title" title="${title}">${title}</h3>
                <p class="movie-overview text-muted small">${overview}</p>
                <div class="movie-meta">
                    <span class="movie-year">${year}</span>
                    <div class="movie-rating">
                        <i class="fas fa-star"></i>
                        <span>${rating}</span>
                    </div>
                </div>
            </div>
        `;

        return card;
    }

    updatePagination(currentPage, totalPages) {
        this.currentPage = currentPage;
        this.totalPages = totalPages;

        const paginationContainer = document.getElementById('searchPagination');
        
        if (totalPages <= 1) {
            paginationContainer.style.display = 'none';
            return;
        }

        paginationContainer.style.display = 'block';
        const pagination = paginationContainer.querySelector('.pagination');
        pagination.innerHTML = '';

        // Previous button
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
        prevLi.innerHTML = `
            <a class="page-link" href="#" onclick="searchManager.goToPage(${currentPage - 1})">
                <i class="fas fa-chevron-right"></i>
            </a>
        `;
        pagination.appendChild(prevLi);

        // Page numbers
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            const firstLi = document.createElement('li');
            firstLi.className = 'page-item';
            firstLi.innerHTML = `<a class="page-link" href="#" onclick="searchManager.goToPage(1)">1</a>`;
            pagination.appendChild(firstLi);

            if (startPage > 2) {
                const dotsLi = document.createElement('li');
                dotsLi.className = 'page-item disabled';
                dotsLi.innerHTML = '<span class="page-link">...</span>';
                pagination.appendChild(dotsLi);
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const li = document.createElement('li');
            li.className = `page-item ${i === currentPage ? 'active' : ''}`;
            li.innerHTML = `<a class="page-link" href="#" onclick="searchManager.goToPage(${i})">${i}</a>`;
            pagination.appendChild(li);
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                const dotsLi = document.createElement('li');
                dotsLi.className = 'page-item disabled';
                dotsLi.innerHTML = '<span class="page-link">...</span>';
                pagination.appendChild(dotsLi);
            }

            const lastLi = document.createElement('li');
            lastLi.className = 'page-item';
            lastLi.innerHTML = `<a class="page-link" href="#" onclick="searchManager.goToPage(${totalPages})">${totalPages}</a>`;
            pagination.appendChild(lastLi);
        }

        // Next button
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
        nextLi.innerHTML = `
            <a class="page-link" href="#" onclick="searchManager.goToPage(${currentPage + 1})">
                <i class="fas fa-chevron-left"></i>
            </a>
        `;
        pagination.appendChild(nextLi);
    }

    goToPage(page) {
        if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
            this.performSearch(page);
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    }

    showLoading() {
        document.getElementById('searchLoading').style.display = 'flex';
        document.getElementById('searchResults').innerHTML = '';
        document.getElementById('searchPagination').style.display = 'none';
    }

    hideLoading() {
        document.getElementById('searchLoading').style.display = 'none';
    }

    showNoResults(message = 'لم نتمكن من العثور على أي نتائج تطابق بحثك') {
        document.getElementById('noResults').style.display = 'block';
        document.getElementById('noResults').querySelector('p').textContent = message;
        document.getElementById('searchResults').innerHTML = '';
        document.getElementById('searchPagination').style.display = 'none';
    }

    hideNoResults() {
        document.getElementById('noResults').style.display = 'none';
    }

    clearResults() {
        document.getElementById('searchResults').innerHTML = '';
        document.getElementById('searchPagination').style.display = 'none';
        this.hideNoResults();
    }

    clearFilters() {
        document.getElementById('advancedSearchForm').reset();
        document.getElementById('headerSearchInput').value = '';
        this.clearResults();
        this.currentQuery = '';
        this.currentFilters = {};
        this.updateURL();
    }

    toggleAdvancedSearch() {
        const advancedSearch = document.getElementById('advancedSearch');
        const isVisible = advancedSearch.style.display !== 'none';
        advancedSearch.style.display = isVisible ? 'none' : 'block';
        
        const toggleBtn = document.getElementById('toggleAdvanced');
        toggleBtn.innerHTML = isVisible ? 
            '<i class="fas fa-cog"></i> إظهار البحث المتقدم' : 
            '<i class="fas fa-times"></i> إخفاء البحث المتقدم';
    }

    updateURL() {
        const params = new URLSearchParams();
        
        if (this.currentQuery) {
            params.set('q', this.currentQuery);
        }
        
        if (this.currentPage > 1) {
            params.set('page', this.currentPage);
        }

        Object.keys(this.currentFilters).forEach(key => {
            if (this.currentFilters[key]) {
                params.set(key, this.currentFilters[key]);
            }
        });

        const newURL = `${window.location.pathname}${params.toString() ? '?' + params.toString() : ''}`;
        window.history.replaceState({}, '', newURL);
    }
}

// Global functions
function clearSearch() {
    searchManager.clearFilters();
}

// Initialize search manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.searchManager = new SearchManager();
});
