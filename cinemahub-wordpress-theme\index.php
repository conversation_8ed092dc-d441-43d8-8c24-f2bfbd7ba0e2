<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php wp_title('|', true, 'right'); ?><?php bloginfo('name'); ?></title>
    <meta name="description" content="<?php bloginfo('description'); ?>">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Theme Styles -->
    <link rel="stylesheet" href="<?php echo get_stylesheet_uri(); ?>">
    
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<!-- Header -->
<header class="site-header">
    <div class="header-container">
        <a href="<?php echo home_url(); ?>" class="site-logo">
            <div class="logo-icon">
                <i class="fas fa-film"></i>
            </div>
            <div class="logo-text">
                <h1><?php bloginfo('name'); ?></h1>
                <p><?php bloginfo('description'); ?></p>
            </div>
        </a>
        
        <nav class="main-nav">
            <ul>
                <li><a href="<?php echo home_url(); ?>"><i class="fas fa-home"></i> الرئيسية</a></li>
                <li><a href="<?php echo get_post_type_archive_link('movie'); ?>"><i class="fas fa-film"></i> الأفلام</a></li>
                <li><a href="<?php echo get_post_type_archive_link('series'); ?>"><i class="fas fa-tv"></i> المسلسلات</a></li>
                <li><a href="<?php echo admin_url(); ?>"><i class="fas fa-cog"></i> لوحة التحكم</a></li>
            </ul>
        </nav>
    </div>
</header>

<!-- Main Content -->
<main class="main-content">
    <div class="container">
        
        <!-- Welcome Section -->
        <section class="welcome-section">
            <h1 class="main-title">مرحباً بك في CinemaHub Pro</h1>
            
            <p class="subtitle">
                منصة احترافية لإدارة وعرض الأفلام والمسلسلات العربية والعالمية<br>
                بتصميم عصري ومتجاوب مع جميع الأجهزة وخاصية استعادة المظهر الأصلي
            </p>
            
            <div class="action-buttons">
                <a href="<?php echo admin_url('post-new.php?post_type=movie'); ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    إضافة فيلم جديد
                </a>
                
                <a href="<?php echo admin_url('post-new.php?post_type=series'); ?>" class="btn btn-secondary">
                    <i class="fas fa-plus"></i>
                    إضافة مسلسل جديد
                </a>
                
                <a href="<?php echo get_template_directory_uri(); ?>/restore-original.php" class="btn restore-btn">
                    <i class="fas fa-undo"></i>
                    استعادة المظهر الأصلي
                </a>
            </div>
        </section>
        
        <!-- Features Grid -->
        <div class="features-grid">
            <div class="feature-card">
                <i class="fas fa-film feature-icon"></i>
                <h3 class="feature-title">إدارة الأفلام</h3>
                <p class="feature-desc">
                    أضف وأدر مجموعة أفلامك بسهولة مع تفاصيل شاملة وصور عالية الجودة، 
                    تقييمات، سنوات الإنتاج، والمزيد من المعلومات المفيدة
                </p>
            </div>
            
            <div class="feature-card">
                <i class="fas fa-tv feature-icon"></i>
                <h3 class="feature-title">إدارة المسلسلات</h3>
                <p class="feature-desc">
                    نظم مسلسلاتك مع تفاصيل المواسم والحلقات ومتابعة حالة العرض، 
                    مع إمكانية إضافة روابط المشاهدة والتحميل
                </p>
            </div>
            
            <div class="feature-card">
                <i class="fas fa-search feature-icon"></i>
                <h3 class="feature-title">بحث متقدم</h3>
                <p class="feature-desc">
                    ابحث في المحتوى بسهولة وسرعة مع فلاتر متقدمة وتصنيفات ذكية، 
                    بحث بالاسم، النوع، السنة، والتقييم
                </p>
            </div>
            
            <div class="feature-card">
                <i class="fas fa-mobile-alt feature-icon"></i>
                <h3 class="feature-title">تصميم متجاوب</h3>
                <p class="feature-desc">
                    تصميم يتكيف مع جميع الأجهزة والشاشات، من الهواتف المحمولة 
                    إلى أجهزة الكمبيوتر المكتبية بأفضل جودة عرض
                </p>
            </div>
            
            <div class="feature-card">
                <i class="fas fa-palette feature-icon"></i>
                <h3 class="feature-title">تخصيص المظهر</h3>
                <p class="feature-desc">
                    خصص ألوان وتصميم موقعك بسهولة مع إمكانية استعادة المظهر الأصلي 
                    في أي وقت بضغطة زر واحدة
                </p>
            </div>
            
            <div class="feature-card">
                <i class="fas fa-rocket feature-icon"></i>
                <h3 class="feature-title">أداء عالي</h3>
                <p class="feature-desc">
                    كود محسن وسريع التحميل مع تقنيات حديثة لضمان أفضل تجربة 
                    للمستخدمين وتحسين ترتيب الموقع في محركات البحث
                </p>
            </div>
        </div>
        
        <!-- Latest Content -->
        <section style="margin-top: 4rem;">
            <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 3rem; color: #dc2626;">
                <i class="fas fa-star"></i>
                أحدث المحتوى
            </h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                <?php
                // Get latest movies
                $movies = new WP_Query(array(
                    'post_type' => 'movie',
                    'posts_per_page' => 3,
                    'orderby' => 'date',
                    'order' => 'DESC'
                ));
                
                if ($movies->have_posts()) :
                    while ($movies->have_posts()) : $movies->the_post();
                        ?>
                        <div style="background: rgba(30, 41, 59, 0.6); border-radius: 20px; overflow: hidden; transition: all 0.3s ease; border: 2px solid transparent;">
                            <div style="position: relative;">
                                <?php if (has_post_thumbnail()) : ?>
                                    <a href="<?php the_permalink(); ?>">
                                        <?php the_post_thumbnail('medium', array('style' => 'width: 100%; height: 250px; object-fit: cover;')); ?>
                                    </a>
                                <?php else : ?>
                                    <div style="width: 100%; height: 250px; background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 3rem;">
                                        <i class="fas fa-film"></i>
                                    </div>
                                <?php endif; ?>
                                
                                <div style="position: absolute; top: 15px; right: 15px; background: rgba(220, 38, 38, 0.9); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem; font-weight: 700;">
                                    فيلم جديد
                                </div>
                            </div>
                            
                            <div style="padding: 2rem;">
                                <h3 style="margin: 0 0 1rem 0; font-size: 1.3rem; font-weight: 700;">
                                    <a href="<?php the_permalink(); ?>" style="color: white; text-decoration: none;">
                                        <?php the_title(); ?>
                                    </a>
                                </h3>
                                
                                <p style="color: #cbd5e1; line-height: 1.6; margin-bottom: 1rem;">
                                    <?php echo wp_trim_words(get_the_excerpt(), 15); ?>
                                </p>
                                
                                <a href="<?php the_permalink(); ?>" style="color: #dc2626; text-decoration: none; font-weight: 600;">
                                    مشاهدة التفاصيل <i class="fas fa-arrow-left"></i>
                                </a>
                            </div>
                        </div>
                        <?php
                    endwhile;
                    wp_reset_postdata();
                else :
                    ?>
                    <div style="grid-column: 1 / -1; text-align: center; padding: 4rem; color: #94a3b8; background: rgba(30, 41, 59, 0.3); border-radius: 20px;">
                        <i class="fas fa-film" style="font-size: 4rem; margin-bottom: 2rem; color: #dc2626;"></i>
                        <h3 style="margin-bottom: 1rem; font-size: 1.5rem;">لا توجد أفلام حالياً</h3>
                        <p style="margin-bottom: 2rem;">ابدأ بإضافة أول فيلم لموقعك</p>
                        <a href="<?php echo admin_url('post-new.php?post_type=movie'); ?>" style="background: #dc2626; color: white; padding: 1rem 2rem; border-radius: 25px; text-decoration: none; font-weight: 600; display: inline-flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-plus"></i>
                            إضافة فيلم الآن
                        </a>
                    </div>
                    <?php
                endif;
                ?>
            </div>
        </section>
    </div>
</main>

<!-- Footer -->
<footer class="site-footer">
    <div class="footer-content">
        <p class="footer-text">
            © 2024 CinemaHub Pro - قالب احترافي لمواقع الأفلام والمسلسلات مع خاصية استعادة المظهر الأصلي
        </p>
        
        <div class="footer-links">
            <a href="<?php echo home_url(); ?>">الرئيسية</a>
            <a href="<?php echo get_post_type_archive_link('movie'); ?>">الأفلام</a>
            <a href="<?php echo get_post_type_archive_link('series'); ?>">المسلسلات</a>
            <a href="<?php echo admin_url(); ?>">لوحة التحكم</a>
            <a href="<?php echo get_template_directory_uri(); ?>/restore-original.php">استعادة المظهر الأصلي</a>
        </div>
        
        <div style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid rgba(148, 163, 184, 0.2);">
            <p style="color: #94a3b8; font-size: 0.9rem;">
                <i class="fas fa-code"></i>
                تم التطوير بواسطة فريق CinemaHub Pro
                <span style="margin: 0 1rem;">•</span>
                <i class="fas fa-heart" style="color: #dc2626;"></i>
                صُنع بحب للمحتوى العربي
            </p>
        </div>
    </div>
</footer>

<!-- Scripts -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects to cards
    const cards = document.querySelectorAll('.feature-card, [style*="background: rgba(30, 41, 59, 0.6)"]');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.borderColor = '#dc2626';
            this.style.transform = 'translateY(-10px)';
            this.style.boxShadow = '0 20px 40px rgba(0,0,0,0.4)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.borderColor = 'transparent';
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
    
    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});
</script>

<?php wp_footer(); ?>
</body>
</html>
