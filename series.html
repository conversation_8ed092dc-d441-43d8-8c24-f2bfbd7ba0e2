<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المسلسلات - CinemaHub</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .series-page {
            padding: 6rem 0 4rem;
            min-height: 100vh;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 4rem;
        }
        
        .page-title {
            font-size: 3rem;
            color: #dc2626;
            margin-bottom: 1rem;
        }
        
        .page-subtitle {
            font-size: 1.2rem;
            color: #cccccc;
        }
        
        .filters {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 3rem;
            flex-wrap: wrap;
        }
        
        .filter-btn {
            padding: 0.75rem 1.5rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(220, 38, 38, 0.3);
            color: #ffffff;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
        }
        
        .filter-btn:hover,
        .filter-btn.active {
            background: #dc2626;
            border-color: #dc2626;
        }
        
        .series-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 4rem;
        }
        
        .series-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid rgba(220, 38, 38, 0.3);
            position: relative;
        }
        
        .series-card:hover {
            transform: translateY(-10px);
            border-color: #dc2626;
            box-shadow: 0 20px 40px rgba(220, 38, 38, 0.2);
        }
        
        .series-poster {
            position: relative;
            overflow: hidden;
        }
        
        .series-poster img {
            width: 100%;
            height: 400px;
            object-fit: cover;
            transition: all 0.3s ease;
        }
        
        .series-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to top, rgba(0,0,0,0.9) 0%, transparent 50%);
            display: flex;
            align-items: flex-end;
            padding: 1.5rem;
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .series-card:hover .series-overlay {
            opacity: 1;
        }
        
        .overlay-content {
            width: 100%;
        }
        
        .play-btn-large {
            background: #dc2626;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            color: #ffffff;
            font-size: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }
        
        .play-btn-large:hover {
            background: #b91c1c;
            transform: scale(1.1);
        }
        
        .series-info {
            padding: 1.5rem;
        }
        
        .series-title {
            font-size: 1.3rem;
            margin-bottom: 0.5rem;
            color: #ffffff;
        }
        
        .series-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .series-year {
            color: #cccccc;
            font-size: 0.9rem;
        }
        
        .series-rating {
            color: #fbbf24;
            font-size: 0.9rem;
        }
        
        .series-description {
            color: #cccccc;
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 1rem;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .series-stats {
            display: flex;
            gap: 1rem;
            font-size: 0.8rem;
            color: #cccccc;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .stat-item i {
            color: #dc2626;
        }
        
        .series-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: #dc2626;
            color: #ffffff;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 3rem;
        }
        
        .page-btn {
            padding: 0.75rem 1rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(220, 38, 38, 0.3);
            color: #ffffff;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .page-btn:hover,
        .page-btn.active {
            background: #dc2626;
            border-color: #dc2626;
        }
        
        @media (max-width: 768px) {
            .page-title {
                font-size: 2rem;
            }
            
            .series-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }
            
            .filters {
                justify-content: flex-start;
                overflow-x: auto;
                padding-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1><i class="fas fa-film"></i> CinemaHub</h1>
                </div>
                <nav class="nav">
                    <a href="index.html" class="nav-link">الرئيسية</a>
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link">الأفلام <i class="fas fa-chevron-down"></i></a>
                        <div class="dropdown-menu">
                            <a href="#">أفلام عربية</a>
                            <a href="#">أفلام أجنبية</a>
                            <a href="#">أفلام هندية</a>
                            <a href="#">أفلام تركية</a>
                        </div>
                    </div>
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link">المسلسلات <i class="fas fa-chevron-down"></i></a>
                        <div class="dropdown-menu">
                            <a href="#">مسلسلات عربية</a>
                            <a href="#">مسلسلات أجنبية</a>
                            <a href="#">مسلسلات تركية</a>
                            <a href="#">مسلسلات هندية</a>
                        </div>
                    </div>
                    <a href="#" class="nav-link">الأحدث</a>
                </nav>
                <div class="auth-buttons">
                    <button class="btn btn-outline">تسجيل دخول</button>
                    <button class="btn btn-primary">إنشاء حساب</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Series Page -->
    <section class="series-page">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">المسلسلات</h1>
                <p class="page-subtitle">اكتشف أحدث المسلسلات العربية والأجنبية</p>
            </div>

            <!-- Filters -->
            <div class="filters">
                <button class="filter-btn active">الكل</button>
                <button class="filter-btn">مسلسلات عربية</button>
                <button class="filter-btn">مسلسلات أجنبية</button>
                <button class="filter-btn">مسلسلات تركية</button>
                <button class="filter-btn">مسلسلات هندية</button>
                <button class="filter-btn">الأحدث</button>
                <button class="filter-btn">الأعلى تقييماً</button>
            </div>

            <!-- Series Grid -->
            <div class="series-grid">
                <!-- Series Card 1 -->
                <div class="series-card">
                    <div class="series-badge">جديد</div>
                    <div class="series-poster">
                        <img src="https://via.placeholder.com/280x400/dc2626/ffffff?text=مسلسل+1" alt="مسلسل 1">
                        <div class="series-overlay">
                            <div class="overlay-content">
                                <button class="play-btn-large"><i class="fas fa-play"></i></button>
                                <div style="color: white; font-weight: bold;">مشاهدة الآن</div>
                            </div>
                        </div>
                    </div>
                    <div class="series-info">
                        <h3 class="series-title">عنوان المسلسل الأول</h3>
                        <div class="series-meta">
                            <span class="series-year">2024</span>
                            <span class="series-rating"><i class="fas fa-star"></i> 9.2</span>
                        </div>
                        <p class="series-description">
                            وصف مختصر للمسلسل يوضح القصة والأحداث الرئيسية بطريقة مشوقة تجذب المشاهد لمتابعة العمل.
                        </p>
                        <div class="series-stats">
                            <div class="stat-item">
                                <i class="fas fa-list"></i>
                                <span>30 حلقة</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-clock"></i>
                                <span>45 دقيقة</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-eye"></i>
                                <span>1.2M مشاهدة</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Series Card 2 -->
                <div class="series-card">
                    <div class="series-poster">
                        <img src="https://via.placeholder.com/280x400/dc2626/ffffff?text=مسلسل+2" alt="مسلسل 2">
                        <div class="series-overlay">
                            <div class="overlay-content">
                                <button class="play-btn-large"><i class="fas fa-play"></i></button>
                                <div style="color: white; font-weight: bold;">مشاهدة الآن</div>
                            </div>
                        </div>
                    </div>
                    <div class="series-info">
                        <h3 class="series-title">عنوان المسلسل الثاني</h3>
                        <div class="series-meta">
                            <span class="series-year">2024</span>
                            <span class="series-rating"><i class="fas fa-star"></i> 8.7</span>
                        </div>
                        <p class="series-description">
                            وصف مختصر للمسلسل يوضح القصة والأحداث الرئيسية بطريقة مشوقة تجذب المشاهد لمتابعة العمل.
                        </p>
                        <div class="series-stats">
                            <div class="stat-item">
                                <i class="fas fa-list"></i>
                                <span>25 حلقة</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-clock"></i>
                                <span>50 دقيقة</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-eye"></i>
                                <span>890K مشاهدة</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Series Card 3 -->
                <div class="series-card">
                    <div class="series-badge">مكتمل</div>
                    <div class="series-poster">
                        <img src="https://via.placeholder.com/280x400/dc2626/ffffff?text=مسلسل+3" alt="مسلسل 3">
                        <div class="series-overlay">
                            <div class="overlay-content">
                                <button class="play-btn-large"><i class="fas fa-play"></i></button>
                                <div style="color: white; font-weight: bold;">مشاهدة الآن</div>
                            </div>
                        </div>
                    </div>
                    <div class="series-info">
                        <h3 class="series-title">عنوان المسلسل الثالث</h3>
                        <div class="series-meta">
                            <span class="series-year">2023</span>
                            <span class="series-rating"><i class="fas fa-star"></i> 8.9</span>
                        </div>
                        <p class="series-description">
                            وصف مختصر للمسلسل يوضح القصة والأحداث الرئيسية بطريقة مشوقة تجذب المشاهد لمتابعة العمل.
                        </p>
                        <div class="series-stats">
                            <div class="stat-item">
                                <i class="fas fa-list"></i>
                                <span>20 حلقة</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-clock"></i>
                                <span>40 دقيقة</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-eye"></i>
                                <span>2.1M مشاهدة</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Series Card 4 -->
                <div class="series-card">
                    <div class="series-poster">
                        <img src="https://via.placeholder.com/280x400/dc2626/ffffff?text=مسلسل+4" alt="مسلسل 4">
                        <div class="series-overlay">
                            <div class="overlay-content">
                                <button class="play-btn-large"><i class="fas fa-play"></i></button>
                                <div style="color: white; font-weight: bold;">مشاهدة الآن</div>
                            </div>
                        </div>
                    </div>
                    <div class="series-info">
                        <h3 class="series-title">عنوان المسلسل الرابع</h3>
                        <div class="series-meta">
                            <span class="series-year">2024</span>
                            <span class="series-rating"><i class="fas fa-star"></i> 8.4</span>
                        </div>
                        <p class="series-description">
                            وصف مختصر للمسلسل يوضح القصة والأحداث الرئيسية بطريقة مشوقة تجذب المشاهد لمتابعة العمل.
                        </p>
                        <div class="series-stats">
                            <div class="stat-item">
                                <i class="fas fa-list"></i>
                                <span>35 حلقة</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-clock"></i>
                                <span>55 دقيقة</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-eye"></i>
                                <span>750K مشاهدة</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Series Card 5 -->
                <div class="series-card">
                    <div class="series-badge">ترندينغ</div>
                    <div class="series-poster">
                        <img src="https://via.placeholder.com/280x400/dc2626/ffffff?text=مسلسل+5" alt="مسلسل 5">
                        <div class="series-overlay">
                            <div class="overlay-content">
                                <button class="play-btn-large"><i class="fas fa-play"></i></button>
                                <div style="color: white; font-weight: bold;">مشاهدة الآن</div>
                            </div>
                        </div>
                    </div>
                    <div class="series-info">
                        <h3 class="series-title">عنوان المسلسل الخامس</h3>
                        <div class="series-meta">
                            <span class="series-year">2024</span>
                            <span class="series-rating"><i class="fas fa-star"></i> 9.0</span>
                        </div>
                        <p class="series-description">
                            وصف مختصر للمسلسل يوضح القصة والأحداث الرئيسية بطريقة مشوقة تجذب المشاهد لمتابعة العمل.
                        </p>
                        <div class="series-stats">
                            <div class="stat-item">
                                <i class="fas fa-list"></i>
                                <span>28 حلقة</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-clock"></i>
                                <span>42 دقيقة</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-eye"></i>
                                <span>1.8M مشاهدة</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Series Card 6 -->
                <div class="series-card">
                    <div class="series-poster">
                        <img src="https://via.placeholder.com/280x400/dc2626/ffffff?text=مسلسل+6" alt="مسلسل 6">
                        <div class="series-overlay">
                            <div class="overlay-content">
                                <button class="play-btn-large"><i class="fas fa-play"></i></button>
                                <div style="color: white; font-weight: bold;">مشاهدة الآن</div>
                            </div>
                        </div>
                    </div>
                    <div class="series-info">
                        <h3 class="series-title">عنوان المسلسل السادس</h3>
                        <div class="series-meta">
                            <span class="series-year">2023</span>
                            <span class="series-rating"><i class="fas fa-star"></i> 8.6</span>
                        </div>
                        <p class="series-description">
                            وصف مختصر للمسلسل يوضح القصة والأحداث الرئيسية بطريقة مشوقة تجذب المشاهد لمتابعة العمل.
                        </p>
                        <div class="series-stats">
                            <div class="stat-item">
                                <i class="fas fa-list"></i>
                                <span>22 حلقة</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-clock"></i>
                                <span>48 دقيقة</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-eye"></i>
                                <span>950K مشاهدة</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            <div class="pagination">
                <a href="#" class="page-btn">السابق</a>
                <a href="#" class="page-btn active">1</a>
                <a href="#" class="page-btn">2</a>
                <a href="#" class="page-btn">3</a>
                <a href="#" class="page-btn">4</a>
                <a href="#" class="page-btn">5</a>
                <a href="#" class="page-btn">التالي</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>CinemaHub</h3>
                    <p>موقعك المفضل لمشاهدة الأفلام والمسلسلات العربية والأجنبية</p>
                </div>
                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="#">الأفلام</a></li>
                        <li><a href="#">المسلسلات</a></li>
                        <li><a href="#">الأحدث</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>تواصل معنا</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 CinemaHub. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
    <script>
        // Filter functionality
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                const filterType = this.textContent;
                console.log('تصفية حسب:', filterType);
                // Here you would implement actual filtering logic
            });
        });

        // Series card interactions
        document.querySelectorAll('.series-card').forEach(card => {
            card.addEventListener('click', function() {
                const title = this.querySelector('.series-title').textContent;
                console.log('النقر على المسلسل:', title);
                // Here you would navigate to series details page
            });
        });
    </script>
</body>
</html>
