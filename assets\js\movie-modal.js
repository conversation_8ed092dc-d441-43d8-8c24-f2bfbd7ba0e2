// ===== Movie Modal Functions =====

// Create modal HTML structure
function createMovieModal() {
    const modalHTML = `
        <div class="modal fade movie-modal" id="movieModal" tabindex="-1" aria-labelledby="movieModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header border-0">
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-0">
                        <div id="movieModalContent">
                            <!-- Content will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Add modal to body if it doesn't exist
    if (!document.getElementById('movieModal')) {
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }
}

// Open movie modal with details
async function openMovieModal(id, type) {
    createMovieModal();
    
    const modal = new bootstrap.Modal(document.getElementById('movieModal'));
    const modalContent = document.getElementById('movieModalContent');
    
    // Show loading
    modalContent.innerHTML = `
        <div class="lazy-loading">
            <div class="lazy-spinner"></div>
        </div>
    `;
    
    modal.show();
    
    try {
        // Fetch movie/series details
        const [detailsResponse, creditsResponse, videosResponse] = await Promise.all([
            fetch(`${BASE_URL}/${type}/${id}?api_key=${API_KEY}&language=ar`),
            fetch(`${BASE_URL}/${type}/${id}/credits?api_key=${API_KEY}`),
            fetch(`${BASE_URL}/${type}/${id}/videos?api_key=${API_KEY}`)
        ]);
        
        const details = await detailsResponse.json();
        const credits = await creditsResponse.json();
        const videos = await videosResponse.json();
        
        // Display movie details
        displayMovieDetails(details, credits, videos, type);
        
    } catch (error) {
        console.error('Error loading movie details:', error);
        modalContent.innerHTML = `
            <div class="alert alert-danger m-4">
                <i class="fas fa-exclamation-triangle"></i>
                حدث خطأ في تحميل تفاصيل ${type === 'movie' ? 'الفيلم' : 'المسلسل'}
            </div>
        `;
    }
}

// Display movie details in modal
function displayMovieDetails(details, credits, videos, type) {
    const modalContent = document.getElementById('movieModalContent');
    
    const title = details.title || details.name;
    const releaseDate = details.release_date || details.first_air_date;
    const year = releaseDate ? releaseDate.split('-')[0] : 'غير محدد';
    const rating = details.vote_average ? details.vote_average.toFixed(1) : 'N/A';
    const runtime = details.runtime || (details.episode_run_time && details.episode_run_time[0]) || 'غير محدد';
    const backdropPath = details.backdrop_path ? BACKDROP_BASE_URL + details.backdrop_path : '';
    const posterPath = details.poster_path ? IMAGE_BASE_URL + details.poster_path : 'assets/images/no-poster.jpg';
    
    // Get genres
    const genres = details.genres ? details.genres.map(g => g.name).join(', ') : 'غير محدد';
    
    // Get cast (first 5 actors)
    const cast = credits.cast ? credits.cast.slice(0, 5).map(actor => actor.name).join(', ') : 'غير محدد';
    
    // Get director
    const director = credits.crew ? credits.crew.find(person => person.job === 'Director')?.name || 'غير محدد' : 'غير محدد';
    
    // Get trailer
    const trailer = videos.results ? videos.results.find(video => video.type === 'Trailer' && video.site === 'YouTube') : null;
    
    modalContent.innerHTML = `
        <div class="movie-detail-header" style="background-image: url('${backdropPath}')">
            <div class="movie-detail-content">
                <div class="row align-items-end">
                    <div class="col-md-3">
                        <img src="${posterPath}" alt="${title}" class="img-fluid rounded shadow-lg">
                    </div>
                    <div class="col-md-9">
                        <h2 class="text-white mb-3">${title}</h2>
                        <div class="movie-meta-info mb-3">
                            <span class="badge bg-primary me-2">${year}</span>
                            <span class="badge bg-secondary me-2">${runtime} دقيقة</span>
                            <span class="badge bg-warning">
                                <i class="fas fa-star"></i> ${rating}
                            </span>
                        </div>
                        <p class="text-light mb-3">${details.overview || 'لا يوجد وصف متاح'}</p>
                        <div class="movie-actions">
                            ${trailer ? `
                                <button class="btn btn-danger me-2" onclick="playTrailer('${trailer.key}')">
                                    <i class="fas fa-play"></i> مشاهدة الإعلان
                                </button>
                            ` : ''}
                            <button class="btn btn-success me-2" onclick="showServers(${details.id}, '${type}')">
                                <i class="fas fa-play-circle"></i> مشاهدة ${type === 'movie' ? 'الفيلم' : 'المسلسل'}
                            </button>
                            <button class="btn btn-outline-light" onclick="addToWatchlist(${details.id}, '${type}')">
                                <i class="fas fa-bookmark"></i> إضافة للمفضلة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="movie-detail-info">
            <div class="row">
                <div class="col-md-8">
                    <h5 class="text-white mb-3">معلومات إضافية</h5>
                    <div class="info-grid">
                        <div class="info-item">
                            <strong>النوع:</strong> ${genres}
                        </div>
                        <div class="info-item">
                            <strong>الإخراج:</strong> ${director}
                        </div>
                        <div class="info-item">
                            <strong>التمثيل:</strong> ${cast}
                        </div>
                        <div class="info-item">
                            <strong>تاريخ الإصدار:</strong> ${releaseDate || 'غير محدد'}
                        </div>
                        ${type === 'tv' ? `
                            <div class="info-item">
                                <strong>عدد المواسم:</strong> ${details.number_of_seasons || 'غير محدد'}
                            </div>
                            <div class="info-item">
                                <strong>عدد الحلقات:</strong> ${details.number_of_episodes || 'غير محدد'}
                            </div>
                        ` : ''}
                    </div>
                    
                    <!-- Servers Section -->
                    <div class="servers-section mt-4" id="serversSection" style="display: none;">
                        <h5 class="text-white mb-3">سيرفرات المشاهدة</h5>
                        <div class="movie-servers" id="movieServers">
                            <!-- Servers will be loaded here -->
                        </div>
                        <div class="player-container mt-3" id="playerContainer">
                            <!-- Video player will be loaded here -->
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <h5 class="text-white mb-3">أفلام مشابهة</h5>
                    <div id="similarMovies">
                        <!-- Similar movies will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Load similar movies/series
    loadSimilarContent(details.id, type);
}

// Load similar content
async function loadSimilarContent(id, type) {
    try {
        const response = await fetch(`${BASE_URL}/${type}/${id}/similar?api_key=${API_KEY}&language=ar&page=1`);
        const data = await response.json();
        
        const similarContainer = document.getElementById('similarMovies');
        similarContainer.innerHTML = '';
        
        data.results.slice(0, 4).forEach(item => {
            const similarItem = document.createElement('div');
            similarItem.className = 'similar-item mb-3';
            similarItem.innerHTML = `
                <div class="row g-2">
                    <div class="col-4">
                        <img src="${item.poster_path ? IMAGE_BASE_URL + item.poster_path : 'assets/images/no-poster.jpg'}" 
                             alt="${item.title || item.name}" class="img-fluid rounded">
                    </div>
                    <div class="col-8">
                        <h6 class="text-white mb-1">${item.title || item.name}</h6>
                        <small class="text-muted">${(item.release_date || item.first_air_date || '').split('-')[0] || 'غير محدد'}</small>
                        <div class="mt-1">
                            <i class="fas fa-star text-warning"></i>
                            <small class="text-light">${item.vote_average ? item.vote_average.toFixed(1) : 'N/A'}</small>
                        </div>
                    </div>
                </div>
            `;
            
            similarItem.addEventListener('click', () => {
                openMovieModal(item.id, type);
            });
            
            similarContainer.appendChild(similarItem);
        });
        
    } catch (error) {
        console.error('Error loading similar content:', error);
    }
}

// Show servers for watching
function showServers(id, type) {
    const serversSection = document.getElementById('serversSection');
    const movieServers = document.getElementById('movieServers');
    
    serversSection.style.display = 'block';
    
    // Sample servers (in real implementation, these would come from your database)
    const servers = [
        { name: 'سيرفر 1', url: `https://example.com/watch/${type}/${id}/server1`, quality: 'HD' },
        { name: 'سيرفر 2', url: `https://example.com/watch/${type}/${id}/server2`, quality: 'FHD' },
        { name: 'سيرفر 3', url: `https://example.com/watch/${type}/${id}/server3`, quality: '4K' },
        { name: 'تحميل مباشر', url: `https://example.com/download/${type}/${id}`, quality: 'HD', download: true }
    ];
    
    movieServers.innerHTML = '';
    
    servers.forEach((server, index) => {
        const serverBtn = document.createElement('button');
        serverBtn.className = `server-btn ${index === 0 ? 'active' : ''}`;
        serverBtn.innerHTML = `
            ${server.download ? '<i class="fas fa-download"></i>' : '<i class="fas fa-play"></i>'}
            ${server.name} (${server.quality})
        `;
        
        serverBtn.addEventListener('click', () => {
            // Remove active class from all buttons
            document.querySelectorAll('.server-btn').forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            serverBtn.classList.add('active');
            
            if (server.download) {
                // Handle download
                window.open(server.url, '_blank');
            } else {
                // Load video player
                loadVideoPlayer(server.url);
            }
        });
        
        movieServers.appendChild(serverBtn);
    });
    
    // Load first server by default
    if (servers.length > 0 && !servers[0].download) {
        loadVideoPlayer(servers[0].url);
    }
}

// Load video player
function loadVideoPlayer(url) {
    const playerContainer = document.getElementById('playerContainer');
    
    playerContainer.innerHTML = `
        <div class="video-player">
            <iframe src="${url}" 
                    width="100%" 
                    height="400" 
                    frameborder="0" 
                    allowfullscreen
                    allow="autoplay; encrypted-media">
            </iframe>
        </div>
    `;
}

// Play trailer
function playTrailer(videoKey) {
    const playerContainer = document.getElementById('playerContainer');
    const serversSection = document.getElementById('serversSection');
    
    serversSection.style.display = 'block';
    
    playerContainer.innerHTML = `
        <div class="video-player">
            <iframe src="https://www.youtube.com/embed/${videoKey}?autoplay=1" 
                    width="100%" 
                    height="400" 
                    frameborder="0" 
                    allowfullscreen
                    allow="autoplay; encrypted-media">
            </iframe>
        </div>
    `;
}

// Add to watchlist
function addToWatchlist(id, type) {
    // Get existing watchlist from localStorage
    let watchlist = JSON.parse(localStorage.getItem('watchlist') || '[]');
    
    // Check if item already exists
    const exists = watchlist.some(item => item.id === id && item.type === type);
    
    if (!exists) {
        watchlist.push({ id, type, addedAt: new Date().toISOString() });
        localStorage.setItem('watchlist', JSON.stringify(watchlist));
        showNotification('تم إضافة العنصر إلى المفضلة بنجاح', 'success');
    } else {
        showNotification('العنصر موجود بالفعل في المفضلة', 'warning');
    }
}

// CSS for modal components
const modalStyles = `
    <style>
        .info-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 10px;
        }
        
        .info-item {
            color: var(--light-color);
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .info-item strong {
            color: var(--primary-color);
            margin-left: 10px;
        }
        
        .similar-item {
            cursor: pointer;
            padding: 10px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }
        
        .similar-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .video-player {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
        }
        
        .servers-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: var(--border-radius);
        }
        
        @media (max-width: 768px) {
            .movie-detail-content .row {
                text-align: center;
            }
            
            .movie-actions {
                margin-top: 20px;
            }
            
            .movie-actions .btn {
                margin-bottom: 10px;
                width: 100%;
            }
        }
    </style>
`;

// Add styles to head
document.head.insertAdjacentHTML('beforeend', modalStyles);
