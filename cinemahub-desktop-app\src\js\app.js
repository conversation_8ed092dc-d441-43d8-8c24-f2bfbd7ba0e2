/**
 * CinemaHub Pro Desktop Application
 * Main Application JavaScript
 * 
 * @version 1.0.0
 * <AUTHOR> Team
 */

const { ipcRenderer } = require('electron');

class CinemaHubApp {
    constructor() {
        this.currentPage = 'dashboard';
        this.sidebarCollapsed = false;
        this.isOnline = true;
        this.syncInProgress = false;
        
        this.init();
    }

    /**
     * Initialize the application
     */
    async init() {
        try {
            await this.hideLoadingScreen();
            this.setupEventListeners();
            this.setupNavigation();
            this.setupSidebar();
            this.loadDashboardData();
            this.checkConnectionStatus();
            this.setupAutoSave();
            
            console.log('CinemaHub Pro Desktop initialized successfully!');
        } catch (error) {
            console.error('Failed to initialize app:', error);
            this.showToast('خطأ في تشغيل التطبيق', 'error');
        }
    }

    /**
     * Hide loading screen
     */
    async hideLoadingScreen() {
        return new Promise((resolve) => {
            setTimeout(() => {
                const loadingScreen = document.getElementById('loadingScreen');
                if (loadingScreen) {
                    loadingScreen.classList.add('hidden');
                    setTimeout(() => {
                        loadingScreen.remove();
                        resolve();
                    }, 500);
                } else {
                    resolve();
                }
            }, 2000);
        });
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Menu events from main process
        ipcRenderer.on('menu-new-project', () => this.newProject());
        ipcRenderer.on('menu-open-project', (event, path) => this.openProject(path));
        ipcRenderer.on('menu-save', () => this.saveProject());
        ipcRenderer.on('menu-settings', () => this.showPage('settings'));
        ipcRenderer.on('menu-preview', () => this.previewWebsite());
        ipcRenderer.on('menu-upload', () => this.uploadToServer());
        ipcRenderer.on('menu-backup', () => this.createBackup());

        // File change events
        ipcRenderer.on('file-changed', (event, filePath) => this.onFileChanged(filePath));
        ipcRenderer.on('file-added', (event, filePath) => this.onFileAdded(filePath));
        ipcRenderer.on('file-removed', (event, filePath) => this.onFileRemoved(filePath));

        // Window events
        window.addEventListener('beforeunload', () => this.onBeforeUnload());
        window.addEventListener('online', () => this.setOnlineStatus(true));
        window.addEventListener('offline', () => this.setOnlineStatus(false));

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
    }

    /**
     * Setup navigation
     */
    setupNavigation() {
        const navItems = document.querySelectorAll('.nav-item');
        
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const page = item.dataset.page;
                if (page) {
                    this.showPage(page);
                }
            });
        });

        // Header buttons
        const previewBtn = document.getElementById('previewBtn');
        const syncBtn = document.getElementById('syncBtn');

        if (previewBtn) {
            previewBtn.addEventListener('click', () => this.previewWebsite());
        }

        if (syncBtn) {
            syncBtn.addEventListener('click', () => this.startSync());
        }
    }

    /**
     * Setup sidebar
     */
    setupSidebar() {
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');

        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', () => {
                this.sidebarCollapsed = !this.sidebarCollapsed;
                sidebar.classList.toggle('collapsed', this.sidebarCollapsed);
                
                // Save state
                this.saveAppState();
            });
        }

        // Load saved state
        this.loadAppState();
    }

    /**
     * Show specific page
     */
    showPage(pageId) {
        // Hide all pages
        const pages = document.querySelectorAll('.page-content');
        pages.forEach(page => page.classList.remove('active'));

        // Show target page
        const targetPage = document.getElementById(`${pageId}-page`);
        if (targetPage) {
            targetPage.classList.add('active');
        }

        // Update navigation
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.classList.remove('active');
            if (item.dataset.page === pageId) {
                item.classList.add('active');
            }
        });

        // Update page title
        const pageTitle = document.getElementById('pageTitle');
        const currentPageSpan = document.getElementById('currentPage');
        
        const pageTitles = {
            dashboard: 'لوحة التحكم',
            movies: 'إدارة الأفلام',
            series: 'إدارة المسلسلات',
            categories: 'التصنيفات',
            website: 'إدارة الموقع',
            sync: 'المزامنة',
            settings: 'الإعدادات'
        };

        if (pageTitle && currentPageSpan) {
            const title = pageTitles[pageId] || pageId;
            pageTitle.textContent = title;
            currentPageSpan.textContent = title;
        }

        this.currentPage = pageId;
        this.loadPageData(pageId);
    }

    /**
     * Load page specific data
     */
    async loadPageData(pageId) {
        try {
            switch (pageId) {
                case 'dashboard':
                    await this.loadDashboardData();
                    break;
                case 'movies':
                    await this.loadMoviesData();
                    break;
                case 'series':
                    await this.loadSeriesData();
                    break;
                case 'categories':
                    await this.loadCategoriesData();
                    break;
                case 'website':
                    await this.loadWebsiteData();
                    break;
                case 'sync':
                    await this.loadSyncData();
                    break;
                case 'settings':
                    await this.loadSettingsData();
                    break;
            }
        } catch (error) {
            console.error(`Failed to load ${pageId} data:`, error);
            this.showToast(`خطأ في تحميل بيانات ${pageId}`, 'error');
        }
    }

    /**
     * Load dashboard data
     */
    async loadDashboardData() {
        try {
            const stats = await this.getStats();
            
            // Update stat cards
            document.getElementById('moviesCount').textContent = stats.movies || 0;
            document.getElementById('seriesCount').textContent = stats.series || 0;
            document.getElementById('categoriesCount').textContent = stats.categories || 0;
            document.getElementById('lastSync').textContent = stats.lastSync || '--';

            // Load recent activity
            await this.loadRecentActivity();
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
        }
    }

    /**
     * Get statistics
     */
    async getStats() {
        try {
            // This would normally fetch from database
            return {
                movies: 150,
                series: 85,
                categories: 12,
                lastSync: 'منذ ساعة'
            };
        } catch (error) {
            console.error('Failed to get stats:', error);
            return {};
        }
    }

    /**
     * Load recent activity
     */
    async loadRecentActivity() {
        const activityList = document.getElementById('activityList');
        if (!activityList) return;

        // This would normally fetch from database
        const activities = [
            {
                icon: 'fas fa-plus text-success',
                text: 'تم إضافة فيلم جديد: "فيلم تجريبي"',
                time: 'منذ 5 دقائق'
            },
            {
                icon: 'fas fa-edit text-warning',
                text: 'تم تحديث معلومات مسلسل "مسلسل تجريبي"',
                time: 'منذ 15 دقيقة'
            },
            {
                icon: 'fas fa-sync text-info',
                text: 'تم مزامنة البيانات مع الموقع',
                time: 'منذ ساعة'
            }
        ];

        activityList.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <p>${activity.text}</p>
                    <small>${activity.time}</small>
                </div>
            </div>
        `).join('');
    }

    /**
     * Load movies data
     */
    async loadMoviesData() {
        const moviesGrid = document.getElementById('moviesGrid');
        if (!moviesGrid) return;

        // Show loading
        moviesGrid.innerHTML = '<div class="text-center p-4"><div class="spinner"></div><p>جاري تحميل الأفلام...</p></div>';

        try {
            // This would normally fetch from database
            const movies = await this.getMovies();
            
            if (movies.length === 0) {
                moviesGrid.innerHTML = `
                    <div class="text-center p-4">
                        <i class="fas fa-film fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد أفلام بعد</p>
                        <button class="btn btn-primary" onclick="showAddMovieModal()">
                            <i class="fas fa-plus"></i> إضافة فيلم جديد
                        </button>
                    </div>
                `;
                return;
            }

            moviesGrid.innerHTML = movies.map(movie => this.createMovieCard(movie)).join('');
        } catch (error) {
            console.error('Failed to load movies:', error);
            moviesGrid.innerHTML = `
                <div class="text-center p-4">
                    <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                    <p class="text-danger">خطأ في تحميل الأفلام</p>
                    <button class="btn btn-outline-primary" onclick="app.loadMoviesData()">
                        <i class="fas fa-redo"></i> إعادة المحاولة
                    </button>
                </div>
            `;
        }
    }

    /**
     * Get movies from database
     */
    async getMovies() {
        // This would normally fetch from database
        return [
            {
                id: 1,
                title: 'فيلم تجريبي 1',
                poster: 'https://via.placeholder.com/300x400/1e293b/ffffff?text=فيلم+1',
                year: 2024,
                rating: 8.5,
                genre: 'أكشن'
            },
            {
                id: 2,
                title: 'فيلم تجريبي 2',
                poster: 'https://via.placeholder.com/300x400/1e293b/ffffff?text=فيلم+2',
                year: 2024,
                rating: 7.8,
                genre: 'كوميديا'
            }
        ];
    }

    /**
     * Create movie card HTML
     */
    createMovieCard(movie) {
        return `
            <div class="movie-card" data-id="${movie.id}">
                <div class="movie-poster">
                    <img src="${movie.poster}" alt="${movie.title}" loading="lazy">
                    <div class="movie-overlay">
                        <button class="play-btn" onclick="app.editMovie(${movie.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <div class="overlay-actions">
                            <button class="overlay-btn" onclick="app.viewMovie(${movie.id})" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="overlay-btn" onclick="app.deleteMovie(${movie.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="movie-info">
                    <h3 class="movie-title">${movie.title}</h3>
                    <div class="movie-meta">
                        <span class="meta-item">
                            <i class="fas fa-calendar"></i>
                            ${movie.year}
                        </span>
                        <span class="meta-item">
                            <i class="fas fa-tag"></i>
                            ${movie.genre}
                        </span>
                    </div>
                    <div class="movie-rating">
                        <i class="fas fa-star"></i>
                        <span>${movie.rating}</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Check connection status
     */
    checkConnectionStatus() {
        this.setOnlineStatus(navigator.onLine);
        
        // Check server connection
        setInterval(async () => {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                this.setOnlineStatus(data.status === 'running');
            } catch (error) {
                this.setOnlineStatus(false);
            }
        }, 30000); // Check every 30 seconds
    }

    /**
     * Set online status
     */
    setOnlineStatus(isOnline) {
        this.isOnline = isOnline;
        
        const statusDot = document.getElementById('statusDot');
        const statusText = document.getElementById('statusText');
        
        if (statusDot && statusText) {
            if (isOnline) {
                statusDot.className = 'status-dot online';
                statusText.textContent = 'متصل';
            } else {
                statusDot.className = 'status-dot offline';
                statusText.textContent = 'غير متصل';
            }
        }
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info', duration = 5000) {
        const toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) return;

        const toastId = 'toast-' + Date.now();
        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        const toast = document.createElement('div');
        toast.id = toastId;
        toast.className = `toast show`;
        toast.innerHTML = `
            <div class="toast-header">
                <i class="${iconMap[type] || iconMap.info} me-2"></i>
                <strong class="me-auto">CinemaHub Pro</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        `;

        toastContainer.appendChild(toast);

        // Auto remove after duration
        setTimeout(() => {
            const toastElement = document.getElementById(toastId);
            if (toastElement) {
                toastElement.remove();
            }
        }, duration);
    }

    /**
     * Handle keyboard shortcuts
     */
    handleKeyboardShortcuts(e) {
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 's':
                    e.preventDefault();
                    this.saveProject();
                    break;
                case 'n':
                    e.preventDefault();
                    this.newProject();
                    break;
                case 'o':
                    e.preventDefault();
                    this.openProject();
                    break;
                case 'p':
                    e.preventDefault();
                    this.previewWebsite();
                    break;
                case 'u':
                    e.preventDefault();
                    this.uploadToServer();
                    break;
            }
        }

        if (e.key === 'F12') {
            e.preventDefault();
            // Toggle dev tools would be handled by main process
        }
    }

    /**
     * Save app state
     */
    async saveAppState() {
        try {
            const state = {
                sidebarCollapsed: this.sidebarCollapsed,
                currentPage: this.currentPage,
                lastSaved: new Date().toISOString()
            };

            await ipcRenderer.invoke('store-set', 'appState', state);
        } catch (error) {
            console.error('Failed to save app state:', error);
        }
    }

    /**
     * Load app state
     */
    async loadAppState() {
        try {
            const state = await ipcRenderer.invoke('store-get', 'appState');
            if (state) {
                this.sidebarCollapsed = state.sidebarCollapsed || false;
                this.currentPage = state.currentPage || 'dashboard';

                // Apply sidebar state
                const sidebar = document.getElementById('sidebar');
                if (sidebar) {
                    sidebar.classList.toggle('collapsed', this.sidebarCollapsed);
                }

                // Show current page
                this.showPage(this.currentPage);
            }
        } catch (error) {
            console.error('Failed to load app state:', error);
        }
    }

    /**
     * Setup auto save
     */
    setupAutoSave() {
        setInterval(() => {
            this.saveAppState();
        }, 30000); // Auto save every 30 seconds
    }

    /**
     * Handle before unload
     */
    onBeforeUnload() {
        this.saveAppState();
    }

    // Placeholder methods for future implementation
    async newProject() { this.showToast('إنشاء مشروع جديد'); }
    async openProject(path) { this.showToast('فتح مشروع'); }
    async saveProject() { this.showToast('تم حفظ المشروع', 'success'); }
    async previewWebsite() { this.showToast('معاينة الموقع'); }
    async uploadToServer() { this.showToast('رفع إلى الخادم'); }
    async createBackup() { this.showToast('إنشاء نسخة احتياطية'); }
    async startSync() { this.showToast('بدء المزامنة'); }
    
    onFileChanged(filePath) { console.log('File changed:', filePath); }
    onFileAdded(filePath) { console.log('File added:', filePath); }
    onFileRemoved(filePath) { console.log('File removed:', filePath); }
    
    async loadSeriesData() { console.log('Loading series data...'); }
    async loadCategoriesData() { console.log('Loading categories data...'); }
    async loadWebsiteData() { console.log('Loading website data...'); }
    async loadSyncData() { console.log('Loading sync data...'); }
    async loadSettingsData() { console.log('Loading settings data...'); }
    
    editMovie(id) { this.showToast(`تحرير الفيلم ${id}`); }
    viewMovie(id) { this.showToast(`عرض الفيلم ${id}`); }
    deleteMovie(id) { this.showToast(`حذف الفيلم ${id}`, 'warning'); }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new CinemaHubApp();
});

// Global functions for HTML onclick events
window.showAddMovieModal = () => app.showToast('إضافة فيلم جديد');
window.showAddSeriesModal = () => app.showToast('إضافة مسلسل جديد');
window.exportData = () => app.showToast('تصدير البيانات');
window.createBackup = () => app.createBackup();
window.openFile = () => app.showToast('فتح ملف');
window.saveFile = () => app.showToast('حفظ ملف');
window.formatCode = () => app.showToast('تنسيق الكود');
window.startSync = () => app.startSync();
window.checkForUpdates = () => app.showToast('التحقق من التحديثات');
window.showAbout = () => app.showToast('حول البرنامج');
