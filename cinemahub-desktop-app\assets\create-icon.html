<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء أيقونة CinemaHub Pro</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            text-align: center;
            max-width: 800px;
        }
        
        h1 {
            color: #dc2626;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }
        
        .icon-container {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            display: inline-block;
        }
        
        #iconCanvas {
            border: 2px solid #dc2626;
            border-radius: 15px;
            background: white;
        }
        
        .buttons {
            margin: 30px 0;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        button {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: right;
            line-height: 1.8;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: rgba(220, 38, 38, 0.1);
            border-radius: 8px;
            border-right: 4px solid #dc2626;
        }
        
        .success {
            background: rgba(34, 197, 94, 0.2);
            border-color: #22c55e;
            color: #22c55e;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 إنشاء أيقونة CinemaHub Pro Desktop</h1>
        
        <div class="icon-container">
            <canvas id="iconCanvas" width="512" height="512"></canvas>
        </div>
        
        <div class="buttons">
            <button onclick="generateIcon()">🎨 إنشاء الأيقونة</button>
            <button onclick="downloadIcon()">💾 تحميل الأيقونة</button>
            <button onclick="generateVariation()">🔄 تغيير التصميم</button>
        </div>
        
        <div class="instructions">
            <h3>📋 التعليمات:</h3>
            <div class="step">
                <strong>1.</strong> اضغط على "إنشاء الأيقونة" لرسم الأيقونة
            </div>
            <div class="step">
                <strong>2.</strong> اضغط على "تحميل الأيقونة" لحفظها كملف PNG
            </div>
            <div class="step">
                <strong>3.</strong> احفظ الملف باسم <code>icon.png</code> في مجلد <code>assets</code>
            </div>
            <div class="step">
                <strong>4.</strong> شغل ملف <code>create-installer.bat</code> لإنشاء ملف التثبيت
            </div>
        </div>
    </div>

    <script>
        let canvas = document.getElementById('iconCanvas');
        let ctx = canvas.getContext('2d');
        let currentVariation = 0;
        
        const variations = [
            { bg: '#dc2626', accent: '#ffffff', secondary: '#1e293b' },
            { bg: '#1e293b', accent: '#dc2626', secondary: '#ffffff' },
            { bg: '#0f172a', accent: '#f59e0b', secondary: '#dc2626' },
            { bg: '#7c3aed', accent: '#ffffff', secondary: '#fbbf24' },
            { bg: '#059669', accent: '#ffffff', secondary: '#dc2626' }
        ];
        
        function generateIcon() {
            const colors = variations[currentVariation % variations.length];
            
            // Clear canvas
            ctx.clearRect(0, 0, 512, 512);
            
            // Background circle with gradient
            const gradient = ctx.createRadialGradient(256, 256, 0, 256, 256, 256);
            gradient.addColorStop(0, colors.bg);
            gradient.addColorStop(1, adjustBrightness(colors.bg, -30));
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(256, 256, 240, 0, 2 * Math.PI);
            ctx.fill();
            
            // Border
            ctx.strokeStyle = adjustBrightness(colors.bg, -50);
            ctx.lineWidth = 8;
            ctx.stroke();
            
            // Film strip background
            ctx.fillStyle = colors.secondary;
            ctx.fillRect(80, 180, 352, 152);
            
            // Film strip border
            ctx.strokeStyle = adjustBrightness(colors.secondary, -30);
            ctx.lineWidth = 4;
            ctx.strokeRect(80, 180, 352, 152);
            
            // Film holes (left side)
            ctx.fillStyle = colors.accent;
            for (let i = 0; i < 3; i++) {
                ctx.fillRect(100, 200 + i * 40, 20, 20);
            }
            
            // Film holes (right side)
            for (let i = 0; i < 3; i++) {
                ctx.fillRect(392, 200 + i * 40, 20, 20);
            }
            
            // Film frames
            ctx.fillStyle = adjustBrightness(colors.secondary, 20);
            const framePositions = [
                [140, 200], [240, 200], [340, 200],
                [140, 272], [240, 272], [340, 272]
            ];
            
            framePositions.forEach(([x, y]) => {
                ctx.fillRect(x, y, 80, 60);
                ctx.strokeStyle = adjustBrightness(colors.secondary, -20);
                ctx.lineWidth = 2;
                ctx.strokeRect(x, y, 80, 60);
            });
            
            // Central play button background
            ctx.fillStyle = colors.accent;
            ctx.beginPath();
            ctx.arc(256, 256, 50, 0, 2 * Math.PI);
            ctx.fill();
            
            // Play button shadow
            ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
            ctx.beginPath();
            ctx.arc(258, 258, 50, 0, 2 * Math.PI);
            ctx.fill();
            
            // Play button
            ctx.fillStyle = colors.bg;
            ctx.beginPath();
            ctx.moveTo(240, 230);
            ctx.lineTo(240, 282);
            ctx.lineTo(290, 256);
            ctx.closePath();
            ctx.fill();
            
            // Text - CinemaHub
            ctx.fillStyle = colors.accent;
            ctx.font = 'bold 36px Arial, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText('CinemaHub', 256, 120);
            
            // Text shadow
            ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.fillText('CinemaHub', 258, 122);
            ctx.fillStyle = colors.accent;
            ctx.fillText('CinemaHub', 256, 120);
            
            // Text - Pro Desktop
            ctx.font = '24px Arial, sans-serif';
            ctx.fillStyle = colors.accent;
            ctx.fillText('Pro Desktop', 256, 420);
            
            // Text shadow
            ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.fillText('Pro Desktop', 258, 422);
            ctx.fillStyle = colors.accent;
            ctx.fillText('Pro Desktop', 256, 420);
            
            // Add some sparkle effects
            ctx.fillStyle = colors.accent;
            const sparkles = [
                [150, 150], [350, 150], [150, 350], [350, 350],
                [200, 100], [300, 100], [200, 400], [300, 400]
            ];
            
            sparkles.forEach(([x, y]) => {
                ctx.beginPath();
                ctx.arc(x, y, 3, 0, 2 * Math.PI);
                ctx.fill();
            });
            
            showSuccess();
        }
        
        function generateVariation() {
            currentVariation++;
            generateIcon();
        }
        
        function downloadIcon() {
            const link = document.createElement('a');
            link.download = 'icon.png';
            link.href = canvas.toDataURL();
            link.click();
            
            showSuccess('تم تحميل الأيقونة بنجاح! احفظها في مجلد assets باسم icon.png');
        }
        
        function adjustBrightness(hex, percent) {
            const num = parseInt(hex.replace("#", ""), 16);
            const amt = Math.round(2.55 * percent);
            const R = (num >> 16) + amt;
            const G = (num >> 8 & 0x00FF) + amt;
            const B = (num & 0x0000FF) + amt;
            return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
                (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
                (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
        }
        
        function showSuccess(message = 'تم إنشاء الأيقونة بنجاح!') {
            const existingSuccess = document.querySelector('.success-message');
            if (existingSuccess) {
                existingSuccess.remove();
            }
            
            const successDiv = document.createElement('div');
            successDiv.className = 'step success success-message';
            successDiv.innerHTML = `<strong>✅</strong> ${message}`;
            
            document.querySelector('.instructions').appendChild(successDiv);
            
            setTimeout(() => {
                if (successDiv.parentNode) {
                    successDiv.remove();
                }
            }, 5000);
        }
        
        // Generate initial icon
        window.onload = function() {
            generateIcon();
        };
    </script>
</body>
</html>
