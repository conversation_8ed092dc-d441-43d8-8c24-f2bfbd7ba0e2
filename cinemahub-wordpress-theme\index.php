<?php
/**
 * The main template file
 *
 * @package CinemaHub Pro
 */

get_header(); ?>

<!-- Loading Screen -->
<div class="loading-screen" id="loadingScreen">
    <div class="loading-content">
        <div class="loading-logo">
            <div class="loading-text">
                <span class="brand-text">CinemaHub Pro</span>
                <span class="brand-subtitle">سينما هاب برو</span>
                <span class="brand-tagline">تجربة مشاهدة لا تُنسى</span>
            </div>
        </div>
        <div class="spinner"></div>
        <p>جاري تحميل أحدث الأفلام والمسلسلات...</p>
    </div>
</div>

<!-- Main Content -->
<div style="margin-top: 80px; padding: 2rem 0;">
    <div class="container">
        <!-- Categories Section -->
        <section class="categories-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">
                        <i class="fas fa-th-large"></i>
                        تصنيفات الأفلام والمسلسلات
                    </h2>
                </div>

                <div class="categories-grid">
                    <?php
                    // Get movie genres
                    $movie_genres = get_terms(array(
                        'taxonomy' => 'movie_genre',
                        'hide_empty' => false,
                        'number' => 6
                    ));
                    
                    // Default categories if no terms exist
                    $default_categories = array(
                        array('name' => 'أفلام الأكشن', 'icon' => 'fas fa-fist-raised', 'count' => '150+'),
                        array('name' => 'أفلام الكوميديا', 'icon' => 'fas fa-laugh', 'count' => '120+'),
                        array('name' => 'أفلام الرعب', 'icon' => 'fas fa-skull-crossbones', 'count' => '80+'),
                        array('name' => 'مسلسلات تركية', 'icon' => 'fas fa-flag', 'count' => '200+'),
                        array('name' => 'مسلسلات كورية', 'icon' => 'fas fa-yin-yang', 'count' => '100+'),
                        array('name' => 'أفلام وثائقية', 'icon' => 'fas fa-video', 'count' => '60+')
                    );
                    
                    if (!empty($movie_genres)) {
                        foreach ($movie_genres as $genre) {
                            $genre_link = get_term_link($genre);
                            $post_count = $genre->count;
                            ?>
                            <div class="category-card" onclick="window.location.href='<?php echo esc_url($genre_link); ?>'">
                                <div class="category-icon">
                                    <i class="fas fa-film"></i>
                                </div>
                                <h3 class="category-title"><?php echo esc_html($genre->name); ?></h3>
                                <p class="category-count"><?php echo $post_count; ?> عنصر</p>
                            </div>
                            <?php
                        }
                    } else {
                        foreach ($default_categories as $category) {
                            ?>
                            <div class="category-card">
                                <div class="category-icon">
                                    <i class="<?php echo esc_attr($category['icon']); ?>"></i>
                                </div>
                                <h3 class="category-title"><?php echo esc_html($category['name']); ?></h3>
                                <p class="category-count"><?php echo esc_html($category['count']); ?> عنصر</p>
                            </div>
                            <?php
                        }
                    }
                    ?>
                </div>
            </div>
        </section>

        <!-- Latest Movies Section -->
        <section class="movies-section" style="padding: 3rem 0;">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">
                        <i class="fas fa-fire"></i>
                        أحدث الأفلام
                    </h2>
                    <a href="<?php echo get_post_type_archive_link('movie'); ?>" class="view-all-btn">
                        عرض الكل <i class="fas fa-arrow-left"></i>
                    </a>
                </div>

                <div class="movies-grid">
                    <?php
                    $movies_query = new WP_Query(array(
                        'post_type' => 'movie',
                        'posts_per_page' => 8,
                        'post_status' => 'publish'
                    ));

                    if ($movies_query->have_posts()) :
                        while ($movies_query->have_posts()) : $movies_query->the_post();
                            $rating = get_post_meta(get_the_ID(), '_movie_rating', true);
                            $year = get_post_meta(get_the_ID(), '_movie_year', true);
                            $duration = get_post_meta(get_the_ID(), '_movie_duration', true);
                            ?>
                            <div class="movie-card">
                                <div class="movie-poster">
                                    <?php if (has_post_thumbnail()) : ?>
                                        <?php the_post_thumbnail('movie-poster', array('alt' => get_the_title())); ?>
                                    <?php else : ?>
                                        <img src="https://via.placeholder.com/300x400/1e293b/ffffff?text=<?php echo urlencode(get_the_title()); ?>" alt="<?php the_title(); ?>">
                                    <?php endif; ?>
                                    
                                    <div class="movie-overlay">
                                        <div class="play-btn">
                                            <i class="fas fa-play"></i>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="movie-info">
                                    <h3 class="movie-title">
                                        <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                    </h3>
                                    
                                    <div class="movie-meta">
                                        <?php if ($year) : ?>
                                            <span class="meta-item">
                                                <i class="fas fa-calendar"></i>
                                                <?php echo esc_html($year); ?>
                                            </span>
                                        <?php endif; ?>
                                        
                                        <?php if ($duration) : ?>
                                            <span class="meta-item">
                                                <i class="fas fa-clock"></i>
                                                <?php echo esc_html($duration); ?> دقيقة
                                            </span>
                                        <?php endif; ?>
                                        
                                        <?php
                                        $genres = get_the_terms(get_the_ID(), 'movie_genre');
                                        if ($genres && !is_wp_error($genres)) :
                                            foreach (array_slice($genres, 0, 2) as $genre) :
                                        ?>
                                            <span class="meta-item">
                                                <i class="fas fa-tag"></i>
                                                <?php echo esc_html($genre->name); ?>
                                            </span>
                                        <?php 
                                            endforeach;
                                        endif; 
                                        ?>
                                    </div>
                                    
                                    <?php if ($rating) : ?>
                                        <div class="movie-rating">
                                            <i class="fas fa-star"></i>
                                            <span><?php echo esc_html($rating); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php
                        endwhile;
                        wp_reset_postdata();
                    else :
                        // Show placeholder movies if no posts exist
                        for ($i = 1; $i <= 8; $i++) :
                            ?>
                            <div class="movie-card">
                                <div class="movie-poster">
                                    <img src="https://via.placeholder.com/300x400/1e293b/ffffff?text=فيلم+<?php echo $i; ?>" alt="فيلم <?php echo $i; ?>">
                                    <div class="movie-overlay">
                                        <div class="play-btn">
                                            <i class="fas fa-play"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="movie-info">
                                    <h3 class="movie-title">فيلم تجريبي <?php echo $i; ?></h3>
                                    <div class="movie-meta">
                                        <span class="meta-item">
                                            <i class="fas fa-calendar"></i>
                                            2024
                                        </span>
                                        <span class="meta-item">
                                            <i class="fas fa-clock"></i>
                                            120 دقيقة
                                        </span>
                                        <span class="meta-item">
                                            <i class="fas fa-tag"></i>
                                            أكشن
                                        </span>
                                    </div>
                                    <div class="movie-rating">
                                        <i class="fas fa-star"></i>
                                        <span>8.5</span>
                                    </div>
                                </div>
                            </div>
                            <?php
                        endfor;
                    endif;
                    ?>
                </div>
            </div>
        </section>

        <!-- Latest Series Section -->
        <section class="series-section" style="padding: 3rem 0;">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">
                        <i class="fas fa-tv"></i>
                        أحدث المسلسلات
                    </h2>
                    <a href="<?php echo get_post_type_archive_link('series'); ?>" class="view-all-btn">
                        عرض الكل <i class="fas fa-arrow-left"></i>
                    </a>
                </div>

                <div class="movies-grid">
                    <?php
                    $series_query = new WP_Query(array(
                        'post_type' => 'series',
                        'posts_per_page' => 8,
                        'post_status' => 'publish'
                    ));

                    if ($series_query->have_posts()) :
                        while ($series_query->have_posts()) : $series_query->the_post();
                            $rating = get_post_meta(get_the_ID(), '_series_rating', true);
                            $year = get_post_meta(get_the_ID(), '_series_year', true);
                            $seasons = get_post_meta(get_the_ID(), '_series_seasons', true);
                            $episodes = get_post_meta(get_the_ID(), '_series_episodes', true);
                            ?>
                            <div class="movie-card">
                                <div class="movie-poster">
                                    <?php if (has_post_thumbnail()) : ?>
                                        <?php the_post_thumbnail('movie-poster', array('alt' => get_the_title())); ?>
                                    <?php else : ?>
                                        <img src="https://via.placeholder.com/300x400/1e293b/ffffff?text=<?php echo urlencode(get_the_title()); ?>" alt="<?php the_title(); ?>">
                                    <?php endif; ?>
                                    
                                    <div class="movie-overlay">
                                        <div class="play-btn">
                                            <i class="fas fa-play"></i>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="movie-info">
                                    <h3 class="movie-title">
                                        <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                    </h3>
                                    
                                    <div class="movie-meta">
                                        <?php if ($year) : ?>
                                            <span class="meta-item">
                                                <i class="fas fa-calendar"></i>
                                                <?php echo esc_html($year); ?>
                                            </span>
                                        <?php endif; ?>
                                        
                                        <?php if ($seasons) : ?>
                                            <span class="meta-item">
                                                <i class="fas fa-list"></i>
                                                <?php echo esc_html($seasons); ?> موسم
                                            </span>
                                        <?php endif; ?>
                                        
                                        <?php if ($episodes) : ?>
                                            <span class="meta-item">
                                                <i class="fas fa-play-circle"></i>
                                                <?php echo esc_html($episodes); ?> حلقة
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <?php if ($rating) : ?>
                                        <div class="movie-rating">
                                            <i class="fas fa-star"></i>
                                            <span><?php echo esc_html($rating); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php
                        endwhile;
                        wp_reset_postdata();
                    else :
                        // Show placeholder series if no posts exist
                        for ($i = 1; $i <= 8; $i++) :
                            ?>
                            <div class="movie-card">
                                <div class="movie-poster">
                                    <img src="https://via.placeholder.com/300x400/1e293b/ffffff?text=مسلسل+<?php echo $i; ?>" alt="مسلسل <?php echo $i; ?>">
                                    <div class="movie-overlay">
                                        <div class="play-btn">
                                            <i class="fas fa-play"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="movie-info">
                                    <h3 class="movie-title">مسلسل تجريبي <?php echo $i; ?></h3>
                                    <div class="movie-meta">
                                        <span class="meta-item">
                                            <i class="fas fa-calendar"></i>
                                            2024
                                        </span>
                                        <span class="meta-item">
                                            <i class="fas fa-list"></i>
                                            3 مواسم
                                        </span>
                                        <span class="meta-item">
                                            <i class="fas fa-play-circle"></i>
                                            24 حلقة
                                        </span>
                                    </div>
                                    <div class="movie-rating">
                                        <i class="fas fa-star"></i>
                                        <span>9.2</span>
                                    </div>
                                </div>
                            </div>
                            <?php
                        endfor;
                    endif;
                    ?>
                </div>
            </div>
        </section>

        <!-- Blog Posts Section -->
        <?php if (have_posts()) : ?>
            <section class="blog-section" style="padding: 3rem 0;">
                <div class="container">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="fas fa-newspaper"></i>
                            أحدث المقالات
                        </h2>
                    </div>

                    <div class="row">
                        <div class="col-lg-8">
                            <div class="blog-posts">
                                <?php while (have_posts()) : the_post(); ?>
                                    <article class="blog-post" style="background: var(--surface-color); border-radius: var(--border-radius-2xl); padding: 2rem; margin-bottom: 2rem; border: 2px solid rgba(148, 163, 184, 0.1);">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <div class="post-thumbnail" style="margin-bottom: 1.5rem;">
                                                <a href="<?php the_permalink(); ?>">
                                                    <?php the_post_thumbnail('large', array('style' => 'width: 100%; height: 300px; object-fit: cover; border-radius: var(--border-radius-lg);')); ?>
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <h2 class="post-title" style="margin-bottom: 1rem;">
                                            <a href="<?php the_permalink(); ?>" style="color: var(--light-color); text-decoration: none;">
                                                <?php the_title(); ?>
                                            </a>
                                        </h2>
                                        
                                        <div class="post-meta" style="margin-bottom: 1rem; color: var(--gray-400); font-size: var(--font-size-sm);">
                                            <span><i class="fas fa-calendar"></i> <?php echo get_the_date(); ?></span>
                                            <span style="margin: 0 1rem;"><i class="fas fa-user"></i> <?php the_author(); ?></span>
                                            <span><i class="fas fa-comments"></i> <?php comments_number('0 تعليق', 'تعليق واحد', '% تعليقات'); ?></span>
                                        </div>
                                        
                                        <div class="post-excerpt" style="color: var(--gray-300); line-height: 1.6;">
                                            <?php the_excerpt(); ?>
                                        </div>
                                        
                                        <a href="<?php the_permalink(); ?>" class="read-more-btn" style="display: inline-flex; align-items: center; gap: 0.5rem; background: var(--gradient-primary); color: white; padding: 0.75rem 1.5rem; border-radius: var(--border-radius-lg); text-decoration: none; font-weight: 500; margin-top: 1rem; transition: var(--transition);">
                                            اقرأ المزيد <i class="fas fa-arrow-left"></i>
                                        </a>
                                    </article>
                                <?php endwhile; ?>
                                
                                <!-- Pagination -->
                                <div class="pagination-wrapper" style="text-align: center; margin-top: 3rem;">
                                    <?php
                                    the_posts_pagination(array(
                                        'prev_text' => '<i class="fas fa-arrow-right"></i> السابق',
                                        'next_text' => 'التالي <i class="fas fa-arrow-left"></i>',
                                    ));
                                    ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4">
                            <?php get_sidebar(); ?>
                        </div>
                    </div>
                </div>
            </section>
        <?php endif; ?>
    </div>
</div>

<?php get_footer(); ?>
