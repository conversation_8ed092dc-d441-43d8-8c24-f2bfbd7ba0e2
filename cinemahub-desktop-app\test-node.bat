@echo off
echo Testing Node.js...
echo.

node --version
if %errorlevel% equ 0 (
    echo Node.js is working!
) else (
    echo Node.js is NOT installed!
    echo Download from: https://nodejs.org/
)

echo.
npm --version
if %errorlevel% equ 0 (
    echo npm is working!
) else (
    echo npm is NOT working!
)

echo.
echo Current folder: %cd%
echo.

if exist "package.json" (
    echo package.json: FOUND
) else (
    echo package.json: MISSING
)

if exist "assets\icon.png" (
    echo Icon file: FOUND
) else (
    echo Icon file: MISSING
)

echo.
echo Press any key to exit...
pause >nul
