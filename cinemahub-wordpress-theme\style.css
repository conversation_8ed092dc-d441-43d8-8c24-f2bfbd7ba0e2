/*
Theme Name: CinemaHub Pro
Description: Professional WordPress theme for Arabic movies and series websites with modern responsive design and restore functionality
Version: 2.0.0
Author: CinemaHub Team
Text Domain: cinemahub-pro
License: GPL v2 or later
*/

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    color: #ffffff;
    direction: rtl;
    line-height: 1.6;
    min-height: 100vh;
}

/* Header Styles */
.site-header {
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(20px);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    border-bottom: 3px solid #dc2626;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.site-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    text-decoration: none;
    color: white;
    transition: transform 0.3s ease;
}

.site-logo:hover {
    transform: scale(1.05);
}

.logo-icon {
    width: 55px;
    height: 55px;
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
    box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
    position: relative;
    overflow: hidden;
}

.logo-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

.site-logo:hover .logo-icon::before {
    opacity: 1;
    animation: shine 0.8s ease;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.logo-text h1 {
    font-size: 2rem;
    font-weight: 900;
    margin: 0;
    background: linear-gradient(135deg, #ffffff 0%, #dc2626 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-text p {
    font-size: 0.9rem;
    color: #dc2626;
    margin: 0;
    font-weight: 600;
}

.main-nav ul {
    display: flex;
    gap: 1rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.main-nav a {
    color: white;
    text-decoration: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.main-nav a:hover {
    background: rgba(220, 38, 38, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
}

/* Main Content */
.main-content {
    margin-top: 100px;
    padding: 3rem 1rem;
    min-height: calc(100vh - 200px);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

/* Welcome Section */
.welcome-section {
    text-align: center;
    padding: 4rem 2rem;
    background: rgba(30, 41, 59, 0.3);
    border-radius: 25px;
    margin-bottom: 4rem;
    border: 2px solid rgba(220, 38, 38, 0.2);
    backdrop-filter: blur(10px);
}

.main-title {
    font-size: 4.5rem;
    font-weight: 900;
    margin-bottom: 2rem;
    background: linear-gradient(135deg, #dc2626 0%, #fbbf24 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 20px rgba(220, 38, 38, 0.3);
    animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% { filter: drop-shadow(0 0 10px rgba(220, 38, 38, 0.5)); }
    100% { filter: drop-shadow(0 0 20px rgba(220, 38, 38, 0.8)); }
}

.subtitle {
    font-size: 1.6rem;
    color: #cbd5e1;
    margin-bottom: 3rem;
    line-height: 1.8;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 4rem;
}

.btn {
    padding: 1.5rem 3rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.4s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    color: white;
    border: 2px solid transparent;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

.btn-primary:hover {
    box-shadow: 0 20px 40px rgba(220, 38, 38, 0.5);
}

/* Restore Button */
.restore-btn {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    color: white;
    border: 2px solid transparent;
}

.restore-btn:hover {
    box-shadow: 0 20px 40px rgba(5, 150, 105, 0.5);
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
    margin-top: 4rem;
}

.feature-card {
    background: rgba(30, 41, 59, 0.6);
    padding: 3rem;
    border-radius: 25px;
    border: 2px solid rgba(148, 163, 184, 0.1);
    transition: all 0.4s ease;
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #dc2626, #fbbf24, #dc2626);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    border-color: #dc2626;
    transform: translateY(-15px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
}

.feature-icon {
    font-size: 3.5rem;
    color: #dc2626;
    margin-bottom: 2rem;
    display: block;
}

.feature-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: white;
}

.feature-desc {
    color: #cbd5e1;
    line-height: 1.8;
    font-size: 1.1rem;
}

/* Footer */
.site-footer {
    background: rgba(15, 23, 42, 0.95);
    padding: 3rem 0 2rem;
    text-align: center;
    border-top: 3px solid #dc2626;
    margin-top: 4rem;
    backdrop-filter: blur(20px);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.footer-text {
    color: #94a3b8;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.footer-links a {
    color: #cbd5e1;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 15px;
}

.footer-links a:hover {
    color: #dc2626;
    background: rgba(220, 38, 38, 0.1);
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .main-nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .main-title {
        font-size: 2.5rem;
    }
    
    .subtitle {
        font-size: 1.2rem;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 350px;
        justify-content: center;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .feature-card {
        padding: 2rem;
    }
    
    .footer-links {
        flex-direction: column;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 2rem 0.5rem;
    }
    
    .welcome-section {
        padding: 2rem 1rem;
    }
    
    .main-title {
        font-size: 2rem;
    }
    
    .btn {
        padding: 1rem 2rem;
        font-size: 1rem;
    }
}
