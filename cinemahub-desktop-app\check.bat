@echo off
title CinemaHub Pro - System Check

echo ========================================
echo   CinemaHub Pro - System Check
echo ========================================
echo.

echo Checking requirements...
echo.

echo 1. Node.js:
node --version >nul 2>&1
if %errorlevel% equ 0 (
    node --version
    echo   Status: OK
) else (
    echo   Status: NOT INSTALLED
    echo   Download from: https://nodejs.org/
)

echo.
echo 2. npm:
npm --version >nul 2>&1
if %errorlevel% equ 0 (
    npm --version
    echo   Status: OK
) else (
    echo   Status: NOT AVAILABLE
)

echo.
echo 3. Current folder:
echo   %cd%

echo.
echo 4. package.json:
if exist "package.json" (
    echo   Status: FOUND
) else (
    echo   Status: MISSING
    echo   Make sure you are in the correct folder
)

echo.
echo 5. Icon file:
if exist "assets\icon.png" (
    echo   Status: FOUND
) else (
    echo   Status: MISSING
    echo   Open assets\download-icon.html to create it
)

echo.
echo 6. Dependencies:
if exist "node_modules" (
    echo   Status: INSTALLED
) else (
    echo   Status: NOT INSTALLED
    echo   Run: npm install
)

echo.
echo 7. Internet connection:
ping google.com -n 1 >nul 2>&1
if %errorlevel% equ 0 (
    echo   Status: CONNECTED
) else (
    echo   Status: NO CONNECTION
)

echo.
echo ========================================
echo              SUMMARY
echo ========================================

set /a issues=0

node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo - Node.js is missing
    set /a issues+=1
)

npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo - npm is missing
    set /a issues+=1
)

if not exist "package.json" (
    echo - package.json is missing
    set /a issues+=1
)

if not exist "assets\icon.png" (
    echo - Icon file is missing
    set /a issues+=1
)

if %issues% equ 0 (
    echo.
    echo READY TO BUILD!
    echo You can run: build.bat
) else (
    echo.
    echo ISSUES FOUND: %issues%
    echo Please fix the issues above first
)

echo.
echo Press any key to exit...
pause >nul
