<?php
/**
 * CinemaHub Pro WordPress Theme Functions
 * 
 * @package CinemaHub Pro
 * @version 1.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function cinemahub_setup() {
    // Add theme support
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    add_theme_support('custom-logo');
    add_theme_support('customize-selective-refresh-widgets');
    
    // Add post formats support
    add_theme_support('post-formats', array(
        'video',
        'gallery',
        'image',
    ));
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('القائمة الرئيسية', 'cinemahub-pro'),
        'footer' => __('قائمة الفوتر', 'cinemahub-pro'),
    ));
    
    // Add image sizes
    add_image_size('movie-poster', 300, 400, true);
    add_image_size('movie-banner', 1200, 600, true);
    add_image_size('movie-thumb', 150, 200, true);
}
add_action('after_setup_theme', 'cinemahub_setup');

/**
 * Enqueue Scripts and Styles
 */
function cinemahub_scripts() {
    // Enqueue styles
    wp_enqueue_style('cinemahub-style', get_stylesheet_uri(), array(), '1.0.0');
    wp_enqueue_style('bootstrap', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css', array(), '5.3.0');
    wp_enqueue_style('fontawesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css', array(), '6.4.0');
    wp_enqueue_style('swiper', 'https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css', array(), '10.0.0');
    wp_enqueue_style('aos', 'https://unpkg.com/aos@2.3.1/dist/aos.css', array(), '2.3.1');
    wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap', array(), null);
    
    // Enqueue scripts
    wp_enqueue_script('bootstrap', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js', array(), '5.3.0', true);
    wp_enqueue_script('swiper', 'https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js', array(), '10.0.0', true);
    wp_enqueue_script('aos', 'https://unpkg.com/aos@2.3.1/dist/aos.js', array(), '2.3.1', true);
    wp_enqueue_script('cinemahub-main', get_template_directory_uri() . '/js/main.js', array('jquery'), '1.0.0', true);
    
    // Localize script for AJAX
    wp_localize_script('cinemahub-main', 'cinemahub_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('cinemahub_nonce'),
    ));
}
add_action('wp_enqueue_scripts', 'cinemahub_scripts');

/**
 * Register Widget Areas
 */
function cinemahub_widgets_init() {
    register_sidebar(array(
        'name'          => __('الشريط الجانبي', 'cinemahub-pro'),
        'id'            => 'sidebar-1',
        'description'   => __('الشريط الجانبي الرئيسي', 'cinemahub-pro'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('فوتر 1', 'cinemahub-pro'),
        'id'            => 'footer-1',
        'description'   => __('منطقة الفوتر الأولى', 'cinemahub-pro'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ));
    
    register_sidebar(array(
        'name'          => __('فوتر 2', 'cinemahub-pro'),
        'id'            => 'footer-2',
        'description'   => __('منطقة الفوتر الثانية', 'cinemahub-pro'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ));
    
    register_sidebar(array(
        'name'          => __('فوتر 3', 'cinemahub-pro'),
        'id'            => 'footer-3',
        'description'   => __('منطقة الفوتر الثالثة', 'cinemahub-pro'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ));
    
    register_sidebar(array(
        'name'          => __('فوتر 4', 'cinemahub-pro'),
        'id'            => 'footer-4',
        'description'   => __('منطقة الفوتر الرابعة', 'cinemahub-pro'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ));
}
add_action('widgets_init', 'cinemahub_widgets_init');

/**
 * Custom Post Types
 */
function cinemahub_custom_post_types() {
    // Movies Post Type
    register_post_type('movie', array(
        'labels' => array(
            'name' => __('الأفلام', 'cinemahub-pro'),
            'singular_name' => __('فيلم', 'cinemahub-pro'),
            'add_new' => __('إضافة فيلم جديد', 'cinemahub-pro'),
            'add_new_item' => __('إضافة فيلم جديد', 'cinemahub-pro'),
            'edit_item' => __('تعديل الفيلم', 'cinemahub-pro'),
            'new_item' => __('فيلم جديد', 'cinemahub-pro'),
            'view_item' => __('عرض الفيلم', 'cinemahub-pro'),
            'search_items' => __('البحث في الأفلام', 'cinemahub-pro'),
            'not_found' => __('لم يتم العثور على أفلام', 'cinemahub-pro'),
            'not_found_in_trash' => __('لا توجد أفلام في المهملات', 'cinemahub-pro'),
        ),
        'public' => true,
        'has_archive' => true,
        'menu_icon' => 'dashicons-video-alt3',
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'rewrite' => array('slug' => 'movies'),
    ));
    
    // Series Post Type
    register_post_type('series', array(
        'labels' => array(
            'name' => __('المسلسلات', 'cinemahub-pro'),
            'singular_name' => __('مسلسل', 'cinemahub-pro'),
            'add_new' => __('إضافة مسلسل جديد', 'cinemahub-pro'),
            'add_new_item' => __('إضافة مسلسل جديد', 'cinemahub-pro'),
            'edit_item' => __('تعديل المسلسل', 'cinemahub-pro'),
            'new_item' => __('مسلسل جديد', 'cinemahub-pro'),
            'view_item' => __('عرض المسلسل', 'cinemahub-pro'),
            'search_items' => __('البحث في المسلسلات', 'cinemahub-pro'),
            'not_found' => __('لم يتم العثور على مسلسلات', 'cinemahub-pro'),
            'not_found_in_trash' => __('لا توجد مسلسلات في المهملات', 'cinemahub-pro'),
        ),
        'public' => true,
        'has_archive' => true,
        'menu_icon' => 'dashicons-playlist-video',
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'rewrite' => array('slug' => 'series'),
    ));
}
add_action('init', 'cinemahub_custom_post_types');

/**
 * Custom Taxonomies
 */
function cinemahub_custom_taxonomies() {
    // Movie Genres
    register_taxonomy('movie_genre', 'movie', array(
        'labels' => array(
            'name' => __('تصنيفات الأفلام', 'cinemahub-pro'),
            'singular_name' => __('تصنيف', 'cinemahub-pro'),
            'search_items' => __('البحث في التصنيفات', 'cinemahub-pro'),
            'all_items' => __('جميع التصنيفات', 'cinemahub-pro'),
            'edit_item' => __('تعديل التصنيف', 'cinemahub-pro'),
            'update_item' => __('تحديث التصنيف', 'cinemahub-pro'),
            'add_new_item' => __('إضافة تصنيف جديد', 'cinemahub-pro'),
            'new_item_name' => __('اسم التصنيف الجديد', 'cinemahub-pro'),
        ),
        'hierarchical' => true,
        'public' => true,
        'rewrite' => array('slug' => 'movie-genre'),
    ));
    
    // Series Genres
    register_taxonomy('series_genre', 'series', array(
        'labels' => array(
            'name' => __('تصنيفات المسلسلات', 'cinemahub-pro'),
            'singular_name' => __('تصنيف', 'cinemahub-pro'),
            'search_items' => __('البحث في التصنيفات', 'cinemahub-pro'),
            'all_items' => __('جميع التصنيفات', 'cinemahub-pro'),
            'edit_item' => __('تعديل التصنيف', 'cinemahub-pro'),
            'update_item' => __('تحديث التصنيف', 'cinemahub-pro'),
            'add_new_item' => __('إضافة تصنيف جديد', 'cinemahub-pro'),
            'new_item_name' => __('اسم التصنيف الجديد', 'cinemahub-pro'),
        ),
        'hierarchical' => true,
        'public' => true,
        'rewrite' => array('slug' => 'series-genre'),
    ));
    
    // Countries
    register_taxonomy('country', array('movie', 'series'), array(
        'labels' => array(
            'name' => __('البلدان', 'cinemahub-pro'),
            'singular_name' => __('بلد', 'cinemahub-pro'),
            'search_items' => __('البحث في البلدان', 'cinemahub-pro'),
            'all_items' => __('جميع البلدان', 'cinemahub-pro'),
            'edit_item' => __('تعديل البلد', 'cinemahub-pro'),
            'update_item' => __('تحديث البلد', 'cinemahub-pro'),
            'add_new_item' => __('إضافة بلد جديد', 'cinemahub-pro'),
            'new_item_name' => __('اسم البلد الجديد', 'cinemahub-pro'),
        ),
        'hierarchical' => true,
        'public' => true,
        'rewrite' => array('slug' => 'country'),
    ));
}
add_action('init', 'cinemahub_custom_taxonomies');

/**
 * Add Custom Fields Meta Boxes
 */
function cinemahub_add_meta_boxes() {
    add_meta_box(
        'movie_details',
        __('تفاصيل الفيلم', 'cinemahub-pro'),
        'cinemahub_movie_details_callback',
        'movie',
        'normal',
        'high'
    );
    
    add_meta_box(
        'series_details',
        __('تفاصيل المسلسل', 'cinemahub-pro'),
        'cinemahub_series_details_callback',
        'series',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'cinemahub_add_meta_boxes');

/**
 * Movie Details Meta Box Callback
 */
function cinemahub_movie_details_callback($post) {
    wp_nonce_field('cinemahub_save_movie_details', 'cinemahub_movie_details_nonce');
    
    $rating = get_post_meta($post->ID, '_movie_rating', true);
    $year = get_post_meta($post->ID, '_movie_year', true);
    $duration = get_post_meta($post->ID, '_movie_duration', true);
    $trailer_url = get_post_meta($post->ID, '_movie_trailer_url', true);
    $watch_url = get_post_meta($post->ID, '_movie_watch_url', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="movie_rating">التقييم (من 10)</label></th>';
    echo '<td><input type="number" id="movie_rating" name="movie_rating" value="' . esc_attr($rating) . '" min="0" max="10" step="0.1" /></td></tr>';
    
    echo '<tr><th><label for="movie_year">سنة الإنتاج</label></th>';
    echo '<td><input type="number" id="movie_year" name="movie_year" value="' . esc_attr($year) . '" min="1900" max="2030" /></td></tr>';
    
    echo '<tr><th><label for="movie_duration">المدة (بالدقائق)</label></th>';
    echo '<td><input type="number" id="movie_duration" name="movie_duration" value="' . esc_attr($duration) . '" min="1" /></td></tr>';
    
    echo '<tr><th><label for="movie_trailer_url">رابط الإعلان</label></th>';
    echo '<td><input type="url" id="movie_trailer_url" name="movie_trailer_url" value="' . esc_attr($trailer_url) . '" class="regular-text" /></td></tr>';
    
    echo '<tr><th><label for="movie_watch_url">رابط المشاهدة</label></th>';
    echo '<td><input type="url" id="movie_watch_url" name="movie_watch_url" value="' . esc_attr($watch_url) . '" class="regular-text" /></td></tr>';
    echo '</table>';
}

/**
 * Series Details Meta Box Callback
 */
function cinemahub_series_details_callback($post) {
    wp_nonce_field('cinemahub_save_series_details', 'cinemahub_series_details_nonce');

    $rating = get_post_meta($post->ID, '_series_rating', true);
    $year = get_post_meta($post->ID, '_series_year', true);
    $seasons = get_post_meta($post->ID, '_series_seasons', true);
    $episodes = get_post_meta($post->ID, '_series_episodes', true);
    $status = get_post_meta($post->ID, '_series_status', true);
    $trailer_url = get_post_meta($post->ID, '_series_trailer_url', true);
    $watch_url = get_post_meta($post->ID, '_series_watch_url', true);

    echo '<table class="form-table">';
    echo '<tr><th><label for="series_rating">التقييم (من 10)</label></th>';
    echo '<td><input type="number" id="series_rating" name="series_rating" value="' . esc_attr($rating) . '" min="0" max="10" step="0.1" /></td></tr>';

    echo '<tr><th><label for="series_year">سنة الإنتاج</label></th>';
    echo '<td><input type="number" id="series_year" name="series_year" value="' . esc_attr($year) . '" min="1900" max="2030" /></td></tr>';

    echo '<tr><th><label for="series_seasons">عدد المواسم</label></th>';
    echo '<td><input type="number" id="series_seasons" name="series_seasons" value="' . esc_attr($seasons) . '" min="1" /></td></tr>';

    echo '<tr><th><label for="series_episodes">عدد الحلقات</label></th>';
    echo '<td><input type="number" id="series_episodes" name="series_episodes" value="' . esc_attr($episodes) . '" min="1" /></td></tr>';

    echo '<tr><th><label for="series_status">حالة المسلسل</label></th>';
    echo '<td><select id="series_status" name="series_status">';
    echo '<option value="ongoing"' . selected($status, 'ongoing', false) . '>مستمر</option>';
    echo '<option value="completed"' . selected($status, 'completed', false) . '>مكتمل</option>';
    echo '<option value="cancelled"' . selected($status, 'cancelled', false) . '>ملغي</option>';
    echo '</select></td></tr>';

    echo '<tr><th><label for="series_trailer_url">رابط الإعلان</label></th>';
    echo '<td><input type="url" id="series_trailer_url" name="series_trailer_url" value="' . esc_attr($trailer_url) . '" class="regular-text" /></td></tr>';

    echo '<tr><th><label for="series_watch_url">رابط المشاهدة</label></th>';
    echo '<td><input type="url" id="series_watch_url" name="series_watch_url" value="' . esc_attr($watch_url) . '" class="regular-text" /></td></tr>';
    echo '</table>';
}

/**
 * Save Movie Meta Data
 */
function cinemahub_save_movie_details($post_id) {
    if (!isset($_POST['cinemahub_movie_details_nonce']) || !wp_verify_nonce($_POST['cinemahub_movie_details_nonce'], 'cinemahub_save_movie_details')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    $fields = array('movie_rating', 'movie_year', 'movie_duration', 'movie_trailer_url', 'movie_watch_url');

    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
        }
    }
}
add_action('save_post', 'cinemahub_save_movie_details');

/**
 * Save Series Meta Data
 */
function cinemahub_save_series_details($post_id) {
    if (!isset($_POST['cinemahub_series_details_nonce']) || !wp_verify_nonce($_POST['cinemahub_series_details_nonce'], 'cinemahub_save_series_details')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    $fields = array('series_rating', 'series_year', 'series_seasons', 'series_episodes', 'series_status', 'series_trailer_url', 'series_watch_url');

    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
        }
    }
}
add_action('save_post', 'cinemahub_save_series_details');

/**
 * AJAX Load More Posts
 */
function cinemahub_load_more_posts() {
    check_ajax_referer('cinemahub_nonce', 'nonce');

    $page = intval($_POST['page']);
    $post_type = sanitize_text_field($_POST['post_type']);

    $query = new WP_Query(array(
        'post_type' => $post_type,
        'posts_per_page' => 8,
        'paged' => $page,
        'post_status' => 'publish'
    ));

    if ($query->have_posts()) {
        ob_start();

        while ($query->have_posts()) : $query->the_post();
            // Include movie card template
            get_template_part('template-parts/movie-card');
        endwhile;

        $html = ob_get_clean();
        wp_reset_postdata();

        wp_send_json_success(array(
            'html' => $html,
            'has_more' => $page < $query->max_num_pages
        ));
    } else {
        wp_send_json_error();
    }
}
add_action('wp_ajax_load_more_posts', 'cinemahub_load_more_posts');
add_action('wp_ajax_nopriv_load_more_posts', 'cinemahub_load_more_posts');

/**
 * Update Post Views
 */
function cinemahub_update_post_views() {
    check_ajax_referer('cinemahub_nonce', 'nonce');

    $post_id = intval($_POST['post_id']);
    $views = get_post_meta($post_id, 'post_views', true);
    $views = $views ? $views + 1 : 1;

    update_post_meta($post_id, 'post_views', $views);

    wp_send_json_success();
}
add_action('wp_ajax_update_post_views', 'cinemahub_update_post_views');
add_action('wp_ajax_nopriv_update_post_views', 'cinemahub_update_post_views');
