<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html b:version='2' class='v2' expr:dir='data:blog.languageDirection' xmlns='http://www.w3.org/1999/xhtml' xmlns:b='http://www.google.com/2005/gml/b' xmlns:data='http://www.google.com/2005/gml/data' xmlns:expr='http://www.google.com/2005/gml/expr'>
<head>
    <meta charset='UTF-8'/>
    <meta content='width=device-width, initial-scale=1.0' name='viewport'/>
    
    <b:if cond='data:view.isHomepage'>
        <title>CinemaHub - سينما هاب | شاهد أحدث الأفلام والمسلسلات مجاناً</title>
    <b:else/>
        <title><data:view.title/> - CinemaHub سينما هاب</title>
    </b:if>
    
    <meta content='موقعك الأول لمشاهدة أحدث الأفلام والمسلسلات العربية والأجنبية بجودة عالية ومجاناً' name='description'/>
    <meta content='أفلام, مسلسلات, مشاهدة اونلاين, تحميل أفلام, أفلام عربية, مسلسلات تركية, CinemaHub' name='keywords'/>
    
    <!-- Open Graph Meta Tags -->
    <meta content='CinemaHub - سينما هاب' property='og:title'/>
    <meta content='موقعك الأول لمشاهدة أحدث الأفلام والمسلسلات' property='og:description'/>
    <meta content='website' property='og:type'/>
    <meta expr:content='data:blog.url' property='og:url'/>
    
    <!-- Favicon -->
    <link href='data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgcng9IjIwIiBmaWxsPSIjZTUwOTE0Ii8+PHRleHQgeD0iNTAiIHk9IjY1IiBmb250LXNpemU9IjYwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSI+8J+OrDwvdGV4dD48L3N2Zz4=' rel='icon'/>
    
    <!-- PWA Manifest -->
    <link href='data:application/json;base64,eyJuYW1lIjoiQ2luZW1hSHViIC0g2LPZitmG2YXYpyDZh9in2KgiLCJzaG9ydF9uYW1lIjoiQ2luZW1hSHViIiwiZGVzY3JpcHRpb24iOiLZhdmI2YLYudmDINin2YTYo9mI2YQg2YTZhdmI2KfZh9iv2Kkg2KPYrdiv2Ksg2KfZhNij2YHZhNin2YUg2YjYp9mE2YXYs9mE2LPZhNin2KoiLCJzdGFydF91cmwiOiIvIiwiZGlzcGxheSI6InN0YW5kYWxvbmUiLCJiYWNrZ3JvdW5kX2NvbG9yIjoiIzE0MTQxNCIsInRoZW1lX2NvbG9yIjoiI2U1MDkxNCJ9' rel='manifest'/>
    <meta content='#e50914' name='theme-color'/>
    <meta content='yes' name='apple-mobile-web-app-capable'/>
    <meta content='black-translucent' name='apple-mobile-web-app-status-bar-style'/>
    <meta content='CinemaHub' name='apple-mobile-web-app-title'/>
    
    <!-- External CSS -->
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'/>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'/>
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&amp;display=swap' rel='stylesheet'/>
    <link href='https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css' rel='stylesheet'/>
    <link href='https://unpkg.com/aos@2.3.1/dist/aos.css' rel='stylesheet'/>
    
    <b:skin><![CDATA[
    /* ===== CSS Variables ===== */
    :root {
        --primary-color: #e50914;
        --secondary-color: #221f1f;
        --dark-color: #141414;
        --light-color: #ffffff;
        --gray-color: #757575;
        --success-color: #28a745;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
        --info-color: #17a2b8;
        
        --gradient-primary: linear-gradient(135deg, #e50914 0%, #b20710 100%);
        --gradient-dark: linear-gradient(135deg, #141414 0%, #000000 100%);
        --gradient-overlay: linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.8) 100%);
        
        --font-family: 'Cairo', sans-serif;
        --font-size-base: 14px;
        --font-size-small: 12px;
        --font-size-large: 16px;
        --font-size-xl: 18px;
        --font-size-xxl: 20px;
        --border-radius: 6px;
        --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        --transition: all 0.3s ease;
        --container-max-width: 1200px;
        --spacing-small: 8px;
        --spacing-medium: 16px;
        --spacing-large: 24px;
    }

    /* ===== Base Styles ===== */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
    }

    html {
        scroll-behavior: smooth;
        font-size: 16px;
    }

    body {
        font-family: var(--font-family);
        background-color: var(--dark-color);
        color: var(--light-color);
        line-height: 1.5;
        overflow-x: hidden;
        font-size: var(--font-size-base);
        margin: 0;
        padding: 0;
        direction: rtl;
    }

    /* ===== Loading Screen ===== */
    .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--dark-color);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease;
    }

    .loading-spinner {
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 20px;
    }

    .loading-logo {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;
    }

    .loading-logo-icon {
        width: 80px;
        height: 80px;
        background: var(--gradient-primary);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        animation: loadingPulse 2s ease-in-out infinite;
        box-shadow: 0 8px 32px rgba(229, 9, 20, 0.4);
    }

    .loading-logo-icon .fa-film {
        font-size: 40px;
        color: var(--light-color);
        z-index: 2;
    }

    .loading-logo-icon .logo-play {
        position: absolute;
        top: -8px;
        right: -8px;
        font-size: 24px !important;
        color: #ffd700;
        animation: playPulse 1.5s ease-in-out infinite;
    }

    .loading-text {
        display: flex;
        flex-direction: column;
        align-items: center;
        line-height: 1.2;
    }

    .loading-text .brand-text {
        font-size: 2rem;
        font-weight: 700;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 5px;
    }

    .loading-text .brand-subtitle {
        font-size: 1rem;
        color: var(--gray-color);
        font-weight: 400;
    }

    .spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(229, 9, 20, 0.3);
        border-top: 3px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes loadingPulse {
        0%, 100% {
            transform: scale(1);
            box-shadow: 0 8px 32px rgba(229, 9, 20, 0.4);
        }
        50% {
            transform: scale(1.1);
            box-shadow: 0 12px 40px rgba(229, 9, 20, 0.6);
        }
    }

    @keyframes playPulse {
        0%, 100% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.2); opacity: 0.8; }
    }

    /* ===== Header Styles ===== */
    .header {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        background: rgba(20, 20, 20, 0.95);
        backdrop-filter: blur(10px);
        z-index: 1000;
        transition: var(--transition);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .header.scrolled {
        background: rgba(20, 20, 20, 0.98);
        box-shadow: var(--box-shadow);
    }

    .navbar {
        padding: 0.5rem 0;
    }

    .navbar-brand {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: var(--light-color) !important;
        transition: var(--transition);
    }

    .navbar-brand:hover {
        transform: scale(1.05);
        color: var(--light-color) !important;
    }

    .logo-container {
        display: flex;
        align-items: center;
        gap: 12px;
        animation: logoEntrance 1s ease-out;
    }

    .logo-icon {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 45px;
        height: 45px;
        background: var(--gradient-primary);
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
        animation: logoGlow 3s ease-in-out infinite alternate;
    }

    .logo-icon .fa-film {
        font-size: 20px;
        color: var(--light-color);
        z-index: 2;
    }

    .logo-play {
        position: absolute;
        top: -5px;
        right: -5px;
        font-size: 16px !important;
        color: #ffd700;
        animation: playPulse 2s ease-in-out infinite;
        z-index: 3;
    }

    .logo-text {
        display: flex;
        flex-direction: column;
        line-height: 1.2;
    }

    .brand-text {
        font-size: var(--font-size-xl);
        font-weight: 700;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin: 0;
        letter-spacing: -0.5px;
    }

    .brand-subtitle {
        font-size: var(--font-size-small);
        color: var(--gray-color);
        font-weight: 400;
        margin-top: -2px;
    }

    /* ===== Navigation ===== */
    .navbar-nav .nav-link {
        color: var(--light-color) !important;
        font-weight: 500;
        margin: 0 6px;
        padding: 6px 12px !important;
        border-radius: var(--border-radius);
        transition: var(--transition);
        position: relative;
        font-size: var(--font-size-base);
    }

    .navbar-nav .nav-link:hover {
        background: rgba(229, 9, 20, 0.1);
        color: var(--primary-color) !important;
    }

    .navbar-nav .nav-link.active {
        background: var(--primary-color);
        color: var(--light-color) !important;
    }

    .navbar-nav .nav-link i {
        margin-left: 6px;
        font-size: var(--font-size-small);
    }

    /* ===== Search Form ===== */
    .search-form {
        position: relative;
        margin-right: auto;
    }

    .search-input {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: var(--light-color);
        border-radius: 20px;
        padding: 8px 40px 8px 16px;
        width: 250px;
        transition: var(--transition);
        font-size: var(--font-size-small);
    }

    .search-input:focus {
        outline: none;
        border-color: var(--primary-color);
        background: rgba(255, 255, 255, 0.15);
        width: 300px;
    }

    .search-input::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }

    .search-btn {
        position: absolute;
        left: 8px;
        background: transparent;
        border: none;
        color: var(--light-color);
        padding: 6px;
        border-radius: 50%;
        transition: var(--transition);
        font-size: var(--font-size-small);
    }

    .search-btn:hover {
        background: var(--primary-color);
        color: var(--light-color);
    }

    /* ===== Animations ===== */
    @keyframes logoGlow {
        0% { box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3); }
        100% { box-shadow: 0 6px 25px rgba(229, 9, 20, 0.6); }
    }

    @keyframes logoEntrance {
        0% { opacity: 0; transform: translateY(-20px) scale(0.8); }
        50% { opacity: 0.7; transform: translateY(-5px) scale(1.1); }
        100% { opacity: 1; transform: translateY(0) scale(1); }
    }

    .navbar-brand:hover .logo-icon {
        transform: rotate(5deg);
    }

    .navbar-brand:hover .logo-play {
        animation-duration: 0.5s;
    }

    /* ===== Hero Section ===== */
    .hero-section {
        height: 50vh;
        position: relative;
        margin-top: 70px;
        overflow: hidden;
    }

    .hero-slider {
        width: 100%;
        height: 100%;
    }

    .hero-slide {
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        position: relative;
        display: flex;
        align-items: center;
    }

    .hero-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--gradient-overlay);
        z-index: 1;
    }

    .hero-content {
        position: relative;
        z-index: 2;
        color: var(--light-color);
        max-width: 500px;
        padding: 0 var(--spacing-large);
    }

    .hero-title {
        font-size: var(--font-size-xxl);
        font-weight: 600;
        margin-bottom: var(--spacing-medium);
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        line-height: 1.3;
    }

    .hero-description {
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-large);
        opacity: 0.9;
        line-height: 1.4;
    }

    .hero-buttons {
        display: flex;
        gap: var(--spacing-small);
        flex-wrap: wrap;
    }

    .btn-hero {
        padding: 8px 20px;
        border-radius: 20px;
        font-weight: 500;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 6px;
        transition: var(--transition);
        font-size: var(--font-size-small);
    }

    .btn-hero.primary {
        background: var(--primary-color);
        color: var(--light-color);
    }

    .btn-hero.secondary {
        background: rgba(255, 255, 255, 0.2);
        color: var(--light-color);
        backdrop-filter: blur(10px);
    }

    .btn-hero:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    }

    /* ===== Main Content ===== */
    .main-content {
        padding: var(--spacing-large) 0;
    }

    .content-section {
        margin-bottom: var(--spacing-large);
    }

    .container {
        max-width: var(--container-max-width);
        margin: 0 auto;
        padding: 0 var(--spacing-medium);
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-medium);
        padding-bottom: var(--spacing-small);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .section-title {
        font-size: var(--font-size-large);
        font-weight: 600;
        color: var(--light-color);
        display: flex;
        align-items: center;
        gap: var(--spacing-small);
    }

    .section-title i {
        color: var(--primary-color);
    }

    .view-all-btn {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 4px;
        transition: var(--transition);
        font-size: var(--font-size-small);
    }

    .view-all-btn:hover {
        color: #ff0a16;
        transform: translateX(-3px);
    }

    /* ===== Movies Grid ===== */
    .movies-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: var(--spacing-medium);
        padding: var(--spacing-medium) 0;
    }

    .movie-card {
        background: var(--secondary-color);
        border-radius: var(--border-radius);
        overflow: hidden;
        transition: var(--transition);
        cursor: pointer;
        position: relative;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .movie-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        border-color: var(--primary-color);
    }

    .movie-poster {
        position: relative;
        overflow: hidden;
    }

    .movie-poster img {
        width: 100%;
        height: 240px;
        object-fit: cover;
        transition: var(--transition);
    }

    .movie-card:hover .movie-poster img {
        transform: scale(1.05);
    }

    .movie-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: var(--transition);
    }

    .movie-card:hover .movie-overlay {
        opacity: 1;
    }

    .play-btn {
        background: var(--primary-color);
        color: var(--light-color);
        border: none;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        font-size: var(--font-size-base);
        display: flex;
        justify-content: center;
        align-items: center;
        transition: var(--transition);
    }

    .play-btn:hover {
        background: #ff0a16;
        transform: scale(1.1);
    }

    .movie-info {
        padding: var(--spacing-small);
    }

    .movie-title {
        font-size: var(--font-size-small);
        font-weight: 500;
        margin-bottom: 4px;
        color: var(--light-color);
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .movie-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 11px;
        color: var(--gray-color);
    }

    .movie-year {
        background: rgba(255, 255, 255, 0.1);
        padding: 1px 6px;
        border-radius: 8px;
        font-size: 10px;
    }

    .movie-rating {
        display: flex;
        align-items: center;
        gap: 2px;
    }

    .movie-rating i {
        color: #ffd700;
        font-size: 10px;
    }

    /* ===== Quality Badge ===== */
    .quality-badge {
        position: absolute;
        top: 6px;
        right: 6px;
        background: var(--primary-color);
        color: var(--light-color);
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 10px;
        font-weight: 500;
        z-index: 2;
    }

    /* ===== Blog Posts ===== */
    .blog-posts-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: var(--spacing-large);
        margin: var(--spacing-large) 0;
    }

    .post-card {
        background: var(--secondary-color);
        border-radius: var(--border-radius);
        overflow: hidden;
        transition: var(--transition);
        box-shadow: var(--box-shadow);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .post-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(229, 9, 20, 0.2);
        border-color: var(--primary-color);
    }

    .post-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
        transition: var(--transition);
    }

    .post-card:hover .post-image {
        transform: scale(1.05);
    }

    .post-placeholder {
        width: 100%;
        height: 200px;
        background: var(--gradient-primary);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--light-color);
        font-size: 3rem;
    }

    .post-content {
        padding: var(--spacing-medium);
    }

    .post-title {
        font-size: var(--font-size-large);
        font-weight: 600;
        margin-bottom: var(--spacing-small);
        color: var(--light-color);
        line-height: 1.4;
    }

    .post-title a {
        color: inherit;
        text-decoration: none;
        transition: var(--transition);
    }

    .post-title a:hover {
        color: var(--primary-color);
    }

    .post-meta {
        font-size: var(--font-size-small);
        color: var(--gray-color);
        margin-bottom: var(--spacing-small);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .post-meta i {
        color: var(--primary-color);
    }

    .post-snippet {
        font-size: var(--font-size-small);
        color: var(--gray-color);
        line-height: 1.6;
        margin-bottom: var(--spacing-small);
    }

    .read-more-btn {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 4px;
        transition: var(--transition);
        font-size: var(--font-size-small);
    }

    .read-more-btn:hover {
        color: #ff0a16;
        transform: translateX(-3px);
    }

    /* ===== Footer ===== */
    .footer {
        background: var(--gradient-dark);
        padding: var(--spacing-large) 0 var(--spacing-medium);
        margin-top: var(--spacing-large);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .footer-widget {
        margin-bottom: var(--spacing-medium);
    }

    .footer-widget h5,
    .footer-widget h6 {
        color: var(--light-color);
        margin-bottom: var(--spacing-small);
        font-weight: 500;
        font-size: var(--font-size-base);
    }

    .footer-widget p {
        color: var(--gray-color);
        line-height: 1.5;
        font-size: var(--font-size-small);
    }

    .footer-logo {
        margin-bottom: var(--spacing-medium);
    }

    .footer-logo-icon {
        background: rgba(229, 9, 20, 0.1);
        border: 1px solid rgba(229, 9, 20, 0.3);
        animation: none;
    }

    .footer-logo-icon:hover {
        background: var(--gradient-primary);
        border-color: var(--primary-color);
        animation: logoGlow 2s ease-in-out infinite alternate;
    }

    .footer-links {
        list-style: none;
        padding: 0;
    }

    .footer-links li {
        margin-bottom: 8px;
    }

    .footer-links a {
        color: var(--gray-color);
        text-decoration: none;
        font-size: var(--font-size-small);
        transition: var(--transition);
    }

    .footer-links a:hover {
        color: var(--primary-color);
        padding-right: 5px;
    }

    .social-links {
        display: flex;
        gap: 10px;
        margin-top: var(--spacing-small);
    }

    .social-links a {
        width: 35px;
        height: 35px;
        background: rgba(255, 255, 255, 0.1);
        color: var(--light-color);
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: var(--transition);
        text-decoration: none;
    }

    .social-links a:hover {
        background: var(--primary-color);
        transform: translateY(-2px);
    }

    .footer-bottom {
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        padding-top: var(--spacing-medium);
        margin-top: var(--spacing-medium);
    }

    .copyright {
        color: var(--gray-color);
        font-size: var(--font-size-small);
        margin: 0;
    }

    .footer-bottom-links {
        display: flex;
        gap: var(--spacing-medium);
        justify-content: flex-end;
    }

    .footer-bottom-links a {
        color: var(--gray-color);
        text-decoration: none;
        font-size: var(--font-size-small);
        transition: var(--transition);
    }

    .footer-bottom-links a:hover {
        color: var(--primary-color);
    }

    /* ===== Back to Top Button ===== */
    .back-to-top {
        position: fixed;
        bottom: 30px;
        left: 30px;
        width: 50px;
        height: 50px;
        background: var(--primary-color);
        color: var(--light-color);
        border: none;
        border-radius: 50%;
        cursor: pointer;
        opacity: 0;
        visibility: hidden;
        transition: var(--transition);
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
    }

    .back-to-top.show {
        opacity: 1;
        visibility: visible;
    }

    .back-to-top:hover {
        background: #ff0a16;
        transform: translateY(-3px);
    }

    /* ===== Font Size Control ===== */
    .font-size-control {
        position: fixed;
        bottom: 30px;
        right: 30px;
        display: flex;
        flex-direction: column;
        gap: 5px;
        z-index: 1000;
        opacity: 0.8;
        transition: var(--transition);
    }

    .font-size-control:hover {
        opacity: 1;
    }

    .font-size-btn {
        width: 40px;
        height: 40px;
        background: rgba(34, 31, 31, 0.9);
        color: var(--light-color);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        cursor: pointer;
        transition: var(--transition);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        position: relative;
    }

    .font-size-btn:hover {
        background: var(--primary-color);
        border-color: var(--primary-color);
        transform: scale(1.1);
    }

    .font-size-btn.active {
        background: var(--primary-color);
        border-color: var(--primary-color);
    }

    .font-size-btn i {
        font-size: 8px;
        margin-bottom: 2px;
    }

    .size-indicator {
        font-size: 10px;
        font-weight: bold;
        line-height: 1;
    }

    /* ===== Responsive Design ===== */
    @media (max-width: 768px) {
        :root {
            --font-size-base: 13px;
            --font-size-small: 11px;
            --font-size-large: 15px;
            --font-size-xl: 16px;
            --font-size-xxl: 18px;
            --spacing-small: 6px;
            --spacing-medium: 12px;
            --spacing-large: 18px;
        }

        .hero-section {
            height: 40vh;
            margin-top: 60px;
        }

        .hero-title {
            font-size: var(--font-size-xl);
        }

        .hero-description {
            font-size: var(--font-size-small);
        }

        .search-input {
            width: 180px;
            padding: 6px 30px 6px 12px;
        }

        .movies-grid {
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: var(--spacing-small);
        }

        .section-header {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--spacing-small);
        }

        .section-title {
            font-size: var(--font-size-base);
        }

        .footer-bottom-links {
            flex-direction: column;
            gap: var(--spacing-small);
        }

        .container {
            padding: 0 var(--spacing-small);
        }

        .navbar {
            padding: 0.3rem 0;
        }

        .logo-container {
            gap: 8px;
        }

        .logo-icon {
            width: 35px;
            height: 35px;
            border-radius: 8px;
        }

        .logo-icon .fa-film {
            font-size: 16px;
        }

        .logo-play {
            font-size: 12px !important;
            top: -3px;
            right: -3px;
        }

        .brand-text {
            font-size: var(--font-size-base);
        }

        .brand-subtitle {
            font-size: 10px;
        }
    }

    @media (max-width: 576px) {
        :root {
            --font-size-base: 12px;
            --font-size-small: 10px;
            --font-size-large: 14px;
            --font-size-xl: 15px;
            --font-size-xxl: 16px;
            --spacing-small: 4px;
            --spacing-medium: 8px;
            --spacing-large: 12px;
        }

        .hero-content {
            padding: 0 var(--spacing-medium);
        }

        .hero-buttons {
            flex-direction: column;
            gap: var(--spacing-small);
        }

        .movies-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-small);
        }

        .search-input {
            width: 100%;
            font-size: 11px;
        }

        .movie-title {
            font-size: 11px;
            -webkit-line-clamp: 1;
            line-clamp: 1;
        }

        .movie-info {
            padding: 6px;
        }

        .quality-badge {
            font-size: 8px;
            padding: 1px 4px;
        }

        .logo-container {
            gap: 6px;
        }

        .logo-icon {
            width: 30px;
            height: 30px;
            border-radius: 6px;
        }

        .logo-icon .fa-film {
            font-size: 14px;
        }

        .logo-play {
            font-size: 10px !important;
            top: -2px;
            right: -2px;
        }

        .brand-text {
            font-size: 12px;
        }

        .brand-subtitle {
            display: none;
        }

        .font-size-control {
            bottom: 80px;
            right: 15px;
            gap: 3px;
        }

        .font-size-btn {
            width: 32px;
            height: 32px;
            font-size: 8px;
        }

        .font-size-btn i {
            font-size: 6px;
        }

        .size-indicator {
            font-size: 8px;
        }
    }

    @media (max-width: 320px) {
        .font-size-control {
            display: none;
        }
    }
    ]]></b:skin>
</head>

<body class='responsive-text'>
    <!-- Loading Screen -->
    <div class='loading-screen' id='loadingScreen'>
        <div class='loading-spinner'>
            <div class='loading-logo'>
                <div class='loading-logo-icon'>
                    <i class='fas fa-film'></i>
                    <i class='fas fa-play-circle logo-play'></i>
                </div>
                <div class='loading-text'>
                    <span class='brand-text'>CinemaHub</span>
                    <span class='brand-subtitle'>سينما هاب</span>
                </div>
            </div>
            <div class='spinner'></div>
            <p>جاري تحميل أحدث الأفلام والمسلسلات...</p>
        </div>
    </div>

    <!-- Header -->
    <header class='header' id='header'>
        <nav class='navbar navbar-expand-lg'>
            <div class='container'>
                <a class='navbar-brand' expr:href='data:blog.homepageUrl'>
                    <div class='logo-container'>
                        <div class='logo-icon'>
                            <i class='fas fa-film'></i>
                            <i class='fas fa-play-circle logo-play'></i>
                        </div>
                        <div class='logo-text'>
                            <span class='brand-text'>CinemaHub</span>
                            <span class='brand-subtitle'>سينما هاب</span>
                        </div>
                    </div>
                </a>

                <button class='navbar-toggler' data-bs-target='#navbarNav' data-bs-toggle='collapse' type='button'>
                    <span class='navbar-toggler-icon'></span>
                </button>

                <div class='collapse navbar-collapse' id='navbarNav'>
                    <ul class='navbar-nav me-auto'>
                        <li class='nav-item'>
                            <a class='nav-link active' expr:href='data:blog.homepageUrl'>
                                <i class='fas fa-home'></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class='nav-item'>
                            <a class='nav-link' href='#movies'>
                                <i class='fas fa-film'></i>
                                أفلام
                            </a>
                        </li>
                        <li class='nav-item'>
                            <a class='nav-link' href='#series'>
                                <i class='fas fa-tv'></i>
                                مسلسلات
                            </a>
                        </li>
                        <li class='nav-item'>
                            <a class='nav-link' href='#top-rated'>
                                <i class='fas fa-star'></i>
                                الأعلى تقييماً
                            </a>
                        </li>
                    </ul>

                    <form class='search-form d-flex'>
                        <input class='search-input' placeholder='ابحث عن فيلم أو مسلسل...' type='search'/>
                        <button class='search-btn' type='submit'>
                            <i class='fas fa-search'></i>
                        </button>
                    </form>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class='hero-section'>
        <div class='swiper hero-slider'>
            <div class='swiper-wrapper' id='heroSlides'>
                <!-- Hero slides will be populated by JavaScript -->
                <div class='swiper-slide hero-slide' style='background-image: url(https://image.tmdb.org/t/p/w1280/s16H6tpK2utvwDtzZ8Qy4qm5Emw.jpg)'>
                    <div class='hero-overlay'></div>
                    <div class='container'>
                        <div class='hero-content' data-aos='fade-up'>
                            <h1 class='hero-title'>أفاتار: طريق الماء</h1>
                            <p class='hero-description'>بعد أكثر من عقد من الأحداث الأولى، تعلم عائلة سولي المشاكل التي تتبعهم والأطوال التي سيذهبون إليها للحفاظ على سلامة بعضهم البعض.</p>
                            <div class='hero-buttons'>
                                <a class='btn-hero primary' href='#'>
                                    <i class='fas fa-play'></i>
                                    مشاهدة الآن
                                </a>
                                <a class='btn-hero secondary' href='#'>
                                    <i class='fas fa-info-circle'></i>
                                    المزيد من المعلومات
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class='swiper-pagination'></div>
            <div class='swiper-button-next'></div>
            <div class='swiper-button-prev'></div>
        </div>
    </section>

    <!-- Main Content -->
    <main class='main-content'>
        <div class='container'>
            <!-- Latest Movies Section -->
            <section class='content-section' id='movies'>
                <div class='section-header' data-aos='fade-up'>
                    <h2 class='section-title'>
                        <i class='fas fa-film'></i>
                        أحدث الأفلام
                    </h2>
                    <a class='view-all-btn' href='#'>
                        عرض الكل
                        <i class='fas fa-arrow-left'></i>
                    </a>
                </div>
                <div class='movies-grid' id='latest-movies'>
                    <!-- Movies will be populated by JavaScript -->
                </div>
            </section>

            <!-- Latest Series Section -->
            <section class='content-section' id='series'>
                <div class='section-header' data-aos='fade-up'>
                    <h2 class='section-title'>
                        <i class='fas fa-tv'></i>
                        أحدث المسلسلات
                    </h2>
                    <a class='view-all-btn' href='#'>
                        عرض الكل
                        <i class='fas fa-arrow-left'></i>
                    </a>
                </div>
                <div class='movies-grid' id='latest-series'>
                    <!-- Series will be populated by JavaScript -->
                </div>
            </section>

            <!-- Top Rated Section -->
            <section class='content-section' id='top-rated'>
                <div class='section-header' data-aos='fade-up'>
                    <h2 class='section-title'>
                        <i class='fas fa-star'></i>
                        الأعلى تقييماً
                    </h2>
                    <a class='view-all-btn' href='#'>
                        عرض الكل
                        <i class='fas fa-arrow-left'></i>
                    </a>
                </div>
                <div class='movies-grid' id='top-rated'>
                    <!-- Top rated content will be populated by JavaScript -->
                </div>
            </section>

            <!-- Blog Posts Section -->
            <b:if cond='data:view.isHomepage'>
                <section class='content-section'>
                    <div class='section-header' data-aos='fade-up'>
                        <h2 class='section-title'>
                            <i class='fas fa-newspaper'></i>
                            أحدث المقالات
                        </h2>
                    </div>
                    <b:section class='main' id='main' maxwidgets='1' showaddelement='no'>
                        <b:widget id='Blog1' locked='true' title='مشاركات المدونة' type='Blog' version='1' visible='true'>
                            <b:includable id='main'>
                                <div class='blog-posts-grid'>
                                    <b:loop values='data:posts' var='post'>
                                        <b:if cond='data:post.isFirstPost'>
                                            <article class='post-card featured-post' data-aos='fade-up'>
                                                <b:if cond='data:post.featuredImage'>
                                                    <img class='post-image' expr:alt='data:post.title' expr:src='data:post.featuredImage'/>
                                                <b:else/>
                                                    <div class='post-placeholder'>
                                                        <i class='fas fa-film'></i>
                                                    </div>
                                                </b:if>

                                                <div class='post-content'>
                                                    <h3 class='post-title'>
                                                        <a expr:href='data:post.link' expr:title='data:post.title'>
                                                            <data:post.title/>
                                                        </a>
                                                    </h3>
                                                    <div class='post-meta'>
                                                        <i class='fas fa-calendar'></i> <data:post.dateHeader/>
                                                        | <i class='fas fa-user'></i> <data:post.author/>
                                                        | <i class='fas fa-eye'></i> مشاهدة
                                                    </div>
                                                    <div class='post-snippet'>
                                                        <data:post.snippet/>
                                                    </div>
                                                    <a class='read-more-btn' expr:href='data:post.link'>
                                                        اقرأ المزيد
                                                        <i class='fas fa-arrow-left'></i>
                                                    </a>
                                                </div>
                                            </article>
                                        </b:if>
                                    </b:loop>
                                </div>
                            </b:includable>
                        </b:widget>
                    </b:section>
                </section>
            </b:if>
        </div>
    </main>

    <!-- Footer -->
    <footer class='footer'>
        <div class='container'>
            <div class='row'>
                <div class='col-lg-4 col-md-6'>
                    <div class='footer-widget'>
                        <div class='footer-logo'>
                            <div class='logo-container'>
                                <div class='logo-icon footer-logo-icon'>
                                    <i class='fas fa-film'></i>
                                    <i class='fas fa-play-circle logo-play'></i>
                                </div>
                                <div class='logo-text'>
                                    <span class='brand-text'>CinemaHub</span>
                                    <span class='brand-subtitle'>سينما هاب</span>
                                </div>
                            </div>
                        </div>
                        <p>موقعك الأول لمشاهدة أحدث الأفلام والمسلسلات العربية والأجنبية بجودة عالية ومجاناً</p>
                        <div class='social-links'>
                            <a href='#'><i class='fab fa-facebook'></i></a>
                            <a href='#'><i class='fab fa-twitter'></i></a>
                            <a href='#'><i class='fab fa-instagram'></i></a>
                            <a href='#'><i class='fab fa-youtube'></i></a>
                        </div>
                    </div>
                </div>
                <div class='col-lg-2 col-md-6'>
                    <div class='footer-widget'>
                        <h6>روابط سريعة</h6>
                        <ul class='footer-links'>
                            <li><a href='#'>الرئيسية</a></li>
                            <li><a href='#movies'>أفلام</a></li>
                            <li><a href='#series'>مسلسلات</a></li>
                            <li><a href='#top-rated'>الأعلى تقييماً</a></li>
                        </ul>
                    </div>
                </div>
                <div class='col-lg-2 col-md-6'>
                    <div class='footer-widget'>
                        <h6>الأقسام</h6>
                        <ul class='footer-links'>
                            <li><a href='#'>أفلام عربية</a></li>
                            <li><a href='#'>أفلام أجنبية</a></li>
                            <li><a href='#'>مسلسلات تركية</a></li>
                            <li><a href='#'>مسلسلات كورية</a></li>
                        </ul>
                    </div>
                </div>
                <div class='col-lg-2 col-md-6'>
                    <div class='footer-widget'>
                        <h6>الجودة</h6>
                        <ul class='footer-links'>
                            <li><a href='#'>HD</a></li>
                            <li><a href='#'>Full HD</a></li>
                            <li><a href='#'>4K</a></li>
                            <li><a href='#'>BluRay</a></li>
                        </ul>
                    </div>
                </div>
                <div class='col-lg-2 col-md-6'>
                    <div class='footer-widget'>
                        <h6>المساعدة</h6>
                        <ul class='footer-links'>
                            <li><a href='#'>اتصل بنا</a></li>
                            <li><a href='#'>الأسئلة الشائعة</a></li>
                            <li><a href='#'>سياسة الخصوصية</a></li>
                            <li><a href='#'>شروط الاستخدام</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class='footer-bottom'>
                <div class='row align-items-center'>
                    <div class='col-md-6'>
                        <p class='copyright'>© 2024 CinemaHub - سينما هاب. جميع الحقوق محفوظة.</p>
                    </div>
                    <div class='col-md-6'>
                        <div class='footer-bottom-links'>
                            <a href='#'>سياسة الخصوصية</a>
                            <a href='#'>شروط الاستخدام</a>
                            <a href='#'>اتصل بنا</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class='back-to-top' id='backToTop'>
        <i class='fas fa-arrow-up'></i>
    </button>

    <!-- Font Size Control -->
    <div class='font-size-control' id='fontSizeControl'>
        <button class='font-size-btn' onclick='changeFontSize("small")' title='خط صغير'>
            <i class='fas fa-font'></i>
            <span class='size-indicator'>ص</span>
        </button>
        <button class='font-size-btn' onclick='changeFontSize("medium")' title='خط متوسط'>
            <i class='fas fa-font'></i>
            <span class='size-indicator'>م</span>
        </button>
        <button class='font-size-btn active' onclick='changeFontSize("large")' title='خط كبير'>
            <i class='fas fa-font'></i>
            <span class='size-indicator'>ك</span>
        </button>
    </div>

    <!-- Scripts -->
    <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
    <script src='https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js'></script>
    <script src='https://unpkg.com/aos@2.3.1/dist/aos.js'></script>

    <script>
    //<![CDATA[
    // ===== Demo Content =====
    const demoMovies = [
        {
            id: 1,
            title: 'أفاتار: طريق الماء',
            poster_path: '/s16H6tpK2utvwDtzZ8Qy4qm5Emw.jpg',
            release_date: '2022-12-16',
            vote_average: 7.7
        },
        {
            id: 2,
            title: 'الرجل العنكبوت: لا طريق للعودة',
            poster_path: '/1g0dhYtq4irTY1GPXvft6k4YLjm.jpg',
            release_date: '2021-12-17',
            vote_average: 8.4
        },
        {
            id: 3,
            title: 'توب غان: مافريك',
            poster_path: '/62HCnUTziyWcpDaBO2i1DX17ljH.jpg',
            release_date: '2022-05-27',
            vote_average: 8.3
        },
        {
            id: 4,
            title: 'دكتور سترينج في الكون المتعدد',
            poster_path: '/9Gtg2DzBhmYamXBS1hKAhiwbBKS.jpg',
            release_date: '2022-05-06',
            vote_average: 7.3
        }
    ];

    const demoSeries = [
        {
            id: 1,
            name: 'بيت التنين',
            poster_path: '/z2yahl2uefxDCl0nogcRBstwruJ.jpg',
            first_air_date: '2022-08-21',
            vote_average: 8.5
        },
        {
            id: 2,
            name: 'حلقات القوة',
            poster_path: '/mYLOqiStMxDK3fYZFirgrMt8z5d.jpg',
            first_air_date: '2022-09-02',
            vote_average: 7.3
        },
        {
            id: 3,
            name: 'أشياء غريبة',
            poster_path: '/49WJfeN0moxb9IPfGn8AIqMGskD.jpg',
            first_air_date: '2016-07-15',
            vote_average: 8.7
        },
        {
            id: 4,
            name: 'الدب',
            poster_path: '/zPIug5giU8oug6Xes5K1sTfQJxY.jpg',
            first_air_date: '2022-06-23',
            vote_average: 8.3
        }
    ];

    // ===== Initialize App =====
    document.addEventListener('DOMContentLoaded', function() {
        initializeApp();
    });

    function initializeApp() {
        // Hide loading screen
        setTimeout(() => {
            const loadingScreen = document.getElementById('loadingScreen');
            if (loadingScreen) {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 500);
            }
        }, 1500);

        // Initialize components
        initializeHeader();
        initializeHeroSlider();
        initializeBackToTop();
        initializeAOS();
        initializeFontSizeControl();

        // Load content
        loadMovies();
        loadSeries();
        loadTopRated();
    }

    // ===== Header Functions =====
    function initializeHeader() {
        const header = document.getElementById('header');

        window.addEventListener('scroll', function() {
            if (window.scrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });
    }

    // ===== Hero Slider =====
    function initializeHeroSlider() {
        if (typeof Swiper !== 'undefined') {
            new Swiper('.hero-slider', {
                loop: true,
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                effect: 'fade',
                fadeEffect: {
                    crossFade: true
                }
            });
        }
    }

    // ===== Back to Top =====
    function initializeBackToTop() {
        const backToTopBtn = document.getElementById('backToTop');

        window.addEventListener('scroll', function() {
            if (window.scrollY > 300) {
                backToTopBtn.classList.add('show');
            } else {
                backToTopBtn.classList.remove('show');
            }
        });

        backToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // ===== AOS Animation =====
    function initializeAOS() {
        if (typeof AOS !== 'undefined') {
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true,
                offset: 100
            });
        }
    }

    // ===== Font Size Control =====
    function initializeFontSizeControl() {
        const savedSize = localStorage.getItem('fontSize') || 'medium';
        applyFontSize(savedSize);
        updateFontSizeButtons(savedSize);
    }

    function changeFontSize(size) {
        applyFontSize(size);
        updateFontSizeButtons(size);
        localStorage.setItem('fontSize', size);
    }

    function applyFontSize(size) {
        const body = document.body;
        body.classList.remove('font-small', 'font-medium', 'font-large');
        body.classList.add('font-' + size);
    }

    function updateFontSizeButtons(activeSize) {
        const buttons = document.querySelectorAll('.font-size-btn');
        buttons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.onclick && btn.onclick.toString().includes(activeSize)) {
                btn.classList.add('active');
            }
        });
    }

    // ===== Content Loading =====
    function loadMovies() {
        const container = document.getElementById('latest-movies');
        if (container) {
            container.innerHTML = createMoviesHTML(demoMovies);
        }
    }

    function loadSeries() {
        const container = document.getElementById('latest-series');
        if (container) {
            container.innerHTML = createSeriesHTML(demoSeries);
        }
    }

    function loadTopRated() {
        const container = document.getElementById('top-rated');
        if (container) {
            const topRated = [...demoMovies, ...demoSeries].sort((a, b) => (b.vote_average || 0) - (a.vote_average || 0));
            container.innerHTML = createMixedHTML(topRated);
        }
    }

    function createMoviesHTML(movies) {
        return movies.map(movie => `
            <div class="movie-card" data-aos="fade-up">
                <div class="movie-poster">
                    <img src="https://image.tmdb.org/t/p/w500${movie.poster_path}" alt="${movie.title}" loading="lazy">
                    <div class="movie-overlay">
                        <button class="play-btn">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                    <div class="quality-badge">HD</div>
                </div>
                <div class="movie-info">
                    <h3 class="movie-title">${movie.title}</h3>
                    <div class="movie-meta">
                        <span class="movie-year">${new Date(movie.release_date).getFullYear()}</span>
                        <div class="movie-rating">
                            <i class="fas fa-star"></i>
                            <span>${movie.vote_average}</span>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    function createSeriesHTML(series) {
        return series.map(show => `
            <div class="movie-card" data-aos="fade-up">
                <div class="movie-poster">
                    <img src="https://image.tmdb.org/t/p/w500${show.poster_path}" alt="${show.name}" loading="lazy">
                    <div class="movie-overlay">
                        <button class="play-btn">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                    <div class="quality-badge">مسلسل</div>
                </div>
                <div class="movie-info">
                    <h3 class="movie-title">${show.name}</h3>
                    <div class="movie-meta">
                        <span class="movie-year">${new Date(show.first_air_date).getFullYear()}</span>
                        <div class="movie-rating">
                            <i class="fas fa-star"></i>
                            <span>${show.vote_average}</span>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    function createMixedHTML(content) {
        return content.map(item => `
            <div class="movie-card" data-aos="fade-up">
                <div class="movie-poster">
                    <img src="https://image.tmdb.org/t/p/w500${item.poster_path}" alt="${item.title || item.name}" loading="lazy">
                    <div class="movie-overlay">
                        <button class="play-btn">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                    <div class="quality-badge">${item.title ? 'HD' : 'مسلسل'}</div>
                </div>
                <div class="movie-info">
                    <h3 class="movie-title">${item.title || item.name}</h3>
                    <div class="movie-meta">
                        <span class="movie-year">${new Date(item.release_date || item.first_air_date).getFullYear()}</span>
                        <div class="movie-rating">
                            <i class="fas fa-star"></i>
                            <span>${item.vote_average}</span>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    // ===== Search Functionality =====
    const searchForm = document.querySelector('.search-form');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const query = document.querySelector('.search-input').value.trim();
            if (query) {
                console.log('Searching for:', query);
                // Implement search functionality here
            }
        });
    }

    // ===== Smooth Scrolling =====
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    //]]>
    </script>
</body>
</html>
