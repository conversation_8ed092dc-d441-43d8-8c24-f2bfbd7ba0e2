# Assets Directory

هذا المجلد يحتوي على جميع الأصول المطلوبة للبرنامج.

## الملفات المطلوبة

### الأيقونات
- `icon.png` - أيقونة عامة (512x512 px)
- `icon.ico` - أيقونة Windows (متعددة الأحجام)
- `icon.icns` - أيقونة macOS (متعددة الأحجام)

### الصور
- `logo.png` - شعار البرنامج
- `splash.png` - صورة شاشة التحميل

## إنشاء الأيقونات

يمكنك استخدام أي أداة لإنشاء الأيقونات، مثل:
- Adobe Illustrator
- Figma
- Canva
- أدوات مجانية أونلاين

## المواصفات المطلوبة

### icon.png
- الحجم: 512x512 بكسل
- التنسيق: PNG
- الشفافية: مدعومة
- الجودة: عالية

### icon.ico (Windows)
- الأحجام: 16x16, 32x32, 48x48, 64x64, 128x128, 256x256
- التنسيق: ICO
- الشفافية: مدعومة

### icon.icns (macOS)
- الأحجام: متعددة (16x16 إلى 1024x1024)
- التنسيق: ICNS
- الشفافية: مدعومة

## أدوات التحويل

### تحويل PNG إلى ICO
```bash
# باستخدام ImageMagick
convert icon.png -resize 256x256 icon.ico
```

### تحويل PNG إلى ICNS
```bash
# على macOS
mkdir icon.iconset
sips -z 16 16 icon.png --out icon.iconset/icon_16x16.png
sips -z 32 32 icon.png --out icon.iconset/<EMAIL>
sips -z 32 32 icon.png --out icon.iconset/icon_32x32.png
sips -z 64 64 icon.png --out icon.iconset/<EMAIL>
sips -z 128 128 icon.png --out icon.iconset/icon_128x128.png
sips -z 256 256 icon.png --out icon.iconset/<EMAIL>
sips -z 256 256 icon.png --out icon.iconset/icon_256x256.png
sips -z 512 512 icon.png --out icon.iconset/<EMAIL>
sips -z 512 512 icon.png --out icon.iconset/icon_512x512.png
sips -z 1024 1024 icon.png --out icon.iconset/<EMAIL>
iconutil -c icns icon.iconset
```

## ملاحظات

- تأكد من أن الأيقونات واضحة في جميع الأحجام
- استخدم ألوان متناسقة مع هوية البرنامج
- تجنب التفاصيل الدقيقة في الأحجام الصغيرة
- اختبر الأيقونات على خلفيات مختلفة
