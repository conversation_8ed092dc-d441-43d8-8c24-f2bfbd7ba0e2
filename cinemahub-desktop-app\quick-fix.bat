@echo off
title CinemaHub Pro - Quick Fix

echo ========================================
echo   CinemaHub Pro - Quick Fix
echo ========================================
echo.

echo Fixing common issues...
echo.

REM Fix 1: Clean old build files
echo 1. Cleaning old build files...
if exist "dist" (
    rmdir /s /q dist 2>nul
    echo   Old dist folder removed
)
mkdir dist 2>nul
echo   Fresh dist folder created

echo.

REM Fix 2: Update package.json
echo 2. Updating package.json...
if exist "package.json.backup" del "package.json.backup"
if exist "package.json" copy "package.json" "package.json.backup" >nul

REM Create a working package.json
(
echo {
echo   "name": "cinemahub-pro-desktop",
echo   "version": "1.0.0",
echo   "description": "CinemaHub Pro Desktop Application",
echo   "main": "main.js",
echo   "homepage": "./",
echo   "scripts": {
echo     "start": "electron .",
echo     "dev": "electron . --dev",
echo     "build": "electron-builder",
echo     "build-win": "electron-builder --win",
echo     "pack": "electron-builder --dir"
echo   },
echo   "devDependencies": {
echo     "electron": "^27.0.0",
echo     "electron-builder": "^24.6.4"
echo   },
echo   "dependencies": {
echo     "axios": "^1.5.0",
echo     "sqlite3": "^5.1.6",
echo     "fs-extra": "^11.1.1",
echo     "chokidar": "^3.5.3"
echo   },
echo   "build": {
echo     "appId": "com.cinemahub.desktop",
echo     "productName": "CinemaHub Pro Desktop",
echo     "directories": {
echo       "output": "dist"
echo     },
echo     "files": [
echo       "main.js",
echo       "src/**/*",
echo       "package.json",
echo       "!node_modules/**/*",
echo       "!dist/**/*",
echo       "!*.bat",
echo       "!*.md"
echo     ],
echo     "extraFiles": [
echo       {
echo         "from": "assets/icon.png",
echo         "to": "icon.png"
echo       }
echo     ],
echo     "win": {
echo       "target": {
echo         "target": "nsis",
echo         "arch": ["x64"]
echo       },
echo       "icon": "assets/icon.png",
echo       "artifactName": "${productName}-Setup-${version}.${ext}"
echo     },
echo     "nsis": {
echo       "oneClick": false,
echo       "allowToChangeInstallationDirectory": true,
echo       "createDesktopShortcut": true,
echo       "createStartMenuShortcut": true,
echo       "installerIcon": "assets/icon.png",
echo       "uninstallerIcon": "assets/icon.png"
echo     }
echo   }
echo }
) > package.json

echo   package.json updated

echo.

REM Fix 3: Install/update dependencies
echo 3. Installing dependencies...
echo This may take a few minutes...
echo.

npm install
if %errorlevel% neq 0 (
    echo   Installation failed, trying alternative...
    npm install --legacy-peer-deps
    if %errorlevel% neq 0 (
        echo   ERROR: Could not install dependencies
        echo   Check internet connection and try again
        pause
        exit /b 1
    )
)

echo   Dependencies installed successfully

echo.

REM Fix 4: Test electron
echo 4. Testing Electron...
npx electron --version >nul 2>&1
if %errorlevel% equ 0 (
    echo   Electron is working
) else (
    echo   Installing Electron globally...
    npm install -g electron
)

echo.

echo ========================================
echo           FIXES APPLIED!
echo ========================================
echo.
echo Your system is now ready for building.
echo.
echo Next step: Run fix-and-build.bat
echo.
echo Press any key to continue...
pause >nul
