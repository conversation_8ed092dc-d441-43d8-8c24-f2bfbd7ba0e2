# 🚀 دليل تشغيل الموقع على بلوجر

## الطريقة الأولى: استخدام القالب الجاهز (الأسهل)

### خطوات التطبيق:

#### 1. تحضير القالب:
- استخدم ملف `blogger-template.xml` المرفق
- هذا القالب يحتوي على التصميم كاملاً

#### 2. رفع القالب لبلوجر:
1. اذهب إلى [blogger.com](https://blogger.com)
2. أنشئ مدونة جديدة أو استخدم موجودة
3. اذهب إلى **Theme** (المظهر)
4. انقر على **Customize** ثم **Edit HTML**
5. احذف الكود الموجود بالكامل
6. انسخ محتوى ملف `blogger-template.xml`
7. الصق الكود في المحرر
8. انقر **Save** (حفظ)

#### 3. إضافة المحتوى:
- اذهب إلى **Posts** (المشاركات)
- أنشئ مشاركات جديدة للأفلام والمسلسلات
- استخدم الصور والأوصاف

---

## الطريقة الثانية: دمج الكود في قالب موجود

### 1. رفع ملفات CSS و JS:

#### أ) استخدام GitHub Pages (مجاني):
```bash
# 1. أنشئ حساب GitHub
# 2. أنشئ repository جديد باسم: cinema-hub-files
# 3. ارفع هذه الملفات:
├── css/
│   └── style.css
├── js/
│   ├── main.js
│   ├── content-manager.js
│   ├── movie-modal.js
│   ├── pwa-manager.js
│   └── error-handler.js
└── config.js

# 4. فعل GitHub Pages من Settings
# 5. احصل على الرابط: https://username.github.io/cinema-hub-files/
```

#### ب) استخدام jsDelivr CDN:
```html
<!-- بعد رفع الملفات لـ GitHub، استخدم هذه الروابط: -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/username/cinema-hub-files@main/css/style.css">
<script src="https://cdn.jsdelivr.net/gh/username/cinema-hub-files@main/js/main.js"></script>
```

### 2. تعديل قالب بلوجر:

#### أ) إضافة CSS في الـ Head:
```html
<!-- اذهب إلى Theme > Edit HTML -->
<!-- ابحث عن </head> وأضف قبلها: -->

<!-- External CSS -->
<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'/>
<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'/>
<link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&amp;display=swap' rel='stylesheet'/>

<!-- Custom CSS -->
<link href='https://username.github.io/cinema-hub-files/css/style.css' rel='stylesheet'/>
```

#### ب) إضافة JavaScript قبل </body>:
```html
<!-- ابحث عن </body> وأضف قبلها: -->

<!-- External JS -->
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
<script src='https://unpkg.com/aos@2.3.1/dist/aos.js'></script>

<!-- Custom JS -->
<script src='https://username.github.io/cinema-hub-files/config.js'></script>
<script src='https://username.github.io/cinema-hub-files/js/content-manager.js'></script>
<script src='https://username.github.io/cinema-hub-files/js/movie-modal.js'></script>
<script src='https://username.github.io/cinema-hub-files/js/main.js'></script>
```

#### ج) تعديل الهيدر:
```html
<!-- ابحث عن header أو navbar واستبدلها بـ: -->
<header class='header'>
    <nav class='navbar'>
        <div class='container'>
            <a class='navbar-brand' href='/'>
                <div class='logo-container'>
                    <div class='logo-icon'>
                        <i class='fas fa-film'></i>
                        <i class='fas fa-play-circle logo-play'></i>
                    </div>
                    <div class='logo-text'>
                        <span class='brand-text'>CinemaHub</span>
                        <span class='brand-subtitle'>سينما هاب</span>
                    </div>
                </div>
            </a>
        </div>
    </nav>
</header>
```

---

## الطريقة الثالثة: إنشاء صفحات ثابتة

### 1. إنشاء صفحة رئيسية:
1. اذهب إلى **Pages** (الصفحات)
2. انقر **New Page** (صفحة جديدة)
3. اختر **HTML View**
4. انسخ محتوى `index.html` (بدون head و body)
5. احفظ الصفحة

### 2. إنشاء صفحة البحث:
1. أنشئ صفحة جديدة باسم "البحث"
2. انسخ محتوى `search.html`
3. احفظ الصفحة

### 3. ربط الصفحات:
- اذهب إلى **Layout** (التخطيط)
- أضف **Pages** widget
- رتب الصفحات في القائمة

---

## إعدادات مهمة لبلوجر:

### 1. إعدادات SEO:
```html
<!-- في قسم الـ Head، أضف: -->
<meta content='CinemaHub - سينما هاب | شاهد أحدث الأفلام والمسلسلات مجاناً' name='description'/>
<meta content='أفلام, مسلسلات, مشاهدة اونلاين, تحميل أفلام' name='keywords'/>
```

### 2. إعدادات الأداء:
```html
<!-- تحسين التحميل -->
<link rel='preconnect' href='https://fonts.googleapis.com'/>
<link rel='preconnect' href='https://cdn.jsdelivr.net'/>
<link rel='dns-prefetch' href='https://api.themoviedb.org'/>
```

### 3. إعدادات الأمان:
```html
<!-- Content Security Policy -->
<meta http-equiv='Content-Security-Policy' content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;"/>
```

---

## نصائح مهمة:

### ✅ الأشياء التي تعمل في بلوجر:
- CSS مخصص
- JavaScript أساسي
- Bootstrap و Font Awesome
- Google Fonts
- الصور الخارجية

### ❌ الأشياء التي لا تعمل:
- Service Workers
- Local Storage (محدود)
- ملفات محلية
- بعض APIs الحديثة

### 🔧 حلول بديلة:
- استخدم **Blogger Data API** بدلاً من TMDB
- استخدم **Google Drive** لتخزين الصور
- استخدم **Firebase** للبيانات الديناميكية

---

## خطوات سريعة للبدء:

### الطريقة السريعة (5 دقائق):
1. انسخ كود `blogger-template.xml`
2. اذهب إلى بلوجر > Theme > Edit HTML
3. الصق الكود
4. احفظ
5. أنشئ مشاركات للأفلام

### الطريقة المتقدمة (30 دقيقة):
1. أنشئ حساب GitHub
2. ارفع ملفات CSS/JS
3. عدل قالب بلوجر
4. أضف الروابط الخارجية
5. اختبر الموقع

---

## روابط مفيدة:
- [Blogger Help](https://support.google.com/blogger/)
- [GitHub Pages](https://pages.github.com/)
- [jsDelivr CDN](https://www.jsdelivr.com/)
- [Bootstrap Docs](https://getbootstrap.com/docs/)

**ملاحظة**: تأكد من اختبار الموقع على أجهزة مختلفة بعد التطبيق!
