const { app, BrowserWindow, Menu, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs-extra');
const chokidar = require('chokidar');
const express = require('express');
const Store = require('electron-store');
const { autoUpdater } = require('electron-updater');

// Initialize store for app settings
const store = new Store();

// Keep a global reference of the window object
let mainWindow;
let serverApp;
let server;

// Enable live reload for development
if (process.env.NODE_ENV === 'development') {
    require('electron-reload')(__dirname, {
        electron: path.join(__dirname, '..', 'node_modules', '.bin', 'electron'),
        hardResetMethod: 'exit'
    });
}

function createWindow() {
    // Create the browser window
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1200,
        minHeight: 800,
        icon: path.join(__dirname, 'assets', 'icon.png'),
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true,
            webSecurity: false
        },
        titleBarStyle: 'default',
        show: false,
        backgroundColor: '#0f172a'
    });

    // Load the app
    mainWindow.loadFile('src/index.html');

    // Show window when ready
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        // Focus on window
        if (process.platform === 'darwin') {
            mainWindow.focus();
        }
    });

    // Handle window closed
    mainWindow.on('closed', () => {
        mainWindow = null;
        if (server) {
            server.close();
        }
    });

    // Handle external links
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });

    // Development tools
    if (process.env.NODE_ENV === 'development') {
        mainWindow.webContents.openDevTools();
    }
}

// Create application menu
function createMenu() {
    const template = [
        {
            label: 'ملف',
            submenu: [
                {
                    label: 'مشروع جديد',
                    accelerator: 'CmdOrCtrl+N',
                    click: () => {
                        mainWindow.webContents.send('menu-new-project');
                    }
                },
                {
                    label: 'فتح مشروع',
                    accelerator: 'CmdOrCtrl+O',
                    click: async () => {
                        const result = await dialog.showOpenDialog(mainWindow, {
                            properties: ['openDirectory'],
                            title: 'اختر مجلد المشروع'
                        });
                        
                        if (!result.canceled) {
                            mainWindow.webContents.send('menu-open-project', result.filePaths[0]);
                        }
                    }
                },
                {
                    label: 'حفظ',
                    accelerator: 'CmdOrCtrl+S',
                    click: () => {
                        mainWindow.webContents.send('menu-save');
                    }
                },
                { type: 'separator' },
                {
                    label: 'إعدادات',
                    accelerator: 'CmdOrCtrl+,',
                    click: () => {
                        mainWindow.webContents.send('menu-settings');
                    }
                },
                { type: 'separator' },
                {
                    label: 'خروج',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'تحرير',
            submenu: [
                {
                    label: 'تراجع',
                    accelerator: 'CmdOrCtrl+Z',
                    role: 'undo'
                },
                {
                    label: 'إعادة',
                    accelerator: 'Shift+CmdOrCtrl+Z',
                    role: 'redo'
                },
                { type: 'separator' },
                {
                    label: 'قص',
                    accelerator: 'CmdOrCtrl+X',
                    role: 'cut'
                },
                {
                    label: 'نسخ',
                    accelerator: 'CmdOrCtrl+C',
                    role: 'copy'
                },
                {
                    label: 'لصق',
                    accelerator: 'CmdOrCtrl+V',
                    role: 'paste'
                }
            ]
        },
        {
            label: 'عرض',
            submenu: [
                {
                    label: 'إعادة تحميل',
                    accelerator: 'CmdOrCtrl+R',
                    click: () => {
                        mainWindow.reload();
                    }
                },
                {
                    label: 'تكبير/تصغير',
                    accelerator: 'F11',
                    click: () => {
                        mainWindow.setFullScreen(!mainWindow.isFullScreen());
                    }
                },
                { type: 'separator' },
                {
                    label: 'تكبير',
                    accelerator: 'CmdOrCtrl+Plus',
                    click: () => {
                        mainWindow.webContents.setZoomLevel(mainWindow.webContents.getZoomLevel() + 0.5);
                    }
                },
                {
                    label: 'تصغير',
                    accelerator: 'CmdOrCtrl+-',
                    click: () => {
                        mainWindow.webContents.setZoomLevel(mainWindow.webContents.getZoomLevel() - 0.5);
                    }
                },
                {
                    label: 'حجم طبيعي',
                    accelerator: 'CmdOrCtrl+0',
                    click: () => {
                        mainWindow.webContents.setZoomLevel(0);
                    }
                }
            ]
        },
        {
            label: 'أدوات',
            submenu: [
                {
                    label: 'معاينة الموقع',
                    accelerator: 'CmdOrCtrl+P',
                    click: () => {
                        mainWindow.webContents.send('menu-preview');
                    }
                },
                {
                    label: 'رفع للخادم',
                    accelerator: 'CmdOrCtrl+U',
                    click: () => {
                        mainWindow.webContents.send('menu-upload');
                    }
                },
                {
                    label: 'نسخ احتياطي',
                    click: () => {
                        mainWindow.webContents.send('menu-backup');
                    }
                },
                { type: 'separator' },
                {
                    label: 'أدوات المطور',
                    accelerator: 'F12',
                    click: () => {
                        mainWindow.webContents.toggleDevTools();
                    }
                }
            ]
        },
        {
            label: 'مساعدة',
            submenu: [
                {
                    label: 'دليل الاستخدام',
                    click: () => {
                        shell.openExternal('https://cinemahub-pro.com/docs');
                    }
                },
                {
                    label: 'الدعم الفني',
                    click: () => {
                        shell.openExternal('https://cinemahub-pro.com/support');
                    }
                },
                { type: 'separator' },
                {
                    label: 'التحقق من التحديثات',
                    click: () => {
                        autoUpdater.checkForUpdatesAndNotify();
                    }
                },
                {
                    label: 'حول البرنامج',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'حول CinemaHub Pro Desktop',
                            message: 'CinemaHub Pro Desktop',
                            detail: `الإصدار: ${app.getVersion()}\nبرنامج إدارة الأفلام والمسلسلات\nتم التطوير بواسطة فريق CinemaHub`
                        });
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// Start local server for preview
function startLocalServer() {
    serverApp = express();
    
    // Serve static files
    serverApp.use(express.static(path.join(__dirname, '../')));
    
    // API endpoints
    serverApp.get('/api/status', (req, res) => {
        res.json({ status: 'running', timestamp: new Date().toISOString() });
    });
    
    // Start server
    server = serverApp.listen(3000, () => {
        console.log('Local server running on http://localhost:3000');
    });
}

// Watch for file changes
function watchFiles() {
    const projectPath = store.get('currentProject');
    if (!projectPath) return;
    
    const watcher = chokidar.watch(projectPath, {
        ignored: /node_modules|\.git/,
        persistent: true
    });
    
    watcher.on('change', (filePath) => {
        console.log('File changed:', filePath);
        mainWindow.webContents.send('file-changed', filePath);
    });
    
    watcher.on('add', (filePath) => {
        console.log('File added:', filePath);
        mainWindow.webContents.send('file-added', filePath);
    });
    
    watcher.on('unlink', (filePath) => {
        console.log('File removed:', filePath);
        mainWindow.webContents.send('file-removed', filePath);
    });
}

// App event handlers
app.whenReady().then(() => {
    createWindow();
    createMenu();
    startLocalServer();
    watchFiles();
    
    // Auto updater
    autoUpdater.checkForUpdatesAndNotify();
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// IPC handlers
ipcMain.handle('get-app-version', () => {
    return app.getVersion();
});

ipcMain.handle('get-app-path', () => {
    return app.getAppPath();
});

ipcMain.handle('show-save-dialog', async (event, options) => {
    const result = await dialog.showSaveDialog(mainWindow, options);
    return result;
});

ipcMain.handle('show-open-dialog', async (event, options) => {
    const result = await dialog.showOpenDialog(mainWindow, options);
    return result;
});

ipcMain.handle('read-file', async (event, filePath) => {
    try {
        const content = await fs.readFile(filePath, 'utf8');
        return { success: true, content };
    } catch (error) {
        return { success: false, error: error.message };
    }
});

ipcMain.handle('write-file', async (event, filePath, content) => {
    try {
        await fs.writeFile(filePath, content, 'utf8');
        return { success: true };
    } catch (error) {
        return { success: false, error: error.message };
    }
});

ipcMain.handle('copy-file', async (event, src, dest) => {
    try {
        await fs.copy(src, dest);
        return { success: true };
    } catch (error) {
        return { success: false, error: error.message };
    }
});

ipcMain.handle('ensure-dir', async (event, dirPath) => {
    try {
        await fs.ensureDir(dirPath);
        return { success: true };
    } catch (error) {
        return { success: false, error: error.message };
    }
});

// Store handlers
ipcMain.handle('store-get', (event, key) => {
    return store.get(key);
});

ipcMain.handle('store-set', (event, key, value) => {
    store.set(key, value);
    return true;
});

ipcMain.handle('store-delete', (event, key) => {
    store.delete(key);
    return true;
});

// Auto updater events
autoUpdater.on('checking-for-update', () => {
    console.log('Checking for update...');
});

autoUpdater.on('update-available', (info) => {
    console.log('Update available.');
});

autoUpdater.on('update-not-available', (info) => {
    console.log('Update not available.');
});

autoUpdater.on('error', (err) => {
    console.log('Error in auto-updater. ' + err);
});

autoUpdater.on('download-progress', (progressObj) => {
    let log_message = "Download speed: " + progressObj.bytesPerSecond;
    log_message = log_message + ' - Downloaded ' + progressObj.percent + '%';
    log_message = log_message + ' (' + progressObj.transferred + "/" + progressObj.total + ')';
    console.log(log_message);
});

autoUpdater.on('update-downloaded', (info) => {
    console.log('Update downloaded');
    autoUpdater.quitAndInstall();
});
