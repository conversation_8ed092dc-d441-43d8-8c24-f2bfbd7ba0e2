// ===== Configuration File for Movie Website =====

// ===== Global Variables for Compatibility =====
const API_KEY = 'YOUR_TMDB_API_KEY'; // Replace with your actual TMDB API key
const BASE_URL = 'https://api.themoviedb.org/3';
const IMAGE_BASE_URL = 'https://image.tmdb.org/t/p/w500';
const BACKDROP_BASE_URL = 'https://image.tmdb.org/t/p/w1280';

const CONFIG = {
    // ===== API Configuration =====
    API: {
        TMDB_API_KEY: 'YOUR_TMDB_API_KEY_HERE', // Replace with your actual TMDB API key
        BASE_URL: 'https://api.themoviedb.org/3',
        IMAGE_BASE_URL: 'https://image.tmdb.org/t/p/w500',
        BACKDROP_BASE_URL: 'https://image.tmdb.org/t/p/w1280',
        ORIGINAL_IMAGE_URL: 'https://image.tmdb.org/t/p/original',
        LANGUAGE: 'ar', // Default language
        REGION: 'SA', // Default region (Saudi Arabia)
        ADULT_CONTENT: false // Include adult content in results
    },

    // ===== Site Configuration =====
    SITE: {
        NAME: 'موقع الأفلام',
        DESCRIPTION: 'موقعك الأول لمشاهدة أحدث الأفلام والمسلسلات العربية والأجنبية بجودة عالية ومجاناً',
        URL: 'https://yoursite.com',
        LOGO: 'assets/images/logo.png',
        FAVICON: 'assets/images/favicon.ico',
        DEFAULT_POSTER: 'assets/images/no-poster.jpg',
        DEFAULT_BACKDROP: 'assets/images/default-backdrop.jpg'
    },

    // ===== Content Configuration =====
    CONTENT: {
        ITEMS_PER_PAGE: 20,
        HERO_SLIDES_COUNT: 5,
        SIMILAR_ITEMS_COUNT: 4,
        LATEST_ITEMS_COUNT: 8,
        CACHE_DURATION: 30 * 60 * 1000, // 30 minutes in milliseconds
        MAX_SEARCH_SUGGESTIONS: 5,
        MAX_WATCHLIST_ITEMS: 100,
        MAX_HISTORY_ITEMS: 100
    },

    // ===== UI Configuration =====
    UI: {
        THEME: 'dark', // 'dark' or 'light'
        ANIMATION_DURATION: 300,
        LAZY_LOADING: true,
        INFINITE_SCROLL: false,
        AUTO_PLAY_TRAILERS: false,
        SHOW_ADULT_CONTENT_WARNING: true
    },

    // ===== Features Configuration =====
    FEATURES: {
        SEARCH: {
            ENABLED: true,
            ADVANCED_SEARCH: true,
            REAL_TIME_SEARCH: true,
            SEARCH_SUGGESTIONS: true,
            MIN_SEARCH_LENGTH: 3,
            SEARCH_DELAY: 500 // milliseconds
        },
        FAVORITES: {
            ENABLED: true,
            LOCAL_STORAGE: true,
            SYNC_WITH_SERVER: false
        },
        WATCH_HISTORY: {
            ENABLED: true,
            LOCAL_STORAGE: true,
            AUTO_TRACK: true,
            MAX_ITEMS: 100
        },
        RECOMMENDATIONS: {
            ENABLED: true,
            BASED_ON_FAVORITES: true,
            BASED_ON_HISTORY: true,
            BASED_ON_GENRES: true
        },
        NOTIFICATIONS: {
            ENABLED: true,
            PUSH_NOTIFICATIONS: false,
            NEW_CONTENT_ALERTS: true,
            FAVORITE_UPDATES: true
        },
        SOCIAL_SHARING: {
            ENABLED: true,
            PLATFORMS: ['facebook', 'twitter', 'whatsapp', 'telegram']
        },
        COMMENTS: {
            ENABLED: false, // Requires backend implementation
            MODERATION: true,
            GUEST_COMMENTS: false
        },
        RATINGS: {
            ENABLED: false, // Requires backend implementation
            USER_RATINGS: false,
            SHOW_TMDB_RATINGS: true
        }
    },

    // ===== Performance Configuration =====
    PERFORMANCE: {
        SERVICE_WORKER: true,
        CACHE_STATIC_FILES: true,
        CACHE_API_RESPONSES: true,
        CACHE_IMAGES: true,
        PRELOAD_CRITICAL_RESOURCES: true,
        LAZY_LOAD_IMAGES: true,
        COMPRESS_IMAGES: true,
        MINIFY_CSS: true,
        MINIFY_JS: true
    },

    // ===== SEO Configuration =====
    SEO: {
        ENABLED: true,
        GENERATE_SITEMAPS: true,
        STRUCTURED_DATA: true,
        OPEN_GRAPH: true,
        TWITTER_CARDS: true,
        CANONICAL_URLS: true,
        META_DESCRIPTIONS: true,
        ALT_TEXTS: true
    },

    // ===== Analytics Configuration =====
    ANALYTICS: {
        GOOGLE_ANALYTICS: {
            ENABLED: false,
            TRACKING_ID: 'GA_TRACKING_ID_HERE'
        },
        FACEBOOK_PIXEL: {
            ENABLED: false,
            PIXEL_ID: 'FB_PIXEL_ID_HERE'
        },
        CUSTOM_ANALYTICS: {
            ENABLED: false,
            ENDPOINT: 'https://your-analytics-endpoint.com'
        }
    },

    // ===== Security Configuration =====
    SECURITY: {
        CONTENT_SECURITY_POLICY: true,
        HTTPS_ONLY: true,
        SECURE_HEADERS: true,
        XSS_PROTECTION: true,
        CLICKJACKING_PROTECTION: true,
        REFERRER_POLICY: 'strict-origin-when-cross-origin'
    },

    // ===== Localization Configuration =====
    LOCALIZATION: {
        DEFAULT_LANGUAGE: 'ar',
        SUPPORTED_LANGUAGES: ['ar', 'en'],
        RTL_LANGUAGES: ['ar'],
        DATE_FORMAT: 'DD/MM/YYYY',
        TIME_FORMAT: '24h',
        CURRENCY: 'SAR',
        NUMBER_FORMAT: 'ar-SA'
    },

    // ===== Server Configuration =====
    SERVERS: {
        // Sample server configuration
        // In a real implementation, this would be managed through an admin panel
        DEFAULT_SERVERS: [
            {
                name: 'سيرفر 1',
                quality: 'HD',
                type: 'stream',
                priority: 1
            },
            {
                name: 'سيرفر 2',
                quality: 'FHD',
                type: 'stream',
                priority: 2
            },
            {
                name: 'تحميل مباشر',
                quality: 'HD',
                type: 'download',
                priority: 3
            }
        ]
    },

    // ===== Error Handling Configuration =====
    ERROR_HANDLING: {
        LOG_ERRORS: true,
        SHOW_ERROR_DETAILS: false, // Set to true for development
        FALLBACK_CONTENT: true,
        RETRY_FAILED_REQUESTS: true,
        MAX_RETRY_ATTEMPTS: 3,
        RETRY_DELAY: 1000 // milliseconds
    },

    // ===== Development Configuration =====
    DEVELOPMENT: {
        DEBUG_MODE: false, // Set to true for development
        CONSOLE_LOGS: false,
        PERFORMANCE_MONITORING: false,
        MOCK_API_RESPONSES: false,
        BYPASS_CACHE: false
    }
};

// ===== Environment-specific overrides =====
if (typeof window !== 'undefined') {
    // Browser environment
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        // Development environment
        CONFIG.DEVELOPMENT.DEBUG_MODE = true;
        CONFIG.DEVELOPMENT.CONSOLE_LOGS = true;
        CONFIG.ERROR_HANDLING.SHOW_ERROR_DETAILS = true;
        CONFIG.PERFORMANCE.SERVICE_WORKER = false; // Disable SW in development
    }
}

// ===== Utility Functions =====
const ConfigUtils = {
    // Get configuration value with fallback
    get: (path, fallback = null) => {
        const keys = path.split('.');
        let value = CONFIG;
        
        for (const key of keys) {
            if (value && typeof value === 'object' && key in value) {
                value = value[key];
            } else {
                return fallback;
            }
        }
        
        return value;
    },

    // Check if feature is enabled
    isFeatureEnabled: (feature) => {
        return ConfigUtils.get(`FEATURES.${feature}.ENABLED`, false);
    },

    // Get API URL with parameters
    getApiUrl: (endpoint, params = {}) => {
        const url = new URL(CONFIG.API.BASE_URL + endpoint);
        url.searchParams.append('api_key', CONFIG.API.TMDB_API_KEY);
        url.searchParams.append('language', CONFIG.API.LANGUAGE);
        
        if (!CONFIG.API.ADULT_CONTENT) {
            url.searchParams.append('include_adult', 'false');
        }
        
        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined) {
                url.searchParams.append(key, params[key]);
            }
        });
        
        return url.toString();
    },

    // Get image URL
    getImageUrl: (path, size = 'w500') => {
        if (!path) return CONFIG.SITE.DEFAULT_POSTER;
        
        const baseUrl = size.startsWith('w') ? 
            CONFIG.API.IMAGE_BASE_URL.replace('w500', size) : 
            CONFIG.API.BACKDROP_BASE_URL.replace('w1280', size);
            
        return baseUrl + path;
    },

    // Validate configuration
    validate: () => {
        const errors = [];
        
        if (!CONFIG.API.TMDB_API_KEY || CONFIG.API.TMDB_API_KEY === 'YOUR_TMDB_API_KEY_HERE') {
            errors.push('TMDB API key is not configured');
        }
        
        if (!CONFIG.SITE.URL || CONFIG.SITE.URL === 'https://yoursite.com') {
            errors.push('Site URL is not configured');
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, ConfigUtils };
} else if (typeof window !== 'undefined') {
    window.CONFIG = CONFIG;
    window.ConfigUtils = ConfigUtils;
}

// Validate configuration on load
if (CONFIG.DEVELOPMENT.DEBUG_MODE) {
    const validation = ConfigUtils.validate();
    if (!validation.isValid) {
        console.warn('Configuration validation failed:', validation.errors);
    }
}
