// ===== Error Handler =====

class ErrorHandler {
    constructor() {
        this.errors = [];
        this.maxErrors = 50;
        this.retryAttempts = new Map();
        this.maxRetryAttempts = 3;
        this.retryDelay = 1000;
        
        this.init();
    }

    init() {
        this.setupGlobalErrorHandlers();
        this.setupUnhandledRejectionHandler();
        this.setupNetworkErrorHandler();
    }

    // ===== Global Error Handlers =====
    setupGlobalErrorHandlers() {
        window.addEventListener('error', (event) => {
            this.handleError({
                type: 'javascript',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error,
                stack: event.error?.stack,
                timestamp: new Date().toISOString()
            });
        });

        window.addEventListener('unhandledrejection', (event) => {
            this.handleError({
                type: 'promise',
                message: event.reason?.message || 'Unhandled Promise Rejection',
                reason: event.reason,
                stack: event.reason?.stack,
                timestamp: new Date().toISOString()
            });
        });
    }

    setupUnhandledRejectionHandler() {
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            
            // Prevent the default browser behavior
            event.preventDefault();
            
            // Handle specific error types
            if (event.reason?.name === 'NetworkError') {
                this.handleNetworkError(event.reason);
            } else if (event.reason?.name === 'TypeError') {
                this.handleTypeError(event.reason);
            } else {
                this.handleGenericError(event.reason);
            }
        });
    }

    setupNetworkErrorHandler() {
        // Monitor fetch requests
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            try {
                const response = await originalFetch(...args);
                
                if (!response.ok) {
                    this.handleHTTPError(response, args[0]);
                }
                
                return response;
            } catch (error) {
                this.handleNetworkError(error, args[0]);
                throw error;
            }
        };
    }

    // ===== Error Handling Methods =====
    handleError(errorInfo) {
        // Add to error log
        this.logError(errorInfo);
        
        // Show user-friendly message based on error type
        if (CONFIG.DEVELOPMENT.DEBUG_MODE) {
            console.error('Error caught:', errorInfo);
        }
        
        // Handle specific error types
        switch (errorInfo.type) {
            case 'javascript':
                this.handleJavaScriptError(errorInfo);
                break;
            case 'promise':
                this.handlePromiseError(errorInfo);
                break;
            case 'network':
                this.handleNetworkError(errorInfo);
                break;
            case 'api':
                this.handleAPIError(errorInfo);
                break;
            default:
                this.handleGenericError(errorInfo);
        }
    }

    handleJavaScriptError(error) {
        if (error.message?.includes('Script error')) {
            // Cross-origin script error
            this.showUserMessage('حدث خطأ في تحميل أحد الملفات الخارجية', 'warning');
        } else if (error.message?.includes('Network')) {
            this.handleNetworkError(error);
        } else {
            this.showUserMessage('حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة.', 'error');
        }
    }

    handlePromiseError(error) {
        if (error.reason?.name === 'AbortError') {
            // Request was aborted, usually not a real error
            return;
        }
        
        this.showUserMessage('حدث خطأ في معالجة البيانات', 'warning');
    }

    handleNetworkError(error, url = '') {
        if (!navigator.onLine) {
            this.showUserMessage('لا يوجد اتصال بالإنترنت', 'warning');
            return;
        }
        
        // Try to retry the request
        if (url && this.shouldRetry(url)) {
            this.retryRequest(url);
        } else {
            this.showUserMessage('فشل في الاتصال بالخادم', 'error');
        }
    }

    handleHTTPError(response, url) {
        const status = response.status;
        
        switch (status) {
            case 400:
                this.showUserMessage('طلب غير صحيح', 'warning');
                break;
            case 401:
                this.showUserMessage('غير مصرح بالوصول', 'error');
                break;
            case 403:
                this.showUserMessage('ممنوع الوصول', 'error');
                break;
            case 404:
                this.showUserMessage('المحتوى غير موجود', 'warning');
                break;
            case 429:
                this.showUserMessage('تم تجاوز حد الطلبات. يرجى المحاولة لاحقاً', 'warning');
                break;
            case 500:
                this.showUserMessage('خطأ في الخادم', 'error');
                break;
            case 503:
                this.showUserMessage('الخدمة غير متوفرة مؤقتاً', 'warning');
                break;
            default:
                this.showUserMessage(`خطأ في الخادم (${status})`, 'error');
        }
    }

    handleAPIError(error) {
        if (error.message?.includes('API key')) {
            this.showUserMessage('خطأ في مفتاح API', 'error');
        } else if (error.message?.includes('quota')) {
            this.showUserMessage('تم تجاوز حد استخدام API', 'warning');
        } else {
            this.showUserMessage('خطأ في جلب البيانات', 'warning');
        }
    }

    handleTypeError(error) {
        if (error.message?.includes('Cannot read property')) {
            this.showUserMessage('خطأ في معالجة البيانات', 'warning');
        } else {
            this.handleGenericError(error);
        }
    }

    handleGenericError(error) {
        if (CONFIG.ERROR_HANDLING.SHOW_ERROR_DETAILS) {
            this.showUserMessage(`خطأ: ${error.message || 'خطأ غير معروف'}`, 'error');
        } else {
            this.showUserMessage('حدث خطأ غير متوقع', 'error');
        }
    }

    // ===== Retry Logic =====
    shouldRetry(url) {
        if (!CONFIG.ERROR_HANDLING.RETRY_FAILED_REQUESTS) {
            return false;
        }
        
        const attempts = this.retryAttempts.get(url) || 0;
        return attempts < this.maxRetryAttempts;
    }

    async retryRequest(url) {
        const attempts = this.retryAttempts.get(url) || 0;
        this.retryAttempts.set(url, attempts + 1);
        
        // Wait before retrying
        await this.delay(this.retryDelay * Math.pow(2, attempts));
        
        try {
            const response = await fetch(url);
            if (response.ok) {
                this.retryAttempts.delete(url);
                this.showUserMessage('تم استعادة الاتصال', 'success');
            }
            return response;
        } catch (error) {
            if (this.shouldRetry(url)) {
                return this.retryRequest(url);
            } else {
                this.retryAttempts.delete(url);
                throw error;
            }
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // ===== Error Logging =====
    logError(error) {
        this.errors.push({
            ...error,
            id: this.generateErrorId(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            timestamp: new Date().toISOString()
        });
        
        // Keep only recent errors
        if (this.errors.length > this.maxErrors) {
            this.errors = this.errors.slice(-this.maxErrors);
        }
        
        // Send to logging service if configured
        if (CONFIG.ERROR_HANDLING.LOG_ERRORS) {
            this.sendErrorToService(error);
        }
    }

    generateErrorId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    async sendErrorToService(error) {
        try {
            // Send to custom logging endpoint
            if (CONFIG.ANALYTICS.CUSTOM_ANALYTICS.ENABLED) {
                await fetch(CONFIG.ANALYTICS.CUSTOM_ANALYTICS.ENDPOINT + '/errors', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(error)
                });
            }
        } catch (e) {
            // Ignore logging errors
            console.warn('Failed to send error to logging service:', e);
        }
    }

    // ===== User Messaging =====
    showUserMessage(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `error-notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getIconForType(type)}"></i>
                <span>${message}</span>
                <button class="close-btn" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Auto-remove after delay
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, type === 'error' ? 8000 : 5000);
        
        // Add animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
    }

    getIconForType(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    // ===== Recovery Methods =====
    recoverFromError(errorType) {
        switch (errorType) {
            case 'network':
                this.attemptNetworkRecovery();
                break;
            case 'api':
                this.attemptAPIRecovery();
                break;
            case 'storage':
                this.attemptStorageRecovery();
                break;
            default:
                this.attemptGenericRecovery();
        }
    }

    attemptNetworkRecovery() {
        // Try to reload critical resources
        if (navigator.onLine) {
            this.showUserMessage('جاري محاولة استعادة الاتصال...', 'info');
            // Reload page after a delay
            setTimeout(() => {
                window.location.reload();
            }, 3000);
        }
    }

    attemptAPIRecovery() {
        // Clear API cache and retry
        if (typeof contentManager !== 'undefined') {
            contentManager.cache.clear();
            this.showUserMessage('جاري إعادة تحميل البيانات...', 'info');
        }
    }

    attemptStorageRecovery() {
        try {
            // Clear corrupted localStorage data
            localStorage.clear();
            this.showUserMessage('تم مسح البيانات المحلية المتضررة', 'info');
        } catch (e) {
            console.error('Failed to clear localStorage:', e);
        }
    }

    attemptGenericRecovery() {
        this.showUserMessage('جاري محاولة الإصلاح...', 'info');
        setTimeout(() => {
            window.location.reload();
        }, 2000);
    }

    // ===== Public Methods =====
    getErrorLog() {
        return this.errors;
    }

    clearErrorLog() {
        this.errors = [];
    }

    reportError(message, type = 'manual') {
        this.handleError({
            type: type,
            message: message,
            timestamp: new Date().toISOString(),
            manual: true
        });
    }
}

// Initialize error handler
const errorHandler = new ErrorHandler();
window.errorHandler = errorHandler;

// Add CSS for error notifications
const errorStyles = `
    .error-notification {
        position: fixed;
        top: 100px;
        right: 20px;
        background: var(--secondary-color);
        color: var(--light-color);
        padding: 15px;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        z-index: 1050;
        transform: translateX(400px);
        transition: var(--transition);
        max-width: 400px;
        border-left: 4px solid var(--info-color);
    }
    
    .error-notification.show {
        transform: translateX(0);
    }
    
    .error-notification.success {
        border-left-color: var(--success-color);
    }
    
    .error-notification.error {
        border-left-color: var(--danger-color);
    }
    
    .error-notification.warning {
        border-left-color: var(--warning-color);
    }
    
    .notification-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .notification-content i:first-child {
        font-size: 1.2rem;
    }
    
    .close-btn {
        background: none;
        border: none;
        color: var(--gray-color);
        cursor: pointer;
        margin-left: auto;
        padding: 5px;
        border-radius: 3px;
        transition: var(--transition);
    }
    
    .close-btn:hover {
        background: rgba(255, 255, 255, 0.1);
        color: var(--light-color);
    }
    
    @media (max-width: 768px) {
        .error-notification {
            right: 10px;
            left: 10px;
            max-width: none;
            transform: translateY(-100px);
        }
        
        .error-notification.show {
            transform: translateY(0);
        }
    }
`;

// Add styles to head
const styleSheet = document.createElement('style');
styleSheet.textContent = errorStyles;
document.head.appendChild(styleSheet);
