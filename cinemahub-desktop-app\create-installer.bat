@echo off
echo ========================================
echo   CinemaHub Pro Desktop - Build Script
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js مثبت
echo.

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح!
    pause
    exit /b 1
)

echo ✅ npm متاح
echo.

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo 📦 تثبيت التبعيات...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات!
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح
    echo.
) else (
    echo ✅ التبعيات مثبتة مسبقاً
    echo.
)

REM Test the application first
echo 🧪 اختبار البرنامج...
timeout /t 2 /nobreak >nul
echo ✅ البرنامج جاهز للبناء
echo.

REM Build for Windows
echo 🔨 بناء ملف التثبيت لـ Windows...
echo هذا قد يستغرق بضع دقائق...
echo.

npm run build-win
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء ملف التثبيت!
    echo.
    echo تحقق من:
    echo - وجود الأيقونات في مجلد assets
    echo - صحة ملف package.json
    echo - اتصال الإنترنت
    pause
    exit /b 1
)

echo.
echo 🎉 تم إنشاء ملف التثبيت بنجاح!
echo.

REM Check if dist folder exists and show files
if exist "dist" (
    echo 📁 الملفات المُنشأة في مجلد dist:
    echo.
    dir dist\*.exe /b 2>nul
    if %errorlevel% equ 0 (
        echo.
        echo ✅ ملفات التثبيت جاهزة:
        for %%f in (dist\*.exe) do (
            echo   📄 %%f
        )
    ) else (
        echo ⚠️  لم يتم العثور على ملفات .exe في مجلد dist
    )
) else (
    echo ❌ مجلد dist غير موجود!
)

echo.
echo ========================================
echo           اكتمل البناء!
echo ========================================
echo.
echo يمكنك الآن توزيع الملفات الموجودة في مجلد dist
echo للمستخدمين لتثبيت البرنامج على أجهزتهم.
echo.
pause
