<?php
/**
 * The template for displaying all single posts
 *
 * @package CinemaHub Pro
 */

get_header(); ?>

<div style="margin-top: 80px; padding: 2rem 0;">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <?php while (have_posts()) : the_post(); ?>
                    <article id="post-<?php the_ID(); ?>" <?php post_class('single-post'); ?> style="background: var(--surface-color); border-radius: var(--border-radius-2xl); padding: 2rem; border: 2px solid rgba(148, 163, 184, 0.1); box-shadow: var(--shadow-lg); margin-bottom: 2rem;">
                        <!-- Post Header -->
                        <header class="post-header" style="margin-bottom: 2rem;">
                            <h1 class="post-title" style="color: var(--light-color); font-size: var(--font-size-4xl); font-weight: 700; margin-bottom: 1rem; line-height: 1.2;">
                                <?php the_title(); ?>
                            </h1>
                            
                            <div class="post-meta" style="display: flex; align-items: center; gap: 1.5rem; margin-bottom: 1.5rem; flex-wrap: wrap; color: var(--gray-400); font-size: var(--font-size-sm);">
                                <span style="display: flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-calendar" style="color: var(--primary-color);"></i>
                                    <?php echo get_the_date(); ?>
                                </span>
                                
                                <span style="display: flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-user" style="color: var(--primary-color);"></i>
                                    <a href="<?php echo get_author_posts_url(get_the_author_meta('ID')); ?>" style="color: var(--gray-400); text-decoration: none; transition: var(--transition);" onmouseover="this.style.color='var(--primary-color)'" onmouseout="this.style.color='var(--gray-400)'">
                                        <?php the_author(); ?>
                                    </a>
                                </span>
                                
                                <span style="display: flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-comments" style="color: var(--primary-color);"></i>
                                    <a href="#comments" style="color: var(--gray-400); text-decoration: none; transition: var(--transition);" onmouseover="this.style.color='var(--primary-color)'" onmouseout="this.style.color='var(--gray-400)'">
                                        <?php comments_number('0 تعليق', 'تعليق واحد', '% تعليقات'); ?>
                                    </a>
                                </span>
                                
                                <?php if (has_category()) : ?>
                                    <span style="display: flex; align-items: center; gap: 0.5rem;">
                                        <i class="fas fa-folder" style="color: var(--primary-color);"></i>
                                        <?php the_category(', '); ?>
                                    </span>
                                <?php endif; ?>
                                
                                <span style="display: flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-eye" style="color: var(--primary-color);"></i>
                                    <?php 
                                    $views = get_post_meta(get_the_ID(), 'post_views', true);
                                    echo $views ? $views : '0';
                                    ?> مشاهدة
                                </span>
                            </div>
                            
                            <?php if (has_post_thumbnail()) : ?>
                                <div class="post-thumbnail" style="margin-bottom: 2rem; border-radius: var(--border-radius-xl); overflow: hidden; box-shadow: var(--shadow-lg);">
                                    <?php the_post_thumbnail('large', array('style' => 'width: 100%; height: 400px; object-fit: cover;')); ?>
                                </div>
                            <?php endif; ?>
                        </header>

                        <!-- Post Content -->
                        <div class="post-content" style="color: var(--gray-300); line-height: 1.8; font-size: var(--font-size-base);">
                            <?php
                            the_content();
                            
                            wp_link_pages(array(
                                'before' => '<div class="page-links" style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid var(--gray-800);">',
                                'after'  => '</div>',
                                'link_before' => '<span style="display: inline-block; padding: 0.5rem 1rem; background: var(--gradient-primary); color: white; border-radius: var(--border-radius); margin: 0 0.25rem; text-decoration: none;">',
                                'link_after' => '</span>',
                            ));
                            ?>
                        </div>

                        <!-- Post Tags -->
                        <?php if (has_tag()) : ?>
                            <div class="post-tags" style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid var(--gray-800);">
                                <h4 style="color: var(--light-color); font-size: var(--font-size-lg); font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-tags" style="color: var(--primary-color);"></i>
                                    الكلمات المفتاحية
                                </h4>
                                <div class="tags-list" style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                                    <?php
                                    $tags = get_the_tags();
                                    if ($tags) :
                                        foreach ($tags as $tag) :
                                    ?>
                                        <a href="<?php echo get_tag_link($tag->term_id); ?>" style="padding: 0.5rem 1rem; background: rgba(255, 255, 255, 0.05); color: var(--gray-300); text-decoration: none; border-radius: var(--border-radius); font-size: var(--font-size-sm); font-weight: 500; transition: var(--transition);" onmouseover="this.style.background='var(--gradient-primary)'; this.style.color='white'; this.style.transform='scale(1.05)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.05)'; this.style.color='var(--gray-300)'; this.style.transform='scale(1)'">
                                            #<?php echo esc_html($tag->name); ?>
                                        </a>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Share Buttons -->
                        <div class="post-share" style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid var(--gray-800);">
                            <h4 style="color: var(--light-color); font-size: var(--font-size-lg); font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-share-alt" style="color: var(--primary-color);"></i>
                                شارك المقال
                            </h4>
                            <div class="share-buttons" style="display: flex; gap: 1rem; flex-wrap: wrap;">
                                <?php
                                $post_url = urlencode(get_permalink());
                                $post_title = urlencode(get_the_title());
                                ?>
                                
                                <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo $post_url; ?>" target="_blank" style="display: flex; align-items: center; gap: 0.5rem; padding: 0.75rem 1.5rem; background: #1877f2; color: white; text-decoration: none; border-radius: var(--border-radius-lg); font-weight: 500; transition: var(--transition);" onmouseover="this.style.transform='translateY(-2px) scale(1.05)'" onmouseout="this.style.transform='translateY(0) scale(1)'">
                                    <i class="fab fa-facebook-f"></i>
                                    فيسبوك
                                </a>
                                
                                <a href="https://twitter.com/intent/tweet?url=<?php echo $post_url; ?>&text=<?php echo $post_title; ?>" target="_blank" style="display: flex; align-items: center; gap: 0.5rem; padding: 0.75rem 1.5rem; background: #1da1f2; color: white; text-decoration: none; border-radius: var(--border-radius-lg); font-weight: 500; transition: var(--transition);" onmouseover="this.style.transform='translateY(-2px) scale(1.05)'" onmouseout="this.style.transform='translateY(0) scale(1)'">
                                    <i class="fab fa-twitter"></i>
                                    تويتر
                                </a>
                                
                                <a href="https://wa.me/?text=<?php echo $post_title; ?>%20<?php echo $post_url; ?>" target="_blank" style="display: flex; align-items: center; gap: 0.5rem; padding: 0.75rem 1.5rem; background: #25d366; color: white; text-decoration: none; border-radius: var(--border-radius-lg); font-weight: 500; transition: var(--transition);" onmouseover="this.style.transform='translateY(-2px) scale(1.05)'" onmouseout="this.style.transform='translateY(0) scale(1)'">
                                    <i class="fab fa-whatsapp"></i>
                                    واتساب
                                </a>
                                
                                <a href="https://t.me/share/url?url=<?php echo $post_url; ?>&text=<?php echo $post_title; ?>" target="_blank" style="display: flex; align-items: center; gap: 0.5rem; padding: 0.75rem 1.5rem; background: #0088cc; color: white; text-decoration: none; border-radius: var(--border-radius-lg); font-weight: 500; transition: var(--transition);" onmouseover="this.style.transform='translateY(-2px) scale(1.05)'" onmouseout="this.style.transform='translateY(0) scale(1)'">
                                    <i class="fab fa-telegram-plane"></i>
                                    تيليجرام
                                </a>
                                
                                <button onclick="copyToClipboard('<?php echo get_permalink(); ?>')" style="display: flex; align-items: center; gap: 0.5rem; padding: 0.75rem 1.5rem; background: var(--gradient-primary); color: white; border: none; border-radius: var(--border-radius-lg); font-weight: 500; cursor: pointer; transition: var(--transition);" onmouseover="this.style.transform='translateY(-2px) scale(1.05)'" onmouseout="this.style.transform='translateY(0) scale(1)'">
                                    <i class="fas fa-copy"></i>
                                    نسخ الرابط
                                </button>
                            </div>
                        </div>
                    </article>

                    <!-- Author Bio -->
                    <div class="author-bio" style="background: var(--surface-color); border-radius: var(--border-radius-2xl); padding: 2rem; border: 2px solid rgba(148, 163, 184, 0.1); box-shadow: var(--shadow-lg); margin-bottom: 2rem;">
                        <div style="display: flex; gap: 1.5rem; align-items: center;">
                            <div class="author-avatar" style="flex-shrink: 0;">
                                <?php echo get_avatar(get_the_author_meta('ID'), 80, '', '', array('style' => 'border-radius: 50%; border: 3px solid var(--primary-color);')); ?>
                            </div>
                            <div class="author-info" style="flex: 1;">
                                <h4 style="color: var(--light-color); font-size: var(--font-size-xl); font-weight: 600; margin-bottom: 0.5rem;">
                                    <?php the_author(); ?>
                                </h4>
                                <p style="color: var(--gray-400); margin-bottom: 1rem; line-height: 1.6;">
                                    <?php echo get_the_author_meta('description') ?: 'كاتب في موقع CinemaHub Pro'; ?>
                                </p>
                                <a href="<?php echo get_author_posts_url(get_the_author_meta('ID')); ?>" style="display: inline-flex; align-items: center; gap: 0.5rem; background: var(--gradient-primary); color: white; padding: 0.5rem 1rem; border-radius: var(--border-radius); text-decoration: none; font-weight: 500; transition: var(--transition);" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                                    <i class="fas fa-user"></i>
                                    عرض جميع المقالات
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Related Posts -->
                    <?php
                    $related_posts = new WP_Query(array(
                        'post_type' => 'post',
                        'posts_per_page' => 3,
                        'post__not_in' => array(get_the_ID()),
                        'category__in' => wp_get_post_categories(get_the_ID()),
                        'orderby' => 'rand'
                    ));

                    if ($related_posts->have_posts()) :
                    ?>
                        <div class="related-posts" style="background: var(--surface-color); border-radius: var(--border-radius-2xl); padding: 2rem; border: 2px solid rgba(148, 163, 184, 0.1); box-shadow: var(--shadow-lg); margin-bottom: 2rem;">
                            <h3 style="color: var(--light-color); font-size: var(--font-size-2xl); font-weight: 600; margin-bottom: 1.5rem; display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-newspaper" style="color: var(--primary-color);"></i>
                                مقالات ذات صلة
                            </h3>
                            
                            <div class="related-posts-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                                <?php while ($related_posts->have_posts()) : $related_posts->the_post(); ?>
                                    <article class="related-post" style="background: rgba(255, 255, 255, 0.05); border-radius: var(--border-radius-lg); overflow: hidden; transition: var(--transition); cursor: pointer;" onclick="window.location.href='<?php the_permalink(); ?>'" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='var(--shadow-lg)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <div class="related-post-thumbnail">
                                                <?php the_post_thumbnail('medium', array('style' => 'width: 100%; height: 150px; object-fit: cover;')); ?>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div style="padding: 1rem;">
                                            <h4 style="color: var(--light-color); font-size: var(--font-size-sm); font-weight: 600; margin-bottom: 0.5rem; line-height: 1.3;">
                                                <?php the_title(); ?>
                                            </h4>
                                            <div style="color: var(--gray-400); font-size: var(--font-size-xs); display: flex; align-items: center; gap: 0.5rem;">
                                                <span><i class="fas fa-calendar"></i> <?php echo get_the_date(); ?></span>
                                            </div>
                                        </div>
                                    </article>
                                <?php endwhile; ?>
                                <?php wp_reset_postdata(); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Comments -->
                    <?php
                    if (comments_open() || get_comments_number()) :
                        comments_template();
                    endif;
                    ?>

                <?php endwhile; ?>
            </div>
            
            <div class="col-lg-4">
                <?php get_sidebar(); ?>
            </div>
        </div>
    </div>
</div>

<script>
// Copy to clipboard function
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i> تم النسخ!';
        button.style.background = 'var(--success-color)';
        
        setTimeout(function() {
            button.innerHTML = originalText;
            button.style.background = 'var(--gradient-primary)';
        }, 2000);
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
    });
}

// Update post views
document.addEventListener('DOMContentLoaded', function() {
    // AJAX call to update post views
    if (typeof cinemahub_ajax !== 'undefined') {
        fetch(cinemahub_ajax.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=update_post_views&post_id=<?php echo get_the_ID(); ?>&nonce=' + cinemahub_ajax.nonce
        });
    }
});
</script>

<style>
/* Single Post Styles */
.single-post .post-content h1,
.single-post .post-content h2,
.single-post .post-content h3,
.single-post .post-content h4,
.single-post .post-content h5,
.single-post .post-content h6 {
    color: var(--light-color);
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.single-post .post-content p {
    margin-bottom: 1.5rem;
}

.single-post .post-content img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius-lg);
    margin: 1rem 0;
}

.single-post .post-content blockquote {
    background: rgba(220, 38, 38, 0.1);
    border-right: 4px solid var(--primary-color);
    padding: 1rem 1.5rem;
    margin: 1.5rem 0;
    border-radius: var(--border-radius);
    font-style: italic;
}

.single-post .post-content ul,
.single-post .post-content ol {
    margin-bottom: 1.5rem;
    padding-right: 2rem;
}

.single-post .post-content li {
    margin-bottom: 0.5rem;
}

.single-post .post-content a {
    color: var(--primary-color);
    text-decoration: underline;
}

.single-post .post-content a:hover {
    color: var(--primary-light);
}

/* Responsive */
@media (max-width: 768px) {
    .post-meta {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 0.75rem !important;
    }
    
    .share-buttons {
        flex-direction: column !important;
    }
    
    .share-buttons a,
    .share-buttons button {
        justify-content: center !important;
    }
    
    .author-bio > div {
        flex-direction: column !important;
        text-align: center !important;
    }
    
    .related-posts-grid {
        grid-template-columns: 1fr !important;
    }
}
</style>

<?php get_footer(); ?>
