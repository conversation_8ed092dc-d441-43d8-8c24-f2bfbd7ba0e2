<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="CinemaHub Pro - أفضل موقع لمشاهدة الأفلام والمسلسلات العربية والأجنبية بجودة عالية">
    <meta name="keywords" content="أفلام, مسلسلات, أفلام رعب, أفلام أكشن, أفلام كوميديا, مسلسلات تركية, مسلسلات كورية">
    <title>CinemaHub Pro - سينما هاب برو | الموقع الأول للأفلام والمسلسلات</title>

    <!-- External CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css" rel="stylesheet">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
    /* ===== CSS Variables ===== */
    :root {
        /* ===== Professional Color Palette ===== */
        --primary-color: #dc2626;
        --primary-dark: #b91c1c;
        --primary-light: #ef4444;
        --primary-hover: #f87171;
        --primary-50: #fef2f2;
        --primary-100: #fee2e2;
        --primary-500: #ef4444;
        --primary-600: #dc2626;
        --primary-700: #b91c1c;
        --primary-800: #991b1b;
        --primary-900: #7f1d1d;

        --secondary-color: #1e293b;
        --secondary-dark: #0f172a;
        --secondary-light: #334155;
        --accent-color: #f59e0b;
        --accent-light: #fbbf24;
        --accent-dark: #d97706;

        --dark-color: #0f172a;
        --darker-color: #020617;
        --light-color: #ffffff;
        --surface-color: #1e293b;
        --surface-light: #334155;

        /* ===== Gray Scale ===== */
        --gray-50: #f8fafc;
        --gray-100: #f1f5f9;
        --gray-200: #e2e8f0;
        --gray-300: #cbd5e1;
        --gray-400: #94a3b8;
        --gray-500: #64748b;
        --gray-600: #475569;
        --gray-700: #334155;
        --gray-800: #1e293b;
        --gray-900: #0f172a;

        /* ===== Semantic Colors ===== */
        --success-color: #059669;
        --success-light: #10b981;
        --success-dark: #047857;
        --warning-color: #d97706;
        --warning-light: #f59e0b;
        --warning-dark: #b45309;
        --danger-color: #dc2626;
        --danger-light: #ef4444;
        --danger-dark: #b91c1c;
        --info-color: #0ea5e9;
        --info-light: #38bdf8;
        --info-dark: #0284c7;
        
        /* ===== Professional Gradients ===== */
        --gradient-primary: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
        --gradient-primary-light: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
        --gradient-secondary: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
        --gradient-dark: linear-gradient(135deg, var(--dark-color) 0%, var(--darker-color) 100%);
        --gradient-surface: linear-gradient(135deg, var(--surface-color) 0%, var(--surface-light) 100%);
        --gradient-overlay: linear-gradient(180deg, rgba(15,23,42,0) 0%, rgba(15,23,42,0.95) 100%);
        --gradient-hero: linear-gradient(45deg, rgba(220,38,38,0.15) 0%, rgba(15,23,42,0.85) 100%);
        --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
        
        --font-family: 'Cairo', sans-serif;
        --font-size-xs: 0.65rem;
        --font-size-sm: 0.75rem;
        --font-size-base: 0.85rem;
        --font-size-lg: 0.95rem;
        --font-size-xl: 1.05rem;
        --font-size-2xl: 1.25rem;
        --font-size-3xl: 1.5rem;
        --font-size-4xl: 1.75rem;
        --font-size-5xl: 2.25rem;
        
        --border-radius-sm: 0.375rem;
        --border-radius: 0.5rem;
        --border-radius-lg: 0.75rem;
        --border-radius-xl: 1rem;
        --border-radius-2xl: 1.5rem;
        --border-radius-3xl: 2rem;
        
        /* ===== Professional Shadows ===== */
        --shadow-sm: 0 1px 2px 0 rgba(15, 23, 42, 0.05);
        --shadow: 0 1px 3px 0 rgba(15, 23, 42, 0.1), 0 1px 2px 0 rgba(15, 23, 42, 0.06);
        --shadow-md: 0 4px 6px -1px rgba(15, 23, 42, 0.1), 0 2px 4px -1px rgba(15, 23, 42, 0.06);
        --shadow-lg: 0 10px 15px -3px rgba(15, 23, 42, 0.15), 0 4px 6px -2px rgba(15, 23, 42, 0.08);
        --shadow-xl: 0 20px 25px -5px rgba(15, 23, 42, 0.2), 0 10px 10px -5px rgba(15, 23, 42, 0.1);
        --shadow-2xl: 0 25px 50px -12px rgba(15, 23, 42, 0.35);
        --shadow-glow: 0 0 20px rgba(220, 38, 38, 0.25);
        --shadow-glow-accent: 0 0 20px rgba(245, 158, 11, 0.25);
        --shadow-inner: inset 0 2px 4px 0 rgba(15, 23, 42, 0.06);
        
        --transition-fast: all 0.15s ease;
        --transition: all 0.3s ease;
        --transition-slow: all 0.5s ease;
        
        --z-dropdown: 1000;
        --z-sticky: 1020;
        --z-fixed: 1030;
        --z-modal: 1050;
        --z-tooltip: 1070;
        --z-toast: 1080;
    }

    /* ===== Base Styles ===== */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    html {
        scroll-behavior: smooth;
        font-size: 16px;
    }

    body {
        font-family: var(--font-family);
        background: var(--dark-color);
        color: var(--light-color);
        line-height: 1.6;
        direction: rtl;
        overflow-x: hidden;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* ===== Typography ===== */
    h1, h2, h3, h4, h5, h6 {
        font-weight: 700;
        line-height: 1.2;
        margin-bottom: 0.5rem;
    }

    h1 { font-size: var(--font-size-4xl); font-weight: 700; }
    h2 { font-size: var(--font-size-3xl); font-weight: 600; }
    h3 { font-size: var(--font-size-2xl); font-weight: 600; }
    h4 { font-size: var(--font-size-xl); font-weight: 500; }
    h5 { font-size: var(--font-size-lg); font-weight: 500; }
    h6 { font-size: var(--font-size-base); font-weight: 500; }

    p {
        margin-bottom: 1rem;
        color: var(--gray-light);
        font-weight: 400;
    }

    a {
        color: var(--primary-color);
        text-decoration: none;
        transition: var(--transition);
    }

    a:hover {
        color: var(--primary-light);
    }

    /* ===== Loading Screen ===== */
    .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--gradient-dark);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease, visibility 0.5s ease;
    }

    .loading-screen.hidden {
        opacity: 0;
        visibility: hidden;
    }

    .loading-content {
        text-align: center;
        animation: fadeInUp 0.8s ease;
    }

    .loading-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1.5rem;
        margin-bottom: 3rem;
    }



    .loading-text {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }

    .loading-text .brand-text {
        font-size: var(--font-size-4xl);
        font-weight: 800;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
        letter-spacing: -0.02em;
    }

    .loading-text .brand-subtitle {
        font-size: var(--font-size-xl);
        color: var(--gray-color);
        font-weight: 500;
    }

    .loading-text .brand-tagline {
        font-size: var(--font-size-sm);
        color: var(--gray-light);
        font-weight: 400;
        margin-top: 0.25rem;
    }

    .spinner {
        width: 60px;
        height: 60px;
        border: 4px solid rgba(229, 9, 20, 0.3);
        border-top: 4px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 2rem;
    }

    .loading-content p {
        color: var(--gray-color);
        font-size: var(--font-size-lg);
        margin: 0;
        font-weight: 500;
    }

    /* ===== Animations ===== */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(50px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(229, 9, 20, 0.7);
        }
        50% {
            transform: scale(1.05);
            box-shadow: 0 0 0 20px rgba(229, 9, 20, 0);
        }
    }

    @keyframes playPulse {
        0%, 100% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.3);
            opacity: 0.7;
        }
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes slideInLeft {
        from {
            opacity: 0;
            transform: translateX(-100px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes zoomIn {
        from {
            opacity: 0;
            transform: scale(0.8);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    @keyframes float {
        0%, 100% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-15px);
        }
    }

    @keyframes glow {
        0%, 100% {
            box-shadow: 0 0 5px rgba(229, 9, 20, 0.5);
        }
        50% {
            box-shadow: 0 0 30px rgba(229, 9, 20, 0.8), 0 0 40px rgba(229, 9, 20, 0.6);
        }
    }

    @keyframes logoGlow {
        0%, 100% {
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3), 0 0 20px rgba(245, 158, 11, 0.2);
        }
        50% {
            box-shadow: 0 6px 25px rgba(220, 38, 38, 0.5), 0 0 30px rgba(245, 158, 11, 0.4);
        }
    }

    @keyframes logoShine {
        0% {
            transform: translateX(-100%) translateY(-100%) rotate(45deg);
        }
        50% {
            transform: translateX(100%) translateY(100%) rotate(45deg);
        }
        100% {
            transform: translateX(-100%) translateY(-100%) rotate(45deg);
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    /* ===== Header and Navigation ===== */














    /* ===== MODERN HEADER STYLES ===== */
    .modern-header {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        background: rgba(15, 23, 42, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-bottom: 1px solid rgba(148, 163, 184, 0.1);
        z-index: 1000;
        transition: all 0.3s ease;
        padding: 0.6rem 0;
    }

    .modern-header.scrolled {
        background: rgba(15, 23, 42, 0.98);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        border-bottom: 1px solid rgba(220, 38, 38, 0.2);
        padding: 0.4rem 0;
    }

    .header-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 1rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 2rem;
    }

    /* Logo Section */
    .header-logo {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        text-decoration: none;
        color: #ffffff;
        transition: all 0.3s ease;
        flex-shrink: 0;
    }

    .header-logo:hover {
        color: #ffffff;
        transform: scale(1.02);
    }

    .logo-icon {
        width: 45px;
        height: 45px;
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.3rem;
        font-weight: bold;
        box-shadow: 0 4px 15px rgba(220, 38, 38, 0.4);
        position: relative;
        overflow: hidden;
    }

    .logo-icon::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.15), transparent);
        transform: rotate(45deg);
        transition: all 0.6s ease;
        opacity: 0;
    }

    .header-logo:hover .logo-icon::before {
        opacity: 1;
        animation: logoShine 0.8s ease;
    }

    @keyframes logoShine {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .logo-text h1 {
        font-size: 1.5rem;
        font-weight: 800;
        margin: 0;
        color: #ffffff;
        letter-spacing: -0.5px;
    }

    .logo-text p {
        font-size: 0.7rem;
        color: #dc2626;
        margin: -2px 0 0 0;
        font-weight: 500;
    }

    /* Navigation Menu */
    .header-nav {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        flex: 1;
        justify-content: center;
    }

    .nav-item {
        position: relative;
    }

    .nav-link {
        display: flex;
        align-items: center;
        gap: 0.4rem;
        padding: 0.5rem 0.8rem;
        color: #ffffff;
        text-decoration: none;
        font-size: 0.85rem;
        font-weight: 500;
        border-radius: 8px;
        transition: all 0.3s ease;
        white-space: nowrap;
    }

    .nav-link:hover,
    .nav-link.active {
        color: #ffffff;
        background: rgba(220, 38, 38, 0.15);
        transform: translateY(-1px);
    }

    .dropdown-toggle .fa-chevron-down {
        font-size: 0.7rem;
        margin-left: 0.5rem;
        transition: transform 0.3s ease;
    }

    .nav-item.open .dropdown-toggle .fa-chevron-down {
        transform: rotate(180deg);
    }

    .nav-link i {
        font-size: 0.8rem;
    }

    /* Dropdown Menu */
    .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        background: var(--surface-color);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(148, 163, 184, 0.1);
        border-radius: 12px;
        box-shadow: var(--shadow-xl);
        padding: 0.75rem;
        min-width: 220px;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.3s ease;
        z-index: 1001;
        pointer-events: none;
    }

    .dropdown-menu.show {
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(0) !important;
        pointer-events: auto !important;
        display: block !important;
    }

    /* Force show dropdown */
    .nav-item.open .dropdown-menu {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    .nav-item:hover .dropdown-menu,
    .nav-item.open .dropdown-menu,
    .dropdown-menu.show {
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(0) !important;
        display: block !important;
    }

    .dropdown-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 0.75rem;
        color: #ffffff;
        text-decoration: none;
        font-size: 0.8rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        margin-bottom: 0.1rem;
    }

    .dropdown-item:hover {
        background: var(--gradient-primary);
        color: var(--light-color);
        transform: translateX(-5px);
        box-shadow: var(--shadow-md);
    }

    .dropdown-item i {
        width: 16px;
        text-align: center;
        font-size: 0.75rem;
    }

    /* Search Section */
    .header-search {
        position: relative;
        flex-shrink: 0;
    }

    .search-input {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: #ffffff;
        border-radius: 25px;
        padding: 0.5rem 2.5rem 0.5rem 1rem;
        width: 220px;
        transition: all 0.3s ease;
        font-size: 0.8rem;
        height: 38px;
    }

    .search-input:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: #dc2626;
        color: #ffffff;
        outline: none;
        box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
        width: 280px;
    }

    .search-input::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .search-btn {
        position: absolute;
        right: 0.3rem;
        top: 50%;
        transform: translateY(-50%);
        background: linear-gradient(135deg, #dc2626, #b91c1c);
        border: none;
        color: #ffffff;
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        cursor: pointer;
        font-size: 0.7rem;
    }

    .search-btn:hover {
        background: linear-gradient(135deg, #b91c1c, #991b1b);
        transform: translateY(-50%) scale(1.05);
    }

    /* User Section */
    .header-user {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        flex-shrink: 0;
    }

    .guest-section {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-login, .btn-register {
        background: var(--gradient-primary);
        color: var(--light-color);
        border: none;
        padding: 0.5rem 0.875rem;
        border-radius: var(--border-radius-lg);
        font-weight: 600;
        font-size: 0.8rem;
        transition: var(--transition);
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 0.35rem;
        text-decoration: none;
        height: 36px;
        white-space: nowrap;
    }

    .btn-register {
        background: transparent;
        border: 2px solid var(--primary-600);
        color: var(--primary-600);
    }

    .btn-login:hover {
        background: var(--primary-dark);
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .btn-register:hover {
        background: var(--primary-600);
        color: var(--light-color);
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    /* User Avatar and Dropdown */
    .user-dropdown-container {
        position: relative;
    }

    .user-avatar {
        background: transparent;
        border: none;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.6rem 0.8rem;
        border-radius: 12px;
        transition: all 0.3s ease;
        cursor: pointer;
        color: #ffffff;
        min-width: 180px;
    }

    .user-avatar:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: scale(1.02);
        box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
    }

    .dropdown-arrow {
        font-size: 0.8rem;
        transition: transform 0.3s ease;
    }

    .user-dropdown-container.active .dropdown-arrow {
        transform: rotate(180deg);
    }

    .user-avatar img {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        border: 3px solid var(--primary-color);
        object-fit: cover;
        box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
        transition: all 0.3s ease;
    }

    .user-avatar:hover img {
        transform: scale(1.05);
        box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
        border-color: var(--primary-light);
    }

    /* Admin Avatar Special Style */
    .user-section.admin .user-avatar img {
        border: 3px solid #ffd700;
        box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
    }

    .user-section.admin .user-avatar:hover img {
        box-shadow: 0 6px 20px rgba(255, 215, 0, 0.6);
        border-color: #ffed4e;
    }

    .user-details {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        text-align: right;
        margin-left: 0.75rem;
        line-height: 1.3;
    }

    .user-name {
        font-size: 1rem;
        font-weight: 700;
        color: #ffffff;
        line-height: 1.2;
        margin-bottom: 4px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        letter-spacing: 0.3px;
    }

    .user-plan {
        font-size: 0.8rem;
        color: #ffffff;
        font-weight: 600;
        background: var(--primary-color);
        padding: 4px 12px;
        border-radius: 15px;
        margin-top: 2px;
        text-transform: uppercase;
        letter-spacing: 0.8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
    }

    /* Plan Styles */
    .user-plan.free {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        color: #ffffff;
        border: 1px solid #6b7280;
    }

    .user-plan.premium {
        background: linear-gradient(135deg, #ffd700 0%, #f59e0b 100%);
        color: #000000;
        border: 1px solid #ffd700;
        box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
    }

    .user-plan.vip {
        background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%);
        color: #ffffff;
        border: 1px solid #9333ea;
        box-shadow: 0 2px 8px rgba(147, 51, 234, 0.3);
    }

    .user-plan.admin {
        background: linear-gradient(135deg, #ffd700 0%, #ff8c00 100%);
        color: #000000;
        font-weight: 800;
        border: 2px solid #ffd700;
        box-shadow: 0 4px 15px rgba(255, 215, 0, 0.5);
        animation: adminGlow 2s ease-in-out infinite alternate;
    }

    @keyframes adminGlow {
        from {
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.5);
        }
        to {
            box-shadow: 0 6px 25px rgba(255, 215, 0, 0.8);
        }
    }

    /* Admin User Name Special Style */
    .user-section.admin .user-name {
        color: #ffd700;
        text-shadow: 0 2px 8px rgba(255, 215, 0, 0.4);
        font-weight: 800;
    }

    .user-dropdown {
        position: absolute;
        top: 100%;
        right: 0;
        background: var(--surface-color);
        border: 1px solid rgba(148, 163, 184, 0.1);
        border-radius: var(--border-radius-xl);
        box-shadow: var(--shadow-2xl);
        backdrop-filter: blur(20px);
        padding: 0.5rem;
        min-width: 220px;
        margin-top: 0.5rem;
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.3s ease;
    }

    .user-dropdown.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .user-dropdown .dropdown-item {
        color: #ffffff;
        padding: 0.6rem 0.8rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.6rem;
        font-size: 0.8rem;
        font-weight: 500;
        margin-bottom: 0.1rem;
        cursor: pointer;
        border: none;
        background: transparent;
        width: 100%;
        text-align: right;
    }

    .user-dropdown .dropdown-item:hover {
        background: rgba(220, 38, 38, 0.1);
        color: var(--primary-600);
        transform: translateX(-3px);
    }

    .user-dropdown .dropdown-item.text-danger:hover {
        background: rgba(220, 38, 38, 0.1);
        color: var(--danger-color);
    }

    .user-dropdown .dropdown-item i {
        width: 16px;
        text-align: center;
        font-size: 0.8rem;
    }

    /* ===== Admin Panel Styles ===== */
    .user-dropdown .dropdown-header {
        color: #ffd700;
        font-weight: 700;
        font-size: var(--font-size-xs);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        padding: 0.5rem 1rem;
        background: rgba(255, 215, 0, 0.1);
        border-radius: var(--border-radius);
        margin: 0.25rem 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .user-dropdown .admin-item {
        background: rgba(147, 51, 234, 0.05);
        border-left: 3px solid #9333ea;
        margin: 0.1rem 0;
    }

    .user-dropdown .admin-item:hover {
        background: rgba(147, 51, 234, 0.15);
        color: #c084fc;
        border-left-color: #c084fc;
    }

    .user-dropdown .dropdown-divider {
        border-color: rgba(148, 163, 184, 0.2);
        margin: 0.5rem 0;
    }

    /* ===== Admin Modals Styles ===== */
    .admin-modal {
        background: var(--surface-color);
        border: 1px solid rgba(148, 163, 184, 0.1);
        border-radius: var(--border-radius-xl);
    }

    .admin-section-header h6 {
        color: var(--light-color);
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .admin-section-header p {
        color: var(--text-muted);
        font-size: 0.9rem;
        margin-bottom: 0;
    }

    .users-list, .content-list {
        max-height: 400px;
        overflow-y: auto;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.02);
        border-radius: var(--border-radius-lg);
        border: 1px solid rgba(148, 163, 184, 0.1);
    }

    .user-item, .content-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: var(--border-radius);
        margin-bottom: 0.75rem;
        border: 1px solid rgba(148, 163, 184, 0.1);
        transition: all 0.3s ease;
    }

    .user-item:hover, .content-item:hover {
        background: rgba(255, 255, 255, 0.08);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .user-item .user-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid var(--primary-color);
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.2);
        transition: all 0.3s ease;
    }

    .user-item .user-avatar:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
    }

    .user-info, .content-info {
        flex: 1;
        margin-left: 1rem;
    }

    .user-info h6, .content-info h6 {
        color: var(--light-color);
        margin-bottom: 0.25rem;
        font-weight: 600;
    }

    .user-info p, .content-info p {
        color: var(--text-muted);
        margin-bottom: 0.25rem;
        font-size: 0.85rem;
    }

    .user-actions, .content-actions {
        display: flex;
        gap: 0.5rem;
    }

    .user-actions .btn, .content-actions .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
        border-radius: var(--border-radius);
    }

    .rating {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        color: #ffd700;
        font-size: 0.85rem;
    }

    /* Watchlist and History Styles */
    .watchlist-item, .history-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: var(--border-radius);
        margin-bottom: 0.75rem;
        border: 1px solid rgba(148, 163, 184, 0.1);
        transition: all 0.3s ease;
    }

    .watchlist-item:hover, .history-item:hover {
        background: rgba(255, 255, 255, 0.08);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .watchlist-poster, .history-poster {
        width: 60px;
        height: 80px;
        border-radius: var(--border-radius);
        object-fit: cover;
        border: 2px solid var(--primary-color);
    }

    .watchlist-info, .history-info {
        flex: 1;
    }

    .watchlist-info h6, .history-info h6 {
        color: var(--light-color);
        margin-bottom: 0.25rem;
        font-weight: 600;
    }

    .watchlist-actions {
        display: flex;
        gap: 0.5rem;
    }

    .watchlist-actions .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
        border-radius: var(--border-radius);
    }

    .progress {
        height: 6px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
        overflow: hidden;
    }

    .progress-bar {
        background: var(--gradient-primary);
        height: 100%;
        transition: width 0.3s ease;
    }



    /* ===== Authentication Modals ===== */
    .auth-modal {
        background: var(--gradient-surface);
        border: 1px solid rgba(148, 163, 184, 0.1);
        border-radius: var(--border-radius-2xl);
        box-shadow: var(--shadow-2xl);
        backdrop-filter: blur(20px);
    }

    .auth-modal .modal-header {
        border-bottom: 1px solid rgba(148, 163, 184, 0.1);
        padding: 1.5rem;
    }

    .auth-modal .modal-title {
        color: var(--light-color);
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .auth-modal .modal-title i {
        color: var(--primary-600);
        font-size: 1.2rem;
    }

    .auth-modal .btn-close {
        background: none;
        border: none;
        color: var(--light-color);
        opacity: 0.7;
        font-size: 1.2rem;
    }

    .auth-modal .btn-close:hover {
        opacity: 1;
        color: var(--primary-600);
    }

    .auth-modal .modal-body {
        padding: 2rem;
    }

    .auth-modal .form-label {
        color: var(--light-color);
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .auth-modal .form-control {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(148, 163, 184, 0.2);
        color: var(--light-color);
        border-radius: var(--border-radius-lg);
        padding: 0.75rem 1rem;
        transition: var(--transition);
    }

    .auth-modal .form-control:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: var(--primary-600);
        color: var(--light-color);
        box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
    }

    .auth-modal .form-control::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }

    .auth-modal .input-group-text {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(148, 163, 184, 0.2);
        color: var(--primary-600);
        border-right: none;
    }

    .auth-modal .btn-outline-secondary {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(148, 163, 184, 0.2);
        color: var(--light-color);
        border-left: none;
    }

    .auth-modal .btn-outline-secondary:hover {
        background: rgba(255, 255, 255, 0.2);
        color: var(--primary-600);
    }

    .auth-modal .form-check-input {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(148, 163, 184, 0.2);
    }

    .auth-modal .form-check-input:checked {
        background: var(--primary-600);
        border-color: var(--primary-600);
    }

    .auth-modal .form-check-label {
        color: var(--light-color);
        font-size: var(--font-size-sm);
    }

    .auth-modal .btn-primary {
        background: var(--gradient-primary);
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: var(--border-radius-lg);
        font-weight: 600;
        transition: var(--transition);
    }

    .auth-modal .btn-primary:hover {
        background: var(--primary-dark);
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .auth-modal .btn-outline-primary {
        background: transparent;
        border: 2px solid var(--primary-600);
        color: var(--primary-600);
        padding: 0.5rem 1.5rem;
        border-radius: var(--border-radius-lg);
        font-weight: 600;
        transition: var(--transition);
    }

    .auth-modal .btn-outline-primary:hover {
        background: var(--primary-600);
        color: var(--light-color);
        transform: translateY(-2px);
    }

    .auth-modal hr {
        border-color: rgba(148, 163, 184, 0.2);
        margin: 1.5rem 0;
    }

    .auth-modal a {
        color: var(--primary-600);
        transition: var(--transition);
    }

    .auth-modal a:hover {
        color: var(--primary-light);
    }

    /* ===== Subscription Plans ===== */
    .subscription-modal {
        background: var(--gradient-surface);
        border: 1px solid rgba(148, 163, 184, 0.1);
        border-radius: var(--border-radius-2xl);
        box-shadow: var(--shadow-2xl);
        backdrop-filter: blur(20px);
    }

    .subscription-modal .modal-header {
        border-bottom: 1px solid rgba(148, 163, 184, 0.1);
        padding: 1.5rem;
    }

    .subscription-modal .modal-title {
        color: var(--light-color);
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .subscription-modal .modal-title i {
        color: #ffd700;
        font-size: 1.2rem;
    }

    .subscription-plans {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        padding: 1rem;
    }

    .plan-card {
        background: var(--gradient-dark);
        border: 2px solid rgba(148, 163, 184, 0.1);
        border-radius: var(--border-radius-2xl);
        padding: 2rem;
        text-align: center;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .plan-card:hover {
        transform: translateY(-10px);
        box-shadow: var(--shadow-2xl);
        border-color: var(--primary-600);
    }

    .plan-card.popular {
        border-color: #ffd700;
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
    }

    .popular-badge {
        position: absolute;
        top: -1px;
        left: 50%;
        transform: translateX(-50%);
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        color: #1a1a1a;
        padding: 0.5rem 1.5rem;
        border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
        font-size: var(--font-size-xs);
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .plan-header {
        margin-bottom: 2rem;
    }

    .plan-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 1.5rem;
        background: var(--gradient-primary);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: var(--light-color);
        box-shadow: var(--shadow-lg);
    }

    .free-plan .plan-icon {
        background: linear-gradient(135deg, #6b7280, #9ca3af);
    }

    .premium-plan .plan-icon {
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        color: #1a1a1a;
    }

    .vip-plan .plan-icon {
        background: linear-gradient(135deg, #9333ea, #c084fc);
    }

    .plan-name {
        color: var(--light-color);
        font-size: var(--font-size-xl);
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .plan-price {
        margin-bottom: 2rem;
    }

    .plan-price .price {
        font-size: 3rem;
        font-weight: 800;
        color: var(--light-color);
        line-height: 1;
    }

    .plan-price .currency {
        font-size: var(--font-size-lg);
        color: var(--primary-600);
        font-weight: 600;
        margin-left: 0.5rem;
    }

    .plan-price .period {
        font-size: var(--font-size-sm);
        color: var(--gray-400);
        font-weight: 500;
    }

    .plan-features ul {
        list-style: none;
        padding: 0;
        margin: 0 0 2rem 0;
        text-align: right;
    }

    .plan-features li {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 0;
        color: var(--light-color);
        font-size: var(--font-size-sm);
        border-bottom: 1px solid rgba(148, 163, 184, 0.1);
    }

    .plan-features li:last-child {
        border-bottom: none;
    }

    .plan-features li i {
        width: 20px;
        text-align: center;
        font-size: var(--font-size-sm);
    }

    .plan-features li .fa-check {
        color: #10b981;
    }

    .plan-features li .fa-times {
        color: #ef4444;
    }

    .btn-plan {
        background: var(--gradient-primary);
        color: var(--light-color);
        border: none;
        padding: 1rem 2rem;
        border-radius: var(--border-radius-xl);
        font-weight: 700;
        font-size: var(--font-size-base);
        transition: var(--transition);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        width: 100%;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .btn-plan:hover {
        background: var(--primary-dark);
        transform: translateY(-3px);
        box-shadow: var(--shadow-xl);
    }

    .btn-plan.current-plan {
        background: linear-gradient(135deg, #6b7280, #9ca3af);
        cursor: not-allowed;
        opacity: 0.7;
    }

    .btn-plan.current-plan:hover {
        transform: none;
        box-shadow: none;
    }

    .premium-plan .btn-plan {
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        color: #1a1a1a;
    }

    .premium-plan .btn-plan:hover {
        background: linear-gradient(135deg, #ffed4e, #ffd700);
        color: #1a1a1a;
    }

    .vip-plan .btn-plan {
        background: linear-gradient(135deg, #9333ea, #c084fc);
    }

    .vip-plan .btn-plan:hover {
        background: linear-gradient(135deg, #c084fc, #9333ea);
    }

    /* ===== Admin Dashboard ===== */
    .admin-modal {
        background: var(--gradient-surface);
        border: 1px solid rgba(147, 51, 234, 0.2);
        border-radius: var(--border-radius-2xl);
        box-shadow: var(--shadow-2xl);
        backdrop-filter: blur(20px);
    }

    .admin-modal .modal-header {
        border-bottom: 1px solid rgba(147, 51, 234, 0.2);
        padding: 1.5rem;
        background: linear-gradient(135deg, rgba(147, 51, 234, 0.1), rgba(168, 85, 247, 0.1));
    }

    .admin-modal .modal-title {
        color: var(--light-color);
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .admin-modal .modal-title i {
        color: #ffd700;
        font-size: 1.3rem;
    }

    .admin-dashboard {
        padding: 1rem;
    }

    .admin-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: var(--gradient-dark);
        border: 2px solid rgba(148, 163, 184, 0.1);
        border-radius: var(--border-radius-xl);
        padding: 1.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(147, 51, 234, 0.05), rgba(168, 85, 247, 0.05));
        opacity: 0;
        transition: var(--transition);
    }

    .stat-card:hover::before {
        opacity: 1;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-xl);
        border-color: #9333ea;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: var(--light-color);
        position: relative;
        z-index: 2;
    }

    .users-stat .stat-icon {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    }

    .movies-stat .stat-icon {
        background: linear-gradient(135deg, #ef4444, #dc2626);
    }

    .revenue-stat .stat-icon {
        background: linear-gradient(135deg, #10b981, #059669);
    }

    .subscriptions-stat .stat-icon {
        background: linear-gradient(135deg, #f59e0b, #d97706);
    }

    .stat-info {
        flex: 1;
        position: relative;
        z-index: 2;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 800;
        color: var(--light-color);
        margin: 0;
        line-height: 1;
    }

    .stat-label {
        color: var(--gray-400);
        font-size: var(--font-size-sm);
        margin: 0.25rem 0 0 0;
        font-weight: 500;
    }

    .admin-actions, .admin-activity {
        margin-bottom: 2rem;
    }

    .section-title {
        color: var(--light-color);
        font-size: var(--font-size-lg);
        font-weight: 600;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid rgba(147, 51, 234, 0.3);
    }

    .section-title i {
        color: #9333ea;
    }

    .action-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .btn-admin {
        background: linear-gradient(135deg, #9333ea, #7c3aed);
        color: var(--light-color);
        border: none;
        padding: 1rem 1.5rem;
        border-radius: var(--border-radius-xl);
        font-weight: 600;
        transition: var(--transition);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: var(--font-size-sm);
    }

    .btn-admin:hover {
        background: linear-gradient(135deg, #7c3aed, #6d28d9);
        transform: translateY(-3px);
        box-shadow: var(--shadow-xl);
        color: var(--light-color);
    }

    .activity-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .activity-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: var(--border-radius-lg);
        margin-bottom: 0.75rem;
        transition: var(--transition);
    }

    .activity-item:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateX(-5px);
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
        color: var(--light-color);
    }

    .user-activity {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    }

    .subscription-activity {
        background: linear-gradient(135deg, #f59e0b, #d97706);
    }

    .movie-activity {
        background: linear-gradient(135deg, #ef4444, #dc2626);
    }

    .activity-content {
        flex: 1;
    }

    .activity-text {
        color: var(--light-color);
        font-size: var(--font-size-sm);
        font-weight: 500;
        margin: 0;
    }

    .activity-time {
        color: var(--gray-400);
        font-size: var(--font-size-xs);
        font-weight: 400;
    }

    /* ===== Responsive User System ===== */
    @media (max-width: 768px) {
        .user-account-section {
            margin-left: 0.5rem;
        }

        .guest-section {
            gap: 0.5rem;
        }

        .btn-login, .btn-register {
            padding: 0.5rem 1rem;
            font-size: var(--font-size-xs);
        }

        .user-avatar img {
            width: 35px;
            height: 35px;
        }

        .user-details {
            display: none;
        }

        .subscription-plans {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .plan-card {
            padding: 1.5rem;
        }

        .plan-icon {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
        }

        .plan-price .price {
            font-size: 2.5rem;
        }

        .auth-modal .modal-body {
            padding: 1.5rem;
        }
    }

    @media (max-width: 576px) {
        .guest-section {
            flex-direction: column;
            gap: 0.5rem;
        }

        .btn-login, .btn-register {
            width: 100%;
            justify-content: center;
        }

        .subscription-plans {
            padding: 0.5rem;
        }

        .plan-card {
            padding: 1rem;
        }

        .popular-badge {
            font-size: 0.6rem;
            padding: 0.3rem 1rem;
        }

        /* Admin Dashboard Responsive */
        .admin-stats {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .action-buttons {
            grid-template-columns: 1fr;
        }

        .stat-card {
            padding: 1rem;
        }

        .stat-number {
            font-size: 1.5rem;
        }

        .admin-modal .modal-body {
            padding: 1rem;
        }
    }

    /* Mobile Navigation */
    .navbar-toggler {
        border: none;
        padding: 0.75rem;
        border-radius: var(--border-radius-lg);
        transition: var(--transition);
        background: rgba(255, 255, 255, 0.1);
    }

    .navbar-toggler:focus {
        box-shadow: none;
    }

    .navbar-toggler:hover {
        background: var(--primary-color);
        transform: scale(1.05);
    }

    .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    }

    @media (max-width: 1200px) {
        .navbar-nav {
            gap: 0.4rem;
        }

        .navbar-nav .nav-link {
            padding: 0.35rem 0.65rem;
            font-size: 0.8rem;
        }

        .search-input {
            width: 220px;
        }

        .search-input:focus {
            width: 260px;
        }
    }

    @media (max-width: 991.98px) {
        .navbar {
            padding: 0.5rem 0;
        }

        .navbar .container {
            justify-content: space-between;
            gap: 0.6rem;
            padding: 0 0.6rem;
        }

        .brand-main {
            font-size: 1.3rem;
        }

        .brand-sub {
            font-size: 0.65rem;
        }

        .navbar-nav .nav-link {
            padding: 0.6rem 0.8rem;
            font-size: 0.85rem;
        }

        .navbar-nav {
            display: none;
        }

        .search-form {
            margin: 0;
        }

        .search-input {
            width: 200px;
            height: 34px;
            padding: 0.45rem 2.3rem 0.45rem 0.75rem;
        }

        .search-input:focus {
            width: 240px;
        }

        .search-btn {
            width: 28px;
            height: 28px;
            font-size: 0.7rem;
        }

        .guest-section {
            gap: 0.4rem;
        }

        .btn-login, .btn-register {
            padding: 0.45rem 0.75rem;
            font-size: 0.75rem;
            height: 34px;
        }
    }

    @media (max-width: 768px) {
        .navbar .container {
            gap: 0.4rem;
            padding: 0 0.4rem;
        }

        .brand-main {
            font-size: 1.2rem;
        }

        .brand-sub {
            font-size: 0.6rem;
        }

        .navbar-nav .nav-link {
            padding: 0.5rem 0.7rem;
            font-size: 0.8rem;
        }

        .search-input {
            width: 160px;
            height: 32px;
            padding: 0.4rem 2.1rem 0.4rem 0.65rem;
            font-size: 0.8rem;
        }

        .search-input:focus {
            width: 190px;
        }

        .search-btn {
            width: 26px;
            height: 26px;
            font-size: 0.65rem;
        }

        .btn-login, .btn-register {
            padding: 0.4rem 0.65rem;
            font-size: 0.7rem;
            height: 32px;
        }
    }

    @media (max-width: 576px) {
        .navbar .container {
            gap: 0.3rem;
            padding: 0 0.3rem;
        }

        .brand-main {
            font-size: 1.1rem;
        }

        .brand-sub {
            font-size: 0.55rem;
        }

        .navbar-nav .nav-link {
            padding: 0.4rem 0.6rem;
            font-size: 0.75rem;
        }

        .search-input {
            width: 130px;
            padding: 0.35rem 2rem 0.35rem 0.6rem;
            font-size: 0.75rem;
            height: 30px;
        }

        .search-input:focus {
            width: 160px;
        }

        .search-btn {
            width: 24px;
            height: 24px;
            font-size: 0.6rem;
            right: 0.25rem;
        }

        .btn-login, .btn-register {
            padding: 0.35rem 0.6rem;
            font-size: 0.65rem;
            height: 30px;
            gap: 0.25rem;
        }

        .btn-login i, .btn-register i {
            font-size: 0.65rem;
        }
    }

    @media (max-width: 480px) {
        .navbar .container {
            gap: 0.25rem;
            padding: 0 0.25rem;
        }

        .brand-main {
            font-size: 1rem;
        }

        .brand-sub {
            font-size: 0.5rem;
        }

        .navbar-nav .nav-link {
            padding: 0.35rem 0.5rem;
            font-size: 0.7rem;
        }

        .search-input {
            width: 110px;
            height: 28px;
            padding: 0.3rem 1.8rem 0.3rem 0.5rem;
            font-size: 0.7rem;
        }

        .search-input:focus {
            width: 140px;
        }

        .search-btn {
            width: 22px;
            height: 22px;
            font-size: 0.55rem;
            right: 0.2rem;
        }

        .btn-login, .btn-register {
            padding: 0.3rem 0.5rem;
            font-size: 0.6rem;
            height: 28px;
        }

        .btn-login i, .btn-register i {
            display: none;
        }
    }

    /* ===== Hero Slider ===== */
    .hero-slider {
        height: 100vh;
        position: relative;
        margin-top: 0;
        padding-top: 60px;
        overflow: hidden;
    }

    .heroSwiper {
        width: 100%;
        height: 100%;
    }

    .hero-slide {
        height: 100vh;
        display: flex;
        align-items: center;
        position: relative;
        background-attachment: fixed;
        background-size: cover;
        background-position: center;
    }

    .hero-slide::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--gradient-overlay);
        z-index: 1;
    }

    .hero-content {
        position: relative;
        z-index: 2;
        max-width: 700px;
        padding: 2rem;
        animation: slideInRight 1s ease;
    }

    .hero-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: var(--gradient-primary);
        color: var(--light-color);
        padding: 0.5rem 1.5rem;
        border-radius: var(--border-radius-3xl);
        font-size: 0.875rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        box-shadow: var(--shadow-lg);
        animation: pulse 2s ease-in-out infinite;
    }

    .hero-badge i {
        font-size: 1rem;
    }

    .hero-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1.2rem;
        text-shadow: 2px 2px 8px rgba(0,0,0,0.8);
        line-height: 1.1;
        letter-spacing: -0.02em;
    }

    .hero-meta {
        display: flex;
        gap: 1.5rem;
        margin-bottom: 2rem;
        flex-wrap: wrap;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(255, 255, 255, 0.15);
        padding: 0.75rem 1.25rem;
        border-radius: var(--border-radius-3xl);
        font-size: 0.875rem;
        font-weight: 500;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: var(--transition);
    }

    .meta-item:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
    }

    .meta-item i {
        color: var(--primary-color);
        font-size: 1rem;
    }

    .hero-description {
        font-size: 1rem;
        margin-bottom: 2rem;
        opacity: 0.95;
        text-shadow: 1px 1px 4px rgba(0,0,0,0.8);
        line-height: 1.6;
        font-weight: 400;
    }

    .hero-buttons {
        display: flex;
        gap: 1.5rem;
        flex-wrap: wrap;
    }

    .btn-hero {
        padding: 1rem 2rem;
        border-radius: var(--border-radius-2xl);
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.6rem;
        transition: var(--transition);
        font-size: 0.95rem;
        border: none;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .btn-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: all 0.6s ease;
    }

    .btn-hero:hover::before {
        left: 100%;
    }

    .btn-hero.primary {
        background: var(--gradient-primary);
        color: var(--light-color);
        box-shadow: var(--shadow-xl);
    }

    .btn-hero.secondary {
        background: rgba(255, 255, 255, 0.2);
        color: var(--light-color);
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .btn-hero.tertiary {
        background: rgba(139, 0, 0, 0.8);
        color: var(--light-color);
        backdrop-filter: blur(10px);
        border: 2px solid rgba(220, 20, 60, 0.5);
    }

    .btn-hero:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: var(--shadow-2xl);
        color: var(--light-color);
    }

    .btn-hero i {
        font-size: 1.25rem;
    }

    /* Swiper Navigation */
    .swiper-button-next,
    .swiper-button-prev {
        color: var(--light-color);
        background: rgba(255, 255, 255, 0.1);
        width: 60px;
        height: 60px;
        border-radius: 50%;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: var(--transition);
    }

    .swiper-button-next:hover,
    .swiper-button-prev:hover {
        background: var(--gradient-primary);
        transform: scale(1.1);
        box-shadow: var(--shadow-lg);
    }

    .swiper-button-next::after,
    .swiper-button-prev::after {
        font-size: 1.5rem;
        font-weight: 700;
    }

    .swiper-pagination {
        bottom: 30px;
    }

    .swiper-pagination-bullet {
        width: 15px;
        height: 15px;
        background: rgba(255, 255, 255, 0.5);
        opacity: 1;
        transition: var(--transition);
    }

    .swiper-pagination-bullet-active {
        background: var(--primary-color);
        transform: scale(1.3);
        box-shadow: var(--shadow-glow);
    }

    /* Responsive Hero */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 1.8rem;
        }

        .hero-description {
            font-size: 0.9rem;
        }

        .hero-buttons {
            flex-direction: column;
            gap: 0.8rem;
        }

        .btn-hero {
            padding: 0.8rem 1.6rem;
            justify-content: center;
            font-size: 0.85rem;
        }

        .hero-meta {
            flex-direction: column;
            gap: 0.6rem;
        }

        .hero-content {
            padding: 1rem;
        }
    }

    @media (max-width: 576px) {
        .hero-title {
            font-size: 1.5rem;
        }

        .hero-description {
            font-size: 0.85rem;
        }

        .hero-content {
            padding: 0.5rem;
        }

        .meta-item {
            font-size: 0.7rem;
            padding: 0.4rem 0.8rem;
        }
    }

    /* ===== Categories Section ===== */
    .categories-section {
        padding: 5rem 0;
        background: linear-gradient(135deg, var(--darker-color) 0%, var(--dark-color) 100%);
    }

    .section-header {
        text-align: center;
        margin-bottom: 4rem;
    }

    .section-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--light-color);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.8rem;
        margin-bottom: 1rem;
        letter-spacing: -0.02em;
    }

    .section-title i {
        color: var(--primary-color);
        font-size: 2rem;
        animation: float 3s ease-in-out infinite;
    }

    .section-subtitle {
        font-size: 1rem;
        color: var(--gray-light);
        margin: 0;
        font-weight: 400;
    }

    .categories-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
    }

    .category-card {
        background: var(--gradient-surface);
        border-radius: var(--border-radius-2xl);
        padding: 1.8rem 1.5rem;
        text-align: center;
        cursor: pointer;
        transition: var(--transition);
        border: 2px solid rgba(148, 163, 184, 0.1);
        position: relative;
        overflow: hidden;
        box-shadow: var(--shadow-lg);
    }

    .category-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--gradient-primary);
        opacity: 0;
        transition: var(--transition);
        z-index: 1;
    }

    .category-card:hover::before {
        opacity: 0.1;
    }

    .category-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: var(--shadow-2xl), var(--shadow-glow);
        border-color: var(--primary-600);
    }





    .category-card.series {
        border-color: #17a2b8;
    }

    .category-card.series::before {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    }

    .category-card.series:hover {
        border-color: #20c997;
        box-shadow: 0 20px 40px rgba(23, 162, 184, 0.3);
    }

    .category-card.anime {
        border-color: #fd7e14;
    }

    .category-card.anime::before {
        background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
    }

    .category-card.anime:hover {
        border-color: #ff8c00;
        box-shadow: 0 20px 40px rgba(253, 126, 20, 0.3);
    }

    .category-card.disabled {
        border-color: rgba(148, 163, 184, 0.2);
        opacity: 0.7;
        cursor: not-allowed;
    }

    .category-card.disabled::before {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    }

    .category-card.disabled:hover {
        border-color: rgba(148, 163, 184, 0.3);
        box-shadow: 0 10px 30px rgba(107, 114, 128, 0.2);
        transform: translateY(-5px);
    }

    .category-icon {
        position: relative;
        z-index: 2;
        width: 60px;
        height: 60px;
        background: var(--gradient-primary);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.2rem;
        transition: var(--transition);
        box-shadow: var(--shadow-lg);
    }

    .category-card.horror .category-icon {
        background: var(--gradient-horror);
    }

    .category-card.horror {
        border-color: var(--horror-color);
    }

    .category-card.horror::before {
        background: var(--gradient-horror);
    }

    .category-card.horror:hover {
        border-color: var(--horror-light);
        box-shadow: 0 20px 40px rgba(139, 0, 0, 0.3);
    }

    .category-card.series .category-icon {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    }

    .category-card.anime .category-icon {
        background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
    }

    .category-card.disabled .category-icon {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
    }

    .category-icon i {
        font-size: 1.8rem;
        color: var(--light-color);
    }

    .category-card:hover .category-icon {
        transform: scale(1.1) rotate(5deg);
        box-shadow: var(--shadow-2xl);
    }

    .category-title {
        position: relative;
        z-index: 2;
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--light-color);
        margin-bottom: 0.4rem;
        transition: var(--transition);
    }

    .category-count {
        position: relative;
        z-index: 2;
        font-size: 0.85rem;
        color: var(--gray-light);
        margin-bottom: 1.2rem;
        font-weight: 500;
    }

    .category-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        padding: 1rem;
        background: var(--gradient-primary);
        color: var(--light-color);
        font-weight: 600;
        transform: translateY(100%);
        transition: var(--transition);
        z-index: 3;
    }

    .category-card.horror .category-overlay {
        background: var(--gradient-horror);
    }

    .category-card.horror.disabled .category-overlay {
        background: linear-gradient(135deg, rgba(139, 0, 0, 0.6) 0%, rgba(100, 0, 0, 0.6) 100%);
    }

    .category-card.series .category-overlay {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    }

    .category-card.anime .category-overlay {
        background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
    }

    .category-card.disabled .category-overlay {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    }

    .category-card.disabled .category-title {
        color: #9ca3af;
    }

    .category-card.disabled .category-count {
        color: #6b7280;
    }

    .category-card:hover .category-overlay {
        transform: translateY(0);
    }

    /* ===== Content Sections ===== */
    .content-section {
        padding: 4rem 0;
        background: var(--dark-color);
    }

    .content-section:nth-child(even) {
        background: var(--darker-color);
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 3rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid rgba(255, 255, 255, 0.1);
        position: relative;
    }

    .section-header::after {
        content: '';
        position: absolute;
        bottom: -2px;
        right: 0;
        width: 150px;
        height: 2px;
        background: var(--gradient-primary);
        border-radius: var(--border-radius);
    }

    .view-all-btn {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 600;
        transition: var(--transition);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border-radius: var(--border-radius-2xl);
        border: 2px solid var(--primary-color);
        background: transparent;
        font-size: 1rem;
    }

    .view-all-btn:hover {
        color: var(--light-color);
        background: var(--gradient-primary);
        transform: translateX(-5px) scale(1.05);
        box-shadow: var(--shadow-lg);
    }

    /* ===== Movies Grid ===== */
    .movies-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 2rem;
        margin: 2rem 0;
    }

    .movie-card {
        background: var(--surface-color);
        border-radius: var(--border-radius-2xl);
        overflow: hidden;
        transition: var(--transition);
        cursor: pointer;
        border: 2px solid rgba(148, 163, 184, 0.1);
        position: relative;
        box-shadow: var(--shadow-lg);
    }

    .movie-card:hover {
        transform: translateY(-15px) scale(1.03);
        box-shadow: var(--shadow-2xl), var(--shadow-glow);
        border-color: var(--primary-600);
    }

    .movie-poster {
        position: relative;
        height: 300px;
        overflow: hidden;
        border-radius: var(--border-radius-2xl) var(--border-radius-2xl) 0 0;
    }

    .movie-poster img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: all 0.5s ease;
    }

    .movie-card:hover .movie-poster img {
        transform: scale(1.1);
    }

    .movie-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: var(--transition);
        backdrop-filter: blur(5px);
    }

    .movie-card:hover .movie-overlay {
        opacity: 1;
    }

    .play-btn {
        background: var(--gradient-primary);
        color: var(--light-color);
        border: none;
        width: 70px;
        height: 70px;
        border-radius: 50%;
        font-size: 1.8rem;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: var(--transition);
        cursor: pointer;
        box-shadow: var(--shadow-xl);
    }

    .play-btn:hover {
        background: var(--primary-dark);
        transform: scale(1.2);
        box-shadow: var(--shadow-2xl);
    }

    .quality-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: var(--gradient-primary);
        color: var(--light-color);
        padding: 0.5rem 1rem;
        border-radius: var(--border-radius-lg);
        font-size: 0.875rem;
        font-weight: 700;
        box-shadow: var(--shadow-md);
        text-transform: uppercase;
    }

    .movie-info {
        padding: 1.2rem;
    }

    .movie-title {
        font-size: 0.95rem;
        font-weight: 600;
        margin-bottom: 0.8rem;
        color: var(--light-color);
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        min-height: 2.4rem;
    }

    .movie-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.75rem;
        color: var(--gray-color);
    }

    .movie-year {
        background: rgba(255, 255, 255, 0.1);
        padding: 0.25rem 0.75rem;
        border-radius: var(--border-radius-lg);
        font-weight: 600;
    }

    .movie-rating {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-weight: 600;
    }

    .movie-rating i {
        color: #ffd700;
        font-size: 1rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .categories-grid {
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 1.2rem;
        }

        .category-card {
            padding: 1.5rem 1.2rem;
        }

        .movies-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .movie-poster {
            height: 250px;
        }

        .section-title {
            font-size: 1.5rem;
            flex-direction: column;
            gap: 0.4rem;
        }

        .section-header {
            flex-direction: column;
            gap: 0.8rem;
            text-align: center;
        }
    }

    @media (max-width: 576px) {
        .categories-grid {
            grid-template-columns: 1fr;
        }

        .movies-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 0.6rem;
        }

        .movie-poster {
            height: 200px;
        }

        .movie-info {
            padding: 0.8rem;
        }

        .category-card {
            padding: 1.2rem 0.8rem;
        }

        .category-icon {
            width: 50px;
            height: 50px;
        }

        .category-icon i {
            font-size: 1.5rem;
        }

        .category-title {
            font-size: 1rem;
        }

        .category-count {
            font-size: 0.75rem;
        }
    }

    /* ===== Footer ===== */
    .footer {
        background: var(--gradient-dark);
        padding: 3rem 0 1.5rem;
        margin-top: 3rem;
        border-top: 2px solid var(--primary-600);
        position: relative;
    }

    .footer::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background: var(--gradient-primary);
    }

    .footer-widget {
        margin-bottom: 2rem;
    }



    .footer-title {
        color: var(--light-color);
        font-weight: 600;
        margin-bottom: 1rem;
        font-size: 0.95rem;
        position: relative;
        padding-bottom: 0.3rem;
        border-bottom: 2px solid var(--primary-600);
        display: inline-block;
    }

    .footer-title::after {
        content: '';
        position: absolute;
        bottom: 0;
        right: 0;
        width: 50px;
        height: 2px;
        background: var(--gradient-primary);
        border-radius: var(--border-radius);
    }

    .footer-brand-subtitle-white {
        color: var(--light-color) !important;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .footer-description {
        color: var(--gray-light);
        line-height: 1.6;
        margin-bottom: 1.8rem;
        font-size: 0.85rem;
    }

    .footer-links {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .footer-links li {
        margin-bottom: 0.75rem;
    }

    .footer-links a {
        color: var(--gray-light);
        text-decoration: none;
        transition: var(--transition);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.2rem 0;
        font-size: 0.8rem;
    }

    .footer-links a:hover {
        color: var(--primary-color);
        transform: translateX(-3px);
    }

    .footer-links a i {
        width: 15px;
        text-align: center;
        font-size: 0.75rem;
        color: var(--primary-color);
    }

    .social-links {
        display: flex;
        gap: 1rem;
        margin-top: 1.5rem;
    }

    .social-link {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 35px;
        height: 35px;
        background: rgba(255, 255, 255, 0.1);
        color: var(--light-color);
        border-radius: 50%;
        text-decoration: none;
        transition: var(--transition);
        font-size: 0.9rem;
        border: 2px solid rgba(229, 9, 20, 0.3);
    }

    .social-link:hover {
        background: var(--gradient-primary);
        color: var(--light-color);
        transform: translateY(-3px) scale(1.1);
        border-color: var(--primary-color);
        box-shadow: var(--shadow-lg);
    }

    .newsletter-text {
        color: var(--gray-light);
        margin-bottom: 1rem;
        font-size: 0.8rem;
        line-height: 1.5;
    }

    .newsletter-form .input-group {
        margin-bottom: 1.2rem;
    }

    .newsletter-input {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        color: var(--light-color);
        border-radius: var(--border-radius-lg) 0 0 var(--border-radius-lg);
        padding: 0.6rem 0.8rem;
        font-size: 0.8rem;
        transition: var(--transition);
    }

    .newsletter-input:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: var(--primary-color);
        color: var(--light-color);
        box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
        outline: none;
    }

    .newsletter-input::placeholder {
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.75rem;
    }

    .newsletter-btn {
        background: var(--gradient-primary);
        border: none;
        border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;
        padding: 0.6rem 1.2rem;
        font-weight: 600;
        transition: var(--transition);
        color: var(--light-color);
        font-size: 0.85rem;
    }

    .newsletter-btn:hover {
        background: var(--primary-dark);
        transform: scale(1.05);
        box-shadow: var(--shadow-lg);
        color: var(--light-color);
    }

    .newsletter-btn i {
        font-size: 0.8rem;
    }

    .app-download {
        margin-top: 2rem;
    }

    .app-text {
        color: var(--light-color);
        font-weight: 600;
        margin-bottom: 1rem;
        font-size: 1rem;
    }

    .app-buttons {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .app-btn {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        background: rgba(255, 255, 255, 0.1);
        color: var(--light-color);
        padding: 0.75rem 1rem;
        border-radius: var(--border-radius-lg);
        text-decoration: none;
        transition: var(--transition);
        border: 2px solid rgba(255, 255, 255, 0.2);
        font-size: 0.875rem;
    }

    .app-btn:hover {
        background: var(--gradient-primary);
        color: var(--light-color);
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .app-btn i {
        font-size: 1.2rem;
    }

    .footer-divider {
        border: none;
        height: 1px;
        background: rgba(255, 255, 255, 0.1);
        margin: 3rem 0 2rem;
    }

    .footer-bottom {
        padding-top: 1rem;
    }

    .copyright {
        color: var(--gray-color);
        margin: 0;
        font-size: 0.875rem;
    }

    .footer-bottom-links {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        gap: 2rem;
        justify-content: flex-end;
    }

    .footer-bottom-links a {
        color: var(--gray-color);
        text-decoration: none;
        font-size: 0.875rem;
        transition: var(--transition);
    }

    .footer-bottom-links a:hover {
        color: var(--primary-color);
    }

    .text-gradient {
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* ===== Back to Top Button ===== */
    .back-to-top {
        position: fixed;
        bottom: 2rem;
        left: 2rem;
        width: 55px;
        height: 55px;
        background: var(--gradient-primary);
        color: var(--light-color);
        border: none;
        border-radius: 50%;
        font-size: 1.3rem;
        cursor: pointer;
        transition: var(--transition);
        z-index: var(--z-fixed);
        opacity: 0;
        visibility: hidden;
        transform: translateY(20px);
        box-shadow: var(--shadow-xl);
    }

    .back-to-top.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .back-to-top:hover {
        background: var(--primary-dark);
        transform: translateY(-5px) scale(1.1);
        box-shadow: var(--shadow-2xl);
    }

    /* ===== Responsive Footer ===== */
    @media (max-width: 768px) {
        .footer {
            padding: 2rem 0 1rem;
        }

        .footer-bottom-links {
            justify-content: center;
            margin-top: 1rem;
            gap: 1rem;
        }

        .footer-bottom {
            text-align: center;
        }

        .social-links {
            justify-content: center;
            gap: 0.6rem;
        }

        .social-link {
            width: 30px;
            height: 30px;
            font-size: 0.8rem;
        }

        .app-buttons {
            align-items: center;
        }

        .app-btn {
            max-width: 200px;
            font-size: 0.75rem;
        }

        .footer-widget {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .footer-title {
            display: block;
            text-align: center;
        }

        .newsletter-input {
            padding: 0.5rem 0.7rem;
            font-size: 0.75rem;
        }

        .newsletter-btn {
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
        }

        .newsletter-text {
            font-size: 0.75rem;
        }
    }

    @media (max-width: 576px) {
        .footer {
            padding: 3rem 0 1.5rem;
        }

        .footer-widget {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .footer-title::after {
            left: 50%;
            transform: translateX(-50%);
        }

        .footer-bottom-links {
            flex-direction: column;
            gap: 0.5rem;
        }

        .back-to-top {
            bottom: 1rem;
            left: 1rem;
            width: 50px;
            height: 50px;
        }
    }
    </style>
</head>

<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-content">
            <div class="loading-logo">
                <div class="loading-text">
                    <span class="brand-text">CinemaHub Pro</span>
                    <span class="brand-subtitle">سينما هاب برو</span>
                    <span class="brand-tagline">تجربة مشاهدة لا تُنسى</span>
                </div>
            </div>
            <div class="spinner"></div>
            <p>جاري تحميل أحدث الأفلام والمسلسلات...</p>
        </div>
    </div>



    <!-- Modern Header -->
    <header class="modern-header" id="header">
        <div class="header-container">
            <!-- Logo -->
            <a href="index-pro.html" class="header-logo">
                <div class="logo-icon">
                    <i class="fas fa-film"></i>
                </div>
                <div class="logo-text">
                    <h1>CinemaHub</h1>
                    <p>أفضل الأفلام والمسلسلات</p>
                </div>
            </a>

            <!-- Navigation -->
            <nav class="header-nav">
                <div class="nav-item">
                    <a href="index-pro.html" class="nav-link active">
                        <i class="fas fa-home"></i>
                        الرئيسية
                    </a>
                </div>

                <div class="nav-item dropdown" id="moviesDropdown">
                    <a href="#" class="nav-link dropdown-toggle" onclick="showMoviesMenu()">
                        <i class="fas fa-film"></i>
                        الأفلام
                    </a>
                    <div class="dropdown-menu" id="moviesMenu" style="display: none;">
                        <a href="movies.html" class="dropdown-item">
                            <i class="fas fa-list"></i>
                            جميع الأفلام
                        </a>
                        <a href="movies-action.html" class="dropdown-item">
                            <i class="fas fa-fist-raised"></i>
                            أفلام أكشن
                        </a>
                        <a href="movies-comedy.html" class="dropdown-item">
                            <i class="fas fa-laugh"></i>
                            أفلام كوميديا
                        </a>
                        <a href="movies-romance.html" class="dropdown-item">
                            <i class="fas fa-heart"></i>
                            أفلام رومانسية
                        </a>
                        <a href="movies-horror.html" class="dropdown-item">
                            <i class="fas fa-mask"></i>
                            أفلام رعب
                        </a>
                        <a href="movies-scifi.html" class="dropdown-item">
                            <i class="fas fa-rocket"></i>
                            خيال علمي
                        </a>
                        <a href="movies-drama.html" class="dropdown-item">
                            <i class="fas fa-theater-masks"></i>
                            أفلام دراما
                        </a>
                        <a href="movies-animation.html" class="dropdown-item">
                            <i class="fas fa-magic"></i>
                            أفلام كرتون
                        </a>
                    </div>
                </div>

                <div class="nav-item dropdown" id="seriesDropdown">
                    <a href="#" class="nav-link dropdown-toggle" onclick="showSeriesMenu()">
                        <i class="fas fa-tv"></i>
                        المسلسلات
                    </a>
                    <div class="dropdown-menu" id="seriesMenu" style="display: none;">
                        <a href="series.html" class="dropdown-item">
                            <i class="fas fa-list"></i>
                            جميع المسلسلات
                        </a>
                        <a href="series-arabic.html" class="dropdown-item">
                            <i class="fas fa-flag"></i>
                            مسلسلات عربية
                        </a>
                        <a href="series-turkish.html" class="dropdown-item">
                            <i class="fas fa-star-and-crescent"></i>
                            مسلسلات تركية
                        </a>
                        <a href="series-korean.html" class="dropdown-item">
                            <i class="fas fa-yin-yang"></i>
                            مسلسلات كورية
                        </a>
                        <a href="series-american.html" class="dropdown-item">
                            <i class="fas fa-flag-usa"></i>
                            مسلسلات أمريكية
                        </a>
                        <a href="series-anime.html" class="dropdown-item">
                            <i class="fas fa-dragon"></i>
                            أنمي
                        </a>
                    </div>
                </div>

                <div class="nav-item">
                    <a href="top-rated.html" class="nav-link">
                        <i class="fas fa-star"></i>
                        الأعلى تقييماً
                    </a>
                </div>

                <div class="nav-item">
                    <a href="latest.html" class="nav-link">
                        <i class="fas fa-clock"></i>
                        أحدث الإضافات
                    </a>
                </div>
            </nav>

            <!-- Search -->
            <div class="header-search">
                <form onsubmit="handleSearch(event)">
                    <input type="search" class="search-input" placeholder="ابحث عن فيلم أو مسلسل..." id="searchInput" />
                    <button type="submit" class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>

            <!-- User Section -->
            <div class="header-user" id="headerUser">
                <!-- Guest Section -->
                <div class="guest-section" id="guestSection">
                    <button class="btn-login" onclick="showLoginModal()">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل الدخول
                    </button>
                    <button class="btn-register" onclick="showRegisterModal()">
                        <i class="fas fa-user-plus"></i>
                        إنشاء حساب
                    </button>
                </div>

                <!-- User Section (Hidden by default) -->
                <div class="user-section d-none" id="userSection">
                    <div class="user-dropdown-container">
                        <div class="user-avatar" onclick="toggleUserDropdown()">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&amp;h=150&amp;fit=crop&amp;crop=face&amp;auto=format&amp;q=80" alt="User" id="userAvatarImg" />
                            <div class="user-details">
                                <span class="user-name" id="userName">المستخدم</span>
                                <span class="user-plan" id="userPlan">مجاني</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-arrow"></i>
                        </div>
                        <div class="user-dropdown" id="userDropdownMenu">
                            <div class="dropdown-item" onclick="showProfileModal()">
                                <i class="fas fa-user"></i> الملف الشخصي
                            </div>
                            <div class="dropdown-item" onclick="showSubscriptionModal()">
                                <i class="fas fa-crown"></i> الباقات والاشتراكات
                            </div>
                            <div class="dropdown-item" onclick="showWatchlistModal()">
                                <i class="fas fa-heart"></i> قائمة المفضلة
                            </div>
                            <div class="dropdown-item" onclick="showHistoryModal()">
                                <i class="fas fa-history"></i> سجل المشاهدة
                            </div>

                            <!-- Admin Panel (Only for Admin) -->
                            <div class="admin-section d-none" id="adminSection">
                                <div class="dropdown-divider"></div>
                                <div class="dropdown-header">
                                    <i class="fas fa-crown text-warning"></i> لوحة الإدارة
                                </div>
                                <div class="dropdown-item admin-item" onclick="showAdminDashboard()">
                                    <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                                </div>
                                <div class="dropdown-item admin-item" onclick="showUsersManagement()">
                                    <i class="fas fa-users"></i> إدارة المستخدمين
                                </div>
                                <div class="dropdown-item admin-item" onclick="showContentManagement()">
                                    <i class="fas fa-film"></i> إدارة المحتوى
                                </div>
                                <div class="dropdown-item admin-item" onclick="showAnalytics()">
                                    <i class="fas fa-chart-bar"></i> الإحصائيات
                                </div>
                            </div>

                            <div class="dropdown-divider"></div>
                            <div class="dropdown-item" onclick="showSettingsModal()">
                                <i class="fas fa-cog"></i> الإعدادات
                            </div>
                            <div class="dropdown-item text-danger" onclick="logout()">
                                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Slider Section -->
    <section class="hero-slider" id="home">
        <div class="swiper heroSwiper">
            <div class="swiper-wrapper">
                <!-- Slide 1 -->
                <div class="swiper-slide">
                    <div class="hero-slide" style="background: linear-gradient(45deg, rgba(220,38,38,0.2) 0%, rgba(15,23,42,0.85) 100%), url('https://image.tmdb.org/t/p/w1280/s16H6tpK2utvwDtzZ8Qy4qm5Emw.jpg') center/cover;">
                        <div class="container">
                            <div class="hero-content" data-aos="fade-up">
                                <div class="hero-badge">
                                    <i class="fas fa-fire"></i>
                                    <span>الأكثر مشاهدة</span>
                                </div>
                                <h1 class="hero-title">أفاتار: طريق الماء</h1>
                                <div class="hero-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-calendar"></i>
                                        <span>2022</span>
                                    </div>
                                    <div class="meta-item">
                                        <i class="fas fa-star"></i>
                                        <span>7.7/10</span>
                                    </div>
                                    <div class="meta-item">
                                        <i class="fas fa-clock"></i>
                                        <span>3 ساعات 12 دقيقة</span>
                                    </div>
                                    <div class="meta-item">
                                        <i class="fas fa-film"></i>
                                        <span>خيال علمي، مغامرة</span>
                                    </div>
                                </div>
                                <p class="hero-description">بعد أكثر من عقد من الأحداث الأولى، تعلم عائلة سولي المشاكل التي تتبعهم والأطوال التي سيذهبون إليها للحفاظ على سلامة بعضهم البعض والمعارك التي يخوضونها للبقاء على قيد الحياة.</p>
                                <div class="hero-buttons">
                                    <button class="btn-hero primary" onclick="watchMovie(1, 'أفاتار: طريق الماء')">
                                        <i class="fas fa-play"></i>
                                        مشاهدة الآن
                                    </button>
                                    <button class="btn-hero secondary" onclick="openMovieDetails(1, 'أفاتار: طريق الماء')">
                                        <i class="fas fa-info-circle"></i>
                                        المزيد من المعلومات
                                    </button>
                                    <button class="btn-hero tertiary" onclick="addToFavorites(1, 'أفاتار: طريق الماء')">
                                        <i class="fas fa-heart"></i>
                                        إضافة للمفضلة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 2 -->
                <div class="swiper-slide">
                    <div class="hero-slide" style="background: linear-gradient(45deg, rgba(220,38,38,0.2) 0%, rgba(15,23,42,0.85) 100%), url('https://image.tmdb.org/t/p/w1280/1g0dhYtq4irTY1GPXvft6k4YLjm.jpg') center/cover;">
                        <div class="container">
                            <div class="hero-content" data-aos="fade-up">
                                <div class="hero-badge">
                                    <i class="fas fa-crown"></i>
                                    <span>الأعلى تقييماً</span>
                                </div>
                                <h1 class="hero-title">الرجل العنكبوت: لا طريق للعودة</h1>
                                <div class="hero-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-calendar"></i>
                                        <span>2021</span>
                                    </div>
                                    <div class="meta-item">
                                        <i class="fas fa-star"></i>
                                        <span>8.4/10</span>
                                    </div>
                                    <div class="meta-item">
                                        <i class="fas fa-clock"></i>
                                        <span>2 ساعة 28 دقيقة</span>
                                    </div>
                                    <div class="meta-item">
                                        <i class="fas fa-film"></i>
                                        <span>أكشن، مغامرة</span>
                                    </div>
                                </div>
                                <p class="hero-description">مع كشف هوية الرجل العنكبوت، لم يعد بيتر باركر قادراً على فصل حياته الطبيعية عن المخاطر العالية لكونه بطلاً خارقاً. عندما يطلب المساعدة من الدكتور سترينج، تصبح المخاطر أكثر خطورة.</p>
                                <div class="hero-buttons">
                                    <button class="btn-hero primary" onclick="watchMovie(2, 'الرجل العنكبوت: لا طريق للعودة')">
                                        <i class="fas fa-play"></i>
                                        مشاهدة الآن
                                    </button>
                                    <button class="btn-hero secondary" onclick="openMovieDetails(2, 'الرجل العنكبوت: لا طريق للعودة')">
                                        <i class="fas fa-info-circle"></i>
                                        المزيد من المعلومات
                                    </button>
                                    <button class="btn-hero tertiary" onclick="addToFavorites(2, 'الرجل العنكبوت: لا طريق للعودة')">
                                        <i class="fas fa-heart"></i>
                                        إضافة للمفضلة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 3 -->
                <div class="swiper-slide">
                    <div class="hero-slide" style="background: linear-gradient(45deg, rgba(220,38,38,0.2) 0%, rgba(15,23,42,0.85) 100%), url('https://image.tmdb.org/t/p/w1280/z2yahl2uefxDCl0nogcRBstwruJ.jpg') center/cover;">
                        <div class="container">
                            <div class="hero-content" data-aos="fade-up">
                                <div class="hero-badge">
                                    <i class="fas fa-tv"></i>
                                    <span>مسلسل جديد</span>
                                </div>
                                <h1 class="hero-title">بيت التنين</h1>
                                <div class="hero-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-calendar"></i>
                                        <span>2022</span>
                                    </div>
                                    <div class="meta-item">
                                        <i class="fas fa-star"></i>
                                        <span>8.5/10</span>
                                    </div>
                                    <div class="meta-item">
                                        <i class="fas fa-tv"></i>
                                        <span>10 حلقات</span>
                                    </div>
                                    <div class="meta-item">
                                        <i class="fas fa-film"></i>
                                        <span>دراما، فانتازيا</span>
                                    </div>
                                </div>
                                <p class="hero-description">مسلسل درامي فانتازي يحكي قصة آل تارغاريان قبل 200 عام من أحداث صراع العروش. يركز على الحرب الأهلية التي مزقت عائلة تارغاريان والمعروفة باسم رقصة التنانين.</p>
                                <div class="hero-buttons">
                                    <button class="btn-hero primary" onclick="watchSeries(1, 'بيت التنين')">
                                        <i class="fas fa-play"></i>
                                        مشاهدة الآن
                                    </button>
                                    <button class="btn-hero secondary" onclick="openSeriesDetails(1, 'بيت التنين')">
                                        <i class="fas fa-info-circle"></i>
                                        المزيد من المعلومات
                                    </button>
                                    <button class="btn-hero tertiary" onclick="addToFavorites(1, 'بيت التنين', 'series')">
                                        <i class="fas fa-heart"></i>
                                        إضافة للمفضلة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="swiper-button-next"></div>
            <div class="swiper-button-prev"></div>
            <div class="swiper-pagination"></div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="categories-section" id="categories">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2 class="section-title">
                    <i class="fas fa-th-large"></i>
                    تصفح حسب التصنيف
                </h2>
                <p class="section-subtitle">اكتشف المحتوى المفضل لديك من خلال التصنيفات المختلفة</p>
            </div>

            <div class="categories-grid" data-aos="fade-up" data-aos-delay="200">
                <!-- Movie Categories -->
                <div class="category-card" onclick="goToCategory('movies-action.html')">
                    <div class="category-icon">
                        <i class="fas fa-fist-raised"></i>
                    </div>
                    <h3 class="category-title">أفلام أكشن</h3>
                    <p class="category-count">120+ فيلم</p>
                    <div class="category-overlay">
                        <span>استكشف الآن</span>
                    </div>
                </div>

                <div class="category-card" onclick="goToCategory('movies-comedy.html')">
                    <div class="category-icon">
                        <i class="fas fa-laugh"></i>
                    </div>
                    <h3 class="category-title">أفلام كوميديا</h3>
                    <p class="category-count">85+ فيلم</p>
                    <div class="category-overlay">
                        <span>استكشف الآن</span>
                    </div>
                </div>

                <div class="category-card horror" onclick="goToCategory('movies-horror.html')">
                    <div class="category-icon">
                        <i class="fas fa-mask"></i>
                    </div>
                    <h3 class="category-title">أفلام رعب</h3>
                    <p class="category-count">65+ فيلم</p>
                    <div class="category-overlay">
                        <span>استكشف الآن</span>
                    </div>
                </div>

                <div class="category-card" onclick="goToCategory('movies-romance.html')">
                    <div class="category-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3 class="category-title">أفلام رومانسية</h3>
                    <p class="category-count">95+ فيلم</p>
                    <div class="category-overlay">
                        <span>استكشف الآن</span>
                    </div>
                </div>

                <div class="category-card" onclick="goToCategory('movies-scifi.html')">
                    <div class="category-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h3 class="category-title">خيال علمي</h3>
                    <p class="category-count">75+ فيلم</p>
                    <div class="category-overlay">
                        <span>استكشف الآن</span>
                    </div>
                </div>

                <div class="category-card" onclick="goToCategory('movies-drama.html')">
                    <div class="category-icon">
                        <i class="fas fa-theater-masks"></i>
                    </div>
                    <h3 class="category-title">أفلام دراما</h3>
                    <p class="category-count">110+ فيلم</p>
                    <div class="category-overlay">
                        <span>استكشف الآن</span>
                    </div>
                </div>

                <!-- Series Categories -->
                <div class="category-card series" onclick="goToCategory('series-arabic.html')">
                    <div class="category-icon">
                        <i class="fas fa-flag"></i>
                    </div>
                    <h3 class="category-title">مسلسلات عربية</h3>
                    <p class="category-count">45+ مسلسل</p>
                    <div class="category-overlay">
                        <span>استكشف الآن</span>
                    </div>
                </div>

                <div class="category-card series" onclick="goToCategory('series-turkish.html')">
                    <div class="category-icon">
                        <i class="fas fa-star-and-crescent"></i>
                    </div>
                    <h3 class="category-title">مسلسلات تركية</h3>
                    <p class="category-count">60+ مسلسل</p>
                    <div class="category-overlay">
                        <span>استكشف الآن</span>
                    </div>
                </div>

                <div class="category-card series" onclick="goToCategory('series-korean.html')">
                    <div class="category-icon">
                        <i class="fas fa-yin-yang"></i>
                    </div>
                    <h3 class="category-title">مسلسلات كورية</h3>
                    <p class="category-count">35+ مسلسل</p>
                    <div class="category-overlay">
                        <span>استكشف الآن</span>
                    </div>
                </div>

                <div class="category-card series" onclick="goToCategory('series-american.html')">
                    <div class="category-icon">
                        <i class="fas fa-flag-usa"></i>
                    </div>
                    <h3 class="category-title">مسلسلات أمريكية</h3>
                    <p class="category-count">80+ مسلسل</p>
                    <div class="category-overlay">
                        <span>استكشف الآن</span>
                    </div>
                </div>

                <div class="category-card anime" onclick="goToCategory('series-anime.html')">
                    <div class="category-icon">
                        <i class="fas fa-dragon"></i>
                    </div>
                    <h3 class="category-title">أنمي</h3>
                    <p class="category-count">50+ مسلسل</p>
                    <div class="category-overlay">
                        <span>استكشف الآن</span>
                    </div>
                </div>

                <div class="category-card" onclick="goToCategory('movies-animation.html')">
                    <div class="category-icon">
                        <i class="fas fa-magic"></i>
                    </div>
                    <h3 class="category-title">أفلام كرتون</h3>
                    <p class="category-count">40+ فيلم</p>
                    <div class="category-overlay">
                        <span>استكشف الآن</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Latest Movies Section -->
    <section class="content-section" id="latest-movies">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2 class="section-title">
                    <i class="fas fa-film"></i>
                    أحدث الأفلام
                </h2>
                <a href="movies.html" class="view-all-btn">
                    عرض الكل <i class="fas fa-arrow-left"></i>
                </a>
            </div>
            <div class="movies-grid" id="latest-movies-grid" data-aos="fade-up" data-aos-delay="200">
                <!-- Movies will be loaded dynamically -->
            </div>
        </div>
    </section>

    <!-- Latest Series Section -->
    <section class="content-section" id="latest-series">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2 class="section-title">
                    <i class="fas fa-tv"></i>
                    أحدث المسلسلات
                </h2>
                <a href="series.html" class="view-all-btn">
                    عرض الكل <i class="fas fa-arrow-left"></i>
                </a>
            </div>
            <div class="movies-grid" id="latest-series-grid" data-aos="fade-up" data-aos-delay="200">
                <!-- Series will be loaded dynamically -->
            </div>
        </div>
    </section>

    <!-- Top Rated Section -->
    <section class="content-section" id="top-rated">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2 class="section-title">
                    <i class="fas fa-star"></i>
                    الأعلى تقييماً
                </h2>
                <a href="top-rated.html" class="view-all-btn">
                    عرض الكل <i class="fas fa-arrow-left"></i>
                </a>
            </div>
            <div class="movies-grid" id="top-rated-grid" data-aos="fade-up" data-aos-delay="200">
                <!-- Top rated content will be loaded dynamically -->
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6">
                    <div class="footer-widget">
                        <div class="footer-logo mb-4">
                            <div class="logo-container">
                                <div class="logo-icon">
                                    <i class="fas fa-film"></i>
                                </div>
                                <div class="logo-text">
                                    <h5 class="brand-name mb-0">CinemaHub</h5>
                                    <small class="brand-tagline">أفضل الأفلام والمسلسلات</small>
                                </div>
                            </div>
                        </div>
                        <p class="footer-description">موقعك الأول لمشاهدة أحدث الأفلام والمسلسلات العربية والأجنبية بجودة عالية ومجاناً. تجربة مشاهدة لا تُنسى مع أفضل المحتوى الترفيهي.</p>
                        <div class="social-links">
                            <a href="#" class="social-link" title="فيسبوك">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="social-link" title="تويتر">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="social-link" title="إنستغرام">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="social-link" title="يوتيوب">
                                <i class="fab fa-youtube"></i>
                            </a>
                            <a href="#" class="social-link" title="تيك توك">
                                <i class="fab fa-tiktok"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6">
                    <div class="footer-widget">
                        <h6 class="footer-title">الأفلام</h6>
                        <ul class="footer-links">
                            <li><a href="movies.html"><i class="fas fa-list"></i> جميع الأفلام</a></li>
                            <li><a href="movies-action.html"><i class="fas fa-fist-raised"></i> أفلام أكشن</a></li>
                            <li><a href="movies-comedy.html"><i class="fas fa-laugh"></i> أفلام كوميديا</a></li>
                            <li><a href="movies-horror.html"><i class="fas fa-ghost"></i> أفلام رعب</a></li>
                            <li><a href="movies-romance.html"><i class="fas fa-heart"></i> أفلام رومانسية</a></li>
                            <li><a href="movies-scifi.html"><i class="fas fa-rocket"></i> خيال علمي</a></li>
                        </ul>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6">
                    <div class="footer-widget">
                        <h6 class="footer-title">المسلسلات</h6>
                        <ul class="footer-links">
                            <li><a href="series.html"><i class="fas fa-tv"></i> جميع المسلسلات</a></li>
                            <li><a href="series-arabic.html"><i class="fas fa-flag"></i> مسلسلات عربية</a></li>
                            <li><a href="series-turkish.html"><i class="fas fa-star-and-crescent"></i> مسلسلات تركية</a></li>
                            <li><a href="series-korean.html"><i class="fas fa-yin-yang"></i> مسلسلات كورية</a></li>
                            <li><a href="series-american.html"><i class="fas fa-flag-usa"></i> مسلسلات أمريكية</a></li>
                            <li><a href="series-anime.html"><i class="fas fa-dragon"></i> أنمي</a></li>
                        </ul>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6">
                    <div class="footer-widget">
                        <h6 class="footer-title">روابط مفيدة</h6>
                        <ul class="footer-links">
                            <li><a href="top-rated.html"><i class="fas fa-star"></i> الأعلى تقييماً</a></li>
                            <li><a href="latest.html"><i class="fas fa-clock"></i> أحدث الإضافات</a></li>
                            <li><a href="about.html"><i class="fas fa-info-circle"></i> من نحن</a></li>
                            <li><a href="contact.html"><i class="fas fa-envelope"></i> اتصل بنا</a></li>
                            <li><a href="privacy.html"><i class="fas fa-shield-alt"></i> سياسة الخصوصية</a></li>
                            <li><a href="terms.html"><i class="fas fa-file-contract"></i> شروط الاستخدام</a></li>
                        </ul>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6">
                    <div class="footer-widget">
                        <h6 class="footer-title"><i class="fas fa-envelope"></i> النشرة الإخبارية</h6>
                        <p class="newsletter-text">اشترك للحصول على أحدث الأفلام والمسلسلات والعروض الحصرية</p>
                        <form class="newsletter-form" onsubmit="subscribeNewsletter(event)">
                            <div class="input-group">
                                <input type="email" class="form-control newsletter-input" placeholder="أدخل بريدك الإلكتروني" required="required" />
                                <button class="btn newsletter-btn" type="submit" title="اشتراك">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </form>
                        <div class="app-download">
                            <p class="app-text">حمل التطبيق</p>
                            <div class="app-buttons">
                                <a href="#" class="app-btn">
                                    <i class="fab fa-apple"></i>
                                    <span>App Store</span>
                                </a>
                                <a href="#" class="app-btn">
                                    <i class="fab fa-google-play"></i>
                                    <span>Google Play</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <hr class="footer-divider" />

            <div class="footer-bottom">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="copyright">
                            © 2024 CinemaHub Pro - سينما هاب برو. جميع الحقوق محفوظة.
                        </p>
                    </div>
                    <div class="col-md-6">
                        <ul class="footer-bottom-links">
                            <li><a href="privacy.html">الخصوصية</a></li>
                            <li><a href="terms.html">الشروط</a></li>
                            <li><a href="dmca.html">DMCA</a></li>
                            <li><a href="sitemap.html">خريطة الموقع</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="backToTop" onclick="scrollToTop()" title="العودة للأعلى">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Login Modal -->
    <div class="modal fade" id="loginModal" tabindex="-1" aria-labelledby="loginModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content auth-modal">
                <div class="modal-header">
                    <h5 class="modal-title" id="loginModalLabel">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل الدخول
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="loginForm" onsubmit="handleLogin(event)">
                        <div class="mb-3">
                            <label for="loginEmail" class="form-label">البريد الإلكتروني</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                <input type="email" class="form-control" id="loginEmail" required="required" />
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="loginPassword" class="form-label">كلمة المرور</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input type="password" class="form-control" id="loginPassword" required="required" />
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('loginPassword')">
                                    <i class="fas fa-eye" id="loginPasswordToggle"></i>
                                </button>
                            </div>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe" />
                            <label class="form-check-label" for="rememberMe">
                                تذكرني
                            </label>
                        </div>
                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-sign-in-alt"></i>
                            تسجيل الدخول
                        </button>
                        <div class="text-center">
                            <a href="#" onclick="showForgotPasswordModal()" class="text-decoration-none">
                                نسيت كلمة المرور؟
                            </a>
                        </div>
                        <hr />
                        <div class="text-center">
                            <p class="mb-2">ليس لديك حساب؟</p>
                            <button type="button" class="btn btn-outline-primary" onclick="switchToRegister()">
                                إنشاء حساب جديد
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Register Modal -->
    <div class="modal fade" id="registerModal" tabindex="-1" aria-labelledby="registerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content auth-modal">
                <div class="modal-header">
                    <h5 class="modal-title" id="registerModalLabel">
                        <i class="fas fa-user-plus"></i>
                        إنشاء حساب جديد
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="registerForm" onsubmit="handleRegister(event)">
                        <div class="mb-3">
                            <label for="registerName" class="form-label">الاسم الكامل</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                                <input type="text" class="form-control" id="registerName" required="required" />
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="registerEmail" class="form-label">البريد الإلكتروني</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                <input type="email" class="form-control" id="registerEmail" required="required" />
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="registerPassword" class="form-label">كلمة المرور</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input type="password" class="form-control" id="registerPassword" required="required" />
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('registerPassword')">
                                    <i class="fas fa-eye" id="registerPasswordToggle"></i>
                                </button>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">تأكيد كلمة المرور</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input type="password" class="form-control" id="confirmPassword" required="required" />
                            </div>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="agreeTerms" required="required" />
                            <label class="form-check-label" for="agreeTerms">
                                أوافق على <a href="#" class="text-decoration-none">الشروط والأحكام</a>
                            </label>
                        </div>
                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-user-plus"></i>
                            إنشاء الحساب
                        </button>
                        <hr />
                        <div class="text-center">
                            <p class="mb-2">لديك حساب بالفعل؟</p>
                            <button type="button" class="btn btn-outline-primary" onclick="switchToLogin()">
                                تسجيل الدخول
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Subscription Modal -->
    <div class="modal fade" id="subscriptionModal" tabindex="-1" aria-labelledby="subscriptionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content subscription-modal">
                <div class="modal-header">
                    <h5 class="modal-title" id="subscriptionModalLabel">
                        <i class="fas fa-crown"></i>
                        الباقات والاشتراكات
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="subscription-plans">
                        <!-- Free Plan -->
                        <div class="plan-card free-plan">
                            <div class="plan-header">
                                <div class="plan-icon">
                                    <i class="fas fa-gift"></i>
                                </div>
                                <h3 class="plan-name">الباقة المجانية</h3>
                                <div class="plan-price">
                                    <span class="price">0</span>
                                    <span class="currency">ريال</span>
                                    <span class="period">/شهر</span>
                                </div>
                            </div>
                            <div class="plan-features">
                                <ul>
                                    <li><i class="fas fa-check"></i> مشاهدة محدودة (5 أفلام/شهر)</li>
                                    <li><i class="fas fa-check"></i> جودة 480p</li>
                                    <li><i class="fas fa-check"></i> إعلانات</li>
                                    <li><i class="fas fa-times"></i> تحميل الأفلام</li>
                                    <li><i class="fas fa-times"></i> مشاهدة بدون إنترنت</li>
                                </ul>
                            </div>
                            <button class="btn btn-plan current-plan" disabled="disabled">
                                <i class="fas fa-check"></i>
                                الباقة الحالية
                            </button>
                        </div>

                        <!-- Premium Plan -->
                        <div class="plan-card premium-plan popular">
                            <div class="popular-badge">الأكثر شعبية</div>
                            <div class="plan-header">
                                <div class="plan-icon">
                                    <i class="fas fa-star"></i>
                                </div>
                                <h3 class="plan-name">الباقة المميزة</h3>
                                <div class="plan-price">
                                    <span class="price">29</span>
                                    <span class="currency">ريال</span>
                                    <span class="period">/شهر</span>
                                </div>
                            </div>
                            <div class="plan-features">
                                <ul>
                                    <li><i class="fas fa-check"></i> مشاهدة غير محدودة</li>
                                    <li><i class="fas fa-check"></i> جودة HD (720p-1080p)</li>
                                    <li><i class="fas fa-check"></i> بدون إعلانات</li>
                                    <li><i class="fas fa-check"></i> تحميل 10 أفلام</li>
                                    <li><i class="fas fa-check"></i> مشاهدة على جهازين</li>
                                </ul>
                            </div>
                            <button class="btn btn-plan" onclick="subscribeToPlan('premium')">
                                <i class="fas fa-crown"></i>
                                اشترك الآن
                            </button>
                        </div>

                        <!-- VIP Plan -->
                        <div class="plan-card vip-plan">
                            <div class="plan-header">
                                <div class="plan-icon">
                                    <i class="fas fa-gem"></i>
                                </div>
                                <h3 class="plan-name">باقة VIP</h3>
                                <div class="plan-price">
                                    <span class="price">59</span>
                                    <span class="currency">ريال</span>
                                    <span class="period">/شهر</span>
                                </div>
                            </div>
                            <div class="plan-features">
                                <ul>
                                    <li><i class="fas fa-check"></i> مشاهدة غير محدودة</li>
                                    <li><i class="fas fa-check"></i> جودة 4K Ultra HD</li>
                                    <li><i class="fas fa-check"></i> بدون إعلانات</li>
                                    <li><i class="fas fa-check"></i> تحميل غير محدود</li>
                                    <li><i class="fas fa-check"></i> مشاهدة على 4 أجهزة</li>
                                    <li><i class="fas fa-check"></i> محتوى حصري</li>
                                    <li><i class="fas fa-check"></i> دعم فني مميز</li>
                                </ul>
                            </div>
                            <button class="btn btn-plan" onclick="subscribeToPlan('vip')">
                                <i class="fas fa-gem"></i>
                                اشترك الآن
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Modal -->
    <div class="modal fade" id="profileModal" tabindex="-1" aria-labelledby="profileModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content auth-modal">
                <div class="modal-header">
                    <h5 class="modal-title" id="profileModalLabel">
                        <i class="fas fa-user"></i>
                        الملف الشخصي
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="profileForm" onsubmit="updateProfile(event)">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="profileName" class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="profileName" required="required" />
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="profileEmail" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="profileEmail" required="required" />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="profilePhone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="profilePhone" />
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="profileAvatar" class="form-label">صورة الملف الشخصي</label>
                                <input type="file" class="form-control" id="profileAvatar" accept="image/*" />
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="profileBio" class="form-label">نبذة شخصية</label>
                            <textarea class="form-control" id="profileBio" rows="3" placeholder="اكتب نبذة عن نفسك..."></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="currentPassword" class="form-label">كلمة المرور الحالية</label>
                                <input type="password" class="form-control" id="currentPassword" />
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="newPassword" class="form-label">كلمة المرور الجديدة</label>
                                <input type="password" class="form-control" id="newPassword" />
                            </div>
                        </div>
                        <div class="d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Watchlist Modal -->
    <div class="modal fade" id="watchlistModal" tabindex="-1" aria-labelledby="watchlistModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content auth-modal">
                <div class="modal-header">
                    <h5 class="modal-title" id="watchlistModalLabel">
                        <i class="fas fa-heart"></i>
                        قائمة المفضلة
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="watchlistContainer">
                        <!-- Watchlist items will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- History Modal -->
    <div class="modal fade" id="historyModal" tabindex="-1" aria-labelledby="historyModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content auth-modal">
                <div class="modal-header">
                    <h5 class="modal-title" id="historyModalLabel">
                        <i class="fas fa-history"></i>
                        سجل المشاهدة
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="historyContainer">
                        <!-- History items will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content auth-modal">
                <div class="modal-header">
                    <h5 class="modal-title" id="settingsModalLabel">
                        <i class="fas fa-cog"></i>
                        الإعدادات
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="settingsForm" onsubmit="updateSettings(event)">
                        <div class="mb-4">
                            <h6><i class="fas fa-bell"></i> الإشعارات</h6>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="notificationsEnabled" />
                                <label class="form-check-label" for="notificationsEnabled">
                                    تفعيل الإشعارات
                                </label>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h6><i class="fas fa-play"></i> تشغيل الفيديو</h6>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="autoPlay" />
                                <label class="form-check-label" for="autoPlay">
                                    التشغيل التلقائي
                                </label>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="qualityPreference" class="form-label">
                                <i class="fas fa-video"></i> جودة الفيديو المفضلة
                            </label>
                            <select class="form-select" id="qualityPreference">
                                <option value="auto">تلقائي</option>
                                <option value="480p">480p</option>
                                <option value="720p">720p HD</option>
                                <option value="1080p">1080p Full HD</option>
                                <option value="4k">4K Ultra HD</option>
                            </select>
                        </div>

                        <div class="mb-4">
                            <label for="languagePreference" class="form-label">
                                <i class="fas fa-language"></i> اللغة المفضلة
                            </label>
                            <select class="form-select" id="languagePreference">
                                <option value="ar">العربية</option>
                                <option value="en">English</option>
                                <option value="fr">Français</option>
                            </select>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Dashboard Modal -->
    <div class="modal fade" id="adminDashboardModal" tabindex="-1" aria-labelledby="adminDashboardLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-centered">
            <div class="modal-content admin-modal">
                <div class="modal-header">
                    <h5 class="modal-title" id="adminDashboardLabel">
                        <i class="fas fa-crown"></i>
                        لوحة التحكم الإدارية
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="admin-dashboard">
                        <!-- Stats Cards -->
                        <div class="admin-stats">
                            <div class="stat-card users-stat">
                                <div class="stat-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalUsers">0</h3>
                                    <p class="stat-label">إجمالي المستخدمين</p>
                                </div>
                            </div>

                            <div class="stat-card movies-stat">
                                <div class="stat-icon">
                                    <i class="fas fa-film"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalMovies">0</h3>
                                    <p class="stat-label">إجمالي الأفلام</p>
                                </div>
                            </div>

                            <div class="stat-card revenue-stat">
                                <div class="stat-icon">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalRevenue">0</h3>
                                    <p class="stat-label">الإيرادات (ريال)</p>
                                </div>
                            </div>

                            <div class="stat-card subscriptions-stat">
                                <div class="stat-icon">
                                    <i class="fas fa-crown"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalSubscriptions">0</h3>
                                    <p class="stat-label">الاشتراكات المدفوعة</p>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="admin-actions">
                            <h6 class="section-title">
                                <i class="fas fa-bolt"></i>
                                إجراءات سريعة
                            </h6>
                            <div class="action-buttons">
                                <button class="btn btn-admin" onclick="addNewMovie()">
                                    <i class="fas fa-plus"></i>
                                    إضافة فيلم جديد
                                </button>
                                <button class="btn btn-admin" onclick="manageUsers()">
                                    <i class="fas fa-users-cog"></i>
                                    إدارة المستخدمين
                                </button>
                                <button class="btn btn-admin" onclick="viewReports()">
                                    <i class="fas fa-chart-line"></i>
                                    عرض التقارير
                                </button>
                                <button class="btn btn-admin" onclick="systemSettings()">
                                    <i class="fas fa-cogs"></i>
                                    إعدادات النظام
                                </button>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div class="admin-activity">
                            <h6 class="section-title">
                                <i class="fas fa-clock"></i>
                                النشاط الأخير
                            </h6>
                            <div class="activity-list" id="recentActivity">
                                <div class="activity-item">
                                    <div class="activity-icon user-activity">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                    <div class="activity-content">
                                        <p class="activity-text">مستخدم جديد: أحمد محمد</p>
                                        <span class="activity-time">منذ 5 دقائق</span>
                                    </div>
                                </div>

                                <div class="activity-item">
                                    <div class="activity-icon subscription-activity">
                                        <i class="fas fa-crown"></i>
                                    </div>
                                    <div class="activity-content">
                                        <p class="activity-text">اشتراك جديد: فاطمة علي - باقة مميزة</p>
                                        <span class="activity-time">منذ 15 دقيقة</span>
                                    </div>
                                </div>

                                <div class="activity-item">
                                    <div class="activity-icon movie-activity">
                                        <i class="fas fa-film"></i>
                                    </div>
                                    <div class="activity-content">
                                        <p class="activity-text">تم إضافة فيلم جديد: أفاتار 2</p>
                                        <span class="activity-time">منذ ساعة</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Management Modal -->
    <div class="modal fade" id="usersManagementModal" tabindex="-1" aria-labelledby="usersManagementLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content admin-modal">
                <div class="modal-header">
                    <h5 class="modal-title" id="usersManagementLabel">
                        <i class="fas fa-users"></i>
                        إدارة المستخدمين
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="admin-section-header mb-4">
                        <h6>قائمة المستخدمين</h6>
                        <p class="text-muted">إدارة حسابات المستخدمين والاشتراكات</p>
                    </div>
                    <div id="usersContainer" class="users-list">
                        <!-- Users will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Management Modal -->
    <div class="modal fade" id="contentManagementModal" tabindex="-1" aria-labelledby="contentManagementLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content admin-modal">
                <div class="modal-header">
                    <h5 class="modal-title" id="contentManagementLabel">
                        <i class="fas fa-film"></i>
                        إدارة المحتوى
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="admin-section-header mb-4">
                        <h6>إدارة الأفلام والمسلسلات</h6>
                        <p class="text-muted">إضافة وتعديل وحذف المحتوى</p>
                    </div>
                    <div id="contentContainer" class="content-list">
                        <!-- Content will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics Modal -->
    <div class="modal fade" id="analyticsModal" tabindex="-1" aria-labelledby="analyticsLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content admin-modal">
                <div class="modal-header">
                    <h5 class="modal-title" id="analyticsLabel">
                        <i class="fas fa-chart-bar"></i>
                        الإحصائيات والتقارير
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="admin-section-header mb-4">
                        <h6>إحصائيات الموقع</h6>
                        <p class="text-muted">تقارير مفصلة عن الأداء والمستخدمين</p>
                    </div>
                    <div class="admin-stats">
                        <div class="stat-card users-stat">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <h3 class="stat-number" id="analyticsUsers">0</h3>
                                <p class="stat-label">إجمالي المستخدمين</p>
                            </div>
                        </div>
                        <div class="stat-card movies-stat">
                            <div class="stat-icon">
                                <i class="fas fa-film"></i>
                            </div>
                            <div class="stat-info">
                                <h3 class="stat-number" id="analyticsMovies">0</h3>
                                <p class="stat-label">إجمالي الأفلام</p>
                            </div>
                        </div>
                        <div class="stat-card revenue-stat">
                            <div class="stat-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="stat-info">
                                <h3 class="stat-number" id="analyticsRevenue">0</h3>
                                <p class="stat-label">إجمالي الإيرادات</p>
                            </div>
                        </div>
                        <div class="stat-card subscriptions-stat">
                            <div class="stat-icon">
                                <i class="fas fa-crown"></i>
                            </div>
                            <div class="stat-info">
                                <h3 class="stat-number" id="analyticsSubscriptions">0</h3>
                                <p class="stat-label">الاشتراكات المدفوعة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script>
        // ===== User Authentication System =====
        let currentUser = null;

        // Demo users for testing
        const demoUsers = [
            {
                id: 0,
                name: 'مدير الموقع',
                email: '<EMAIL>',
                password: 'admin123',
                role: 'admin',
                plan: 'vip',
                avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&amp;h=150&amp;fit=crop&amp;crop=face&amp;auto=format&amp;q=80',
                joinDate: '2024-01-01',
                watchHistory: [],
                favorites: [],
                permissions: ['manage_users', 'manage_content', 'manage_subscriptions', 'view_analytics']
            },
            {
                id: 1,
                name: 'أحمد محمد',
                email: '<EMAIL>',
                password: '123456',
                role: 'user',
                plan: 'free',
                avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&amp;h=150&amp;fit=crop&amp;crop=face&amp;auto=format&amp;q=80',
                joinDate: '2024-01-15',
                watchHistory: [],
                favorites: []
            },
            {
                id: 2,
                name: 'فاطمة علي',
                email: '<EMAIL>',
                password: '123456',
                role: 'user',
                plan: 'premium',
                avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&amp;h=150&amp;fit=crop&amp;crop=face&amp;auto=format&amp;q=80',
                joinDate: '2024-02-10',
                watchHistory: [],
                favorites: []
            }
        ];

        // Subscription plans
        const subscriptionPlans = {
            free: {
                name: 'مجاني',
                price: 0,
                features: ['5 أفلام/شهر', 'جودة 480p', 'إعلانات'],
                color: '#6b7280'
            },
            premium: {
                name: 'مميز',
                price: 29,
                features: ['مشاهدة غير محدودة', 'جودة HD', 'بدون إعلانات'],
                color: '#ffd700'
            },
            vip: {
                name: 'VIP',
                price: 59,
                features: ['مشاهدة غير محدودة', 'جودة 4K', 'محتوى حصري'],
                color: '#9333ea'
            }
        };

        // Authentication Functions
        function showLoginModal() {
            const modal = new bootstrap.Modal(document.getElementById('loginModal'));
            modal.show();
        }

        function showRegisterModal() {
            const modal = new bootstrap.Modal(document.getElementById('registerModal'));
            modal.show();
        }

        function showSubscriptionModal() {
            const modal = new bootstrap.Modal(document.getElementById('subscriptionModal'));
            modal.show();
        }

        function switchToRegister() {
            bootstrap.Modal.getInstance(document.getElementById('loginModal')).hide();
            setTimeout(() => showRegisterModal(), 300);
        }

        function switchToLogin() {
            bootstrap.Modal.getInstance(document.getElementById('registerModal')).hide();
            setTimeout(() => showLoginModal(), 300);
        }

        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const toggle = document.getElementById(inputId + 'Toggle');

            if (input.type === 'password') {
                input.type = 'text';
                toggle.classList.remove('fa-eye');
                toggle.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                toggle.classList.remove('fa-eye-slash');
                toggle.classList.add('fa-eye');
            }
        }

        function handleLogin(event) {
            event.preventDefault();

            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            // Demo login - check against demo users
            const user = demoUsers.find(u => u.email === email && u.password === password);

            if (user) {
                currentUser = { ...user };

                // Save to localStorage if remember me is checked
                if (rememberMe) {
                    localStorage.setItem('currentUser', JSON.stringify(currentUser));
                } else {
                    sessionStorage.setItem('currentUser', JSON.stringify(currentUser));
                }

                updateUserInterface();
                bootstrap.Modal.getInstance(document.getElementById('loginModal')).hide();
                showNotification(`مرحباً ${user.name}! تم تسجيل الدخول بنجاح`, 'success');
            } else {
                showNotification('البريد الإلكتروني أو كلمة المرور غير صحيحة', 'error');
            }
        }

        function handleRegister(event) {
            event.preventDefault();

            const name = document.getElementById('registerName').value;
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const agreeTerms = document.getElementById('agreeTerms').checked;

            // Validation
            if (password !== confirmPassword) {
                showNotification('كلمات المرور غير متطابقة', 'error');
                return;
            }

            if (!agreeTerms) {
                showNotification('يجب الموافقة على الشروط والأحكام', 'warning');
                return;
            }

            // Check if email already exists
            if (demoUsers.find(u => u.email === email)) {
                showNotification('البريد الإلكتروني مستخدم بالفعل', 'error');
                return;
            }

            // Create new user with random avatar
            const avatars = [
                'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&amp;h=150&amp;fit=crop&amp;crop=face&amp;auto=format&amp;q=80',
                'https://images.unsplash.com/photo-1599566150163-29194dcaad36?w=150&amp;h=150&amp;fit=crop&amp;crop=face&amp;auto=format&amp;q=80',
                'https://images.unsplash.com/photo-1527980965255-d3b416303d12?w=150&amp;h=150&amp;fit=crop&amp;crop=face&amp;auto=format&amp;q=80',
                'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&amp;h=150&amp;fit=crop&amp;crop=face&amp;auto=format&amp;q=80',
                'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&amp;h=150&amp;fit=crop&amp;crop=face&amp;auto=format&amp;q=80',
                'https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=150&amp;h=150&amp;fit=crop&amp;crop=face&amp;auto=format&amp;q=80'
            ];

            const randomAvatar = avatars[Math.floor(Math.random() * avatars.length)];

            const newUser = {
                id: Date.now(),
                name: name,
                email: email,
                password: password,
                role: 'user',
                plan: 'free',
                avatar: randomAvatar,
                joinDate: new Date().toISOString().split('T')[0],
                watchHistory: [],
                favorites: []
            };

            demoUsers.push(newUser);
            currentUser = { ...newUser };

            sessionStorage.setItem('currentUser', JSON.stringify(currentUser));

            updateUserInterface();
            bootstrap.Modal.getInstance(document.getElementById('registerModal')).hide();
            showNotification(`مرحباً ${name}! تم إنشاء الحساب بنجاح`, 'success');
        }

        function logout() {
            currentUser = null;
            localStorage.removeItem('currentUser');
            sessionStorage.removeItem('currentUser');
            updateUserInterface();
            showNotification('تم تسجيل الخروج بنجاح', 'info');
        }

        function updateUserInterface() {
            const guestSection = document.getElementById('guestSection');
            const userSection = document.getElementById('userSection');
            const adminSection = document.getElementById('adminSection');

            if (currentUser) {
                guestSection.classList.add('d-none');
                userSection.classList.remove('d-none');

                // Update user info
                document.getElementById('userName').textContent = currentUser.name;
                document.getElementById('userPlan').textContent = subscriptionPlans[currentUser.plan].name;
                document.getElementById('userAvatarImg').src = currentUser.avatar;

                // Update plan styling
                const planElement = document.getElementById('userPlan');
                if (currentUser.role === 'admin') {
                    planElement.className = 'user-plan admin';
                    planElement.textContent = 'مدير';
                    adminSection.classList.remove('d-none');
                    userSection.classList.add('admin'); // Add admin class to user section
                } else {
                    planElement.className = `user-plan ${currentUser.plan}`;
                    adminSection.classList.add('d-none');
                    userSection.classList.remove('admin'); // Remove admin class
                }
            } else {
                guestSection.classList.remove('d-none');
                userSection.classList.add('d-none');
                adminSection.classList.add('d-none');
            }
        }

        function subscribeToPlan(planType) {
            if (!currentUser) {
                showNotification('يجب تسجيل الدخول أولاً', 'warning');
                showLoginModal();
                return;
            }

            if (currentUser.plan === planType) {
                showNotification('أنت مشترك بالفعل في هذه الباقة', 'info');
                return;
            }

            const plan = subscriptionPlans[planType];

            // Simulate payment process
            showNotification(`جاري معالجة الدفع لباقة ${plan.name}...`, 'info');

            setTimeout(() => {
                currentUser.plan = planType;

                // Update storage
                if (localStorage.getItem('currentUser')) {
                    localStorage.setItem('currentUser', JSON.stringify(currentUser));
                } else {
                    sessionStorage.setItem('currentUser', JSON.stringify(currentUser));
                }

                updateUserInterface();
                bootstrap.Modal.getInstance(document.getElementById('subscriptionModal')).hide();
                showNotification(`تم الاشتراك في باقة ${plan.name} بنجاح!`, 'success');
            }, 2000);
        }

        // Profile and other modals (developed functions)
        function showProfileModal() {
            if (!currentUser) {
                showNotification('يجب تسجيل الدخول أولاً', 'error');
                return;
            }

            const modal = new bootstrap.Modal(document.getElementById('profileModal'));

            // Update profile form with current user data
            document.getElementById('profileName').value = currentUser.name || '';
            document.getElementById('profileEmail').value = currentUser.email || '';
            document.getElementById('profilePhone').value = currentUser.phone || '';
            document.getElementById('profileBio').value = currentUser.bio || '';

            // Show current avatar in profile modal
            const profileAvatar = document.getElementById('profileAvatar');
            if (profileAvatar) {
                profileAvatar.src = currentUser.avatar;
            }

            modal.show();
        }

        function changeAvatar() {
            const avatars = [
                'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&amp;h=150&amp;fit=crop&amp;crop=face&amp;auto=format&amp;q=80',
                'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&amp;h=150&amp;fit=crop&amp;crop=face&amp;auto=format&amp;q=80',
                'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&amp;h=150&amp;fit=crop&amp;crop=face&amp;auto=format&amp;q=80',
                'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&amp;h=150&amp;fit=crop&amp;crop=face&amp;auto=format&amp;q=80',
                'https://images.unsplash.com/photo-1599566150163-29194dcaad36?w=150&amp;h=150&amp;fit=crop&amp;crop=face&amp;auto=format&amp;q=80',
                'https://images.unsplash.com/photo-1527980965255-d3b416303d12?w=150&amp;h=150&amp;fit=crop&amp;crop=face&amp;auto=format&amp;q=80',
                'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&amp;h=150&amp;fit=crop&amp;crop=face&amp;auto=format&amp;q=80',
                'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&amp;h=150&amp;fit=crop&amp;crop=face&amp;auto=format&amp;q=80',
                'https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=150&amp;h=150&amp;fit=crop&amp;crop=face&amp;auto=format&amp;q=80',
                'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&amp;h=150&amp;fit=crop&amp;crop=face&amp;auto=format&amp;q=80'
            ];

            const randomAvatar = avatars[Math.floor(Math.random() * avatars.length)];

            if (currentUser) {
                currentUser.avatar = randomAvatar;

                // Update UI
                document.getElementById('userAvatarImg').src = randomAvatar;
                const profileAvatar = document.getElementById('profileAvatar');
                if (profileAvatar) {
                    profileAvatar.src = randomAvatar;
                }

                // Update storage
                if (localStorage.getItem('currentUser')) {
                    localStorage.setItem('currentUser', JSON.stringify(currentUser));
                } else {
                    sessionStorage.setItem('currentUser', JSON.stringify(currentUser));
                }

                showNotification('تم تغيير الصورة الشخصية بنجاح!', 'success');
            }
        }

        function showWatchlistModal() {
            if (!currentUser) {
                showNotification('يجب تسجيل الدخول أولاً', 'error');
                return;
            }

            const modal = new bootstrap.Modal(document.getElementById('watchlistModal'));
            loadWatchlist();
            modal.show();
        }

        function showHistoryModal() {
            if (!currentUser) {
                showNotification('يجب تسجيل الدخول أولاً', 'error');
                return;
            }

            const modal = new bootstrap.Modal(document.getElementById('historyModal'));
            loadWatchHistory();
            modal.show();
        }

        function showSettingsModal() {
            if (!currentUser) {
                showNotification('يجب تسجيل الدخول أولاً', 'error');
                return;
            }

            const modal = new bootstrap.Modal(document.getElementById('settingsModal'));
            loadUserSettings();
            modal.show();
        }

        function showForgotPasswordModal() {
            showNotification('استعادة كلمة المرور قيد التطوير', 'info');
        }

        // Update Profile Function
        function updateProfile(event) {
            event.preventDefault();

            if (!currentUser) {
                showNotification('يجب تسجيل الدخول أولاً', 'error');
                return;
            }

            const name = document.getElementById('profileName').value;
            const email = document.getElementById('profileEmail').value;
            const phone = document.getElementById('profilePhone').value;
            const bio = document.getElementById('profileBio').value;
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;

            // Validate current password if trying to change password
            if (newPassword && currentPassword !== currentUser.password) {
                showNotification('كلمة المرور الحالية غير صحيحة', 'error');
                return;
            }

            // Update user data
            currentUser.name = name;
            currentUser.email = email;
            currentUser.phone = phone;
            currentUser.bio = bio;

            if (newPassword) {
                currentUser.password = newPassword;
            }

            // Update storage
            if (localStorage.getItem('currentUser')) {
                localStorage.setItem('currentUser', JSON.stringify(currentUser));
            } else {
                sessionStorage.setItem('currentUser', JSON.stringify(currentUser));
            }

            updateUserInterface();
            bootstrap.Modal.getInstance(document.getElementById('profileModal')).hide();
            showNotification('تم تحديث الملف الشخصي بنجاح', 'success');
        }

        // Load Watchlist Function
        function loadWatchlist() {
            const watchlistContainer = document.getElementById('watchlistContainer');
            if (!watchlistContainer) return;

            // Add demo favorites if empty
            if (!currentUser.favorites || currentUser.favorites.length === 0) {
                currentUser.favorites = [
                    {
                        id: 1,
                        title: 'أفاتار: طريق الماء',
                        year: 2022,
                        genre: 'خيال علمي',
                        poster: 'https://via.placeholder.com/150x200/dc2626/ffffff?text=أفاتار'
                    },
                    {
                        id: 2,
                        title: 'الرجل العنكبوت',
                        year: 2021,
                        genre: 'أكشن',
                        poster: 'https://via.placeholder.com/150x200/dc2626/ffffff?text=سبايدر'
                    }
                ];
            }

            // Display favorites
            watchlistContainer.innerHTML = currentUser.favorites.map(item => `
                <div class="watchlist-item">
                    <img src="${item.poster}" alt="${item.title}" class="watchlist-poster">
                    <div class="watchlist-info">
                        <h6>${item.title}</h6>
                        <p class="text-muted">${item.year} • ${item.genre}</p>
                        <div class="watchlist-actions">
                            <button class="btn btn-sm btn-primary" onclick="watchMovie(${item.id})">
                                <i class="fas fa-play"></i> مشاهدة
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="removeFromFavorites(${item.id})">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Load Watch History Function
        function loadWatchHistory() {
            const historyContainer = document.getElementById('historyContainer');
            if (!historyContainer) return;

            // Add demo watch history if empty
            if (!currentUser.watchHistory || currentUser.watchHistory.length === 0) {
                currentUser.watchHistory = [
                    {
                        id: 1,
                        title: 'بيت التنين',
                        watchDate: '2024-01-15',
                        progress: 75,
                        poster: 'https://via.placeholder.com/150x200/dc2626/ffffff?text=التنين'
                    },
                    {
                        id: 2,
                        title: 'لعبة الحبار',
                        watchDate: '2024-01-10',
                        progress: 100,
                        poster: 'https://via.placeholder.com/150x200/dc2626/ffffff?text=الحبار'
                    }
                ];
            }

            // Display watch history
            historyContainer.innerHTML = currentUser.watchHistory.map(item => `
                <div class="history-item">
                    <img src="${item.poster}" alt="${item.title}" class="history-poster">
                    <div class="history-info">
                        <h6>${item.title}</h6>
                        <p class="text-muted">شوهد في: ${item.watchDate}</p>
                        <div class="progress mb-2">
                            <div class="progress-bar" style="width: ${item.progress}%"></div>
                        </div>
                        <small class="text-muted">${item.progress}% مكتمل</small>
                    </div>
                </div>
            `).join('');
        }

        // Load User Settings Function
        function loadUserSettings() {
            // Load current settings (demo)
            document.getElementById('notificationsEnabled').checked = currentUser.settings?.notifications ?? true;
            document.getElementById('autoPlay').checked = currentUser.settings?.autoPlay ?? true;
            document.getElementById('qualityPreference').value = currentUser.settings?.quality ?? 'auto';
            document.getElementById('languagePreference').value = currentUser.settings?.language ?? 'ar';
        }

        // Update Settings Function
        function updateSettings(event) {
            event.preventDefault();

            if (!currentUser) {
                showNotification('يجب تسجيل الدخول أولاً', 'error');
                return;
            }

            // Initialize settings object if it doesn't exist
            if (!currentUser.settings) {
                currentUser.settings = {};
            }

            // Update settings
            currentUser.settings.notifications = document.getElementById('notificationsEnabled').checked;
            currentUser.settings.autoPlay = document.getElementById('autoPlay').checked;
            currentUser.settings.quality = document.getElementById('qualityPreference').value;
            currentUser.settings.language = document.getElementById('languagePreference').value;

            // Update storage
            if (localStorage.getItem('currentUser')) {
                localStorage.setItem('currentUser', JSON.stringify(currentUser));
            } else {
                sessionStorage.setItem('currentUser', JSON.stringify(currentUser));
            }

            bootstrap.Modal.getInstance(document.getElementById('settingsModal')).hide();
            showNotification('تم حفظ الإعدادات بنجاح', 'success');
        }

        // ===== Admin Functions =====
        function showAdminDashboard() {
            if (!currentUser || currentUser.role !== 'admin') {
                showNotification('غير مصرح لك بالوصول لهذه الصفحة', 'error');
                return;
            }

            updateAdminStats();
            const modal = new bootstrap.Modal(document.getElementById('adminDashboardModal'));
            modal.show();
        }

        function updateAdminStats() {
            // Calculate stats
            const totalUsers = demoUsers.filter(u => u.role !== 'admin').length;
            const totalMovies = demoMovies.length + demoSeries.length;
            const paidSubscriptions = demoUsers.filter(u => u.plan !== 'free').length;
            const totalRevenue = paidSubscriptions * 29 + demoUsers.filter(u => u.plan === 'vip').length * 30; // Simplified calculation

            // Update UI
            document.getElementById('totalUsers').textContent = totalUsers;
            document.getElementById('totalMovies').textContent = totalMovies;
            document.getElementById('totalRevenue').textContent = totalRevenue.toLocaleString();
            document.getElementById('totalSubscriptions').textContent = paidSubscriptions;
        }

        function showUsersManagement() {
            if (!currentUser || currentUser.role !== 'admin') {
                showNotification('غير مصرح لك بالوصول لهذه الصفحة', 'error');
                return;
            }

            const modal = new bootstrap.Modal(document.getElementById('usersManagementModal'));
            loadUsersData();
            modal.show();
        }

        function showContentManagement() {
            if (!currentUser || currentUser.role !== 'admin') {
                showNotification('غير مصرح لك بالوصول لهذه الصفحة', 'error');
                return;
            }

            const modal = new bootstrap.Modal(document.getElementById('contentManagementModal'));
            loadContentData();
            modal.show();
        }

        function showAnalytics() {
            if (!currentUser || currentUser.role !== 'admin') {
                showNotification('غير مصرح لك بالوصول لهذه الصفحة', 'error');
                return;
            }

            const modal = new bootstrap.Modal(document.getElementById('analyticsModal'));
            loadAnalyticsData();
            modal.show();
        }

        // Admin Quick Actions
        function addNewMovie() {
            if (!currentUser || currentUser.role !== 'admin') {
                showNotification('غير مصرح لك بالوصول لهذه الوظيفة', 'error');
                return;
            }

            const modal = new bootstrap.Modal(document.getElementById('addMovieModal'));
            modal.show();
        }

        function manageUsers() {
            if (!currentUser || currentUser.role !== 'admin') {
                showNotification('غير مصرح لك بالوصول لهذه الوظيفة', 'error');
                return;
            }

            showUsersManagement();
        }

        function viewReports() {
            if (!currentUser || currentUser.role !== 'admin') {
                showNotification('غير مصرح لك بالوصول لهذه الوظيفة', 'error');
                return;
            }

            showAnalytics();
        }

        function systemSettings() {
            if (!currentUser || currentUser.role !== 'admin') {
                showNotification('غير مصرح لك بالوصول لهذه الوظيفة', 'error');
                return;
            }

            const modal = new bootstrap.Modal(document.getElementById('systemSettingsModal'));
            modal.show();
        }

        // Load Admin Data Functions
        function loadUsersData() {
            const usersContainer = document.getElementById('usersContainer');
            if (!usersContainer) return;

            // Add more demo users with beautiful avatars
            const extendedUsers = [
                ...demoUsers,
                {
                    id: 3,
                    name: 'سارة أحمد',
                    email: '<EMAIL>',
                    plan: 'vip',
                    avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face&auto=format&q=80'
                },
                {
                    id: 4,
                    name: 'محمد علي',
                    email: '<EMAIL>',
                    plan: 'premium',
                    avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face&auto=format&q=80'
                },
                {
                    id: 5,
                    name: 'نور الدين',
                    email: '<EMAIL>',
                    plan: 'free',
                    avatar: 'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face&auto=format&q=80'
                }
            ];

            usersContainer.innerHTML = extendedUsers.map(user => `
                <div class="user-item">
                    <img src="${user.avatar}" alt="${user.name}" class="user-avatar">
                    <div class="user-info">
                        <h6>${user.name}</h6>
                        <p class="text-muted">${user.email}</p>
                        <span class="badge bg-${user.plan === 'free' ? 'secondary' : user.plan === 'premium' ? 'warning' : 'primary'}">${subscriptionPlans[user.plan].name}</span>
                    </div>
                    <div class="user-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="editUser(${user.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(${user.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function loadContentData() {
            const contentContainer = document.getElementById('contentContainer');
            if (!contentContainer) return;

            // Demo content data
            const demoContent = [
                { id: 1, title: 'أفاتار: طريق الماء', type: 'movie', year: 2022, rating: 7.7 },
                { id: 2, title: 'الرجل العنكبوت: لا طريق للعودة', type: 'movie', year: 2021, rating: 8.4 },
                { id: 3, title: 'بيت التنين', type: 'series', year: 2022, rating: 8.5 }
            ];

            contentContainer.innerHTML = demoContent.map(item => `
                <div class="content-item">
                    <div class="content-info">
                        <h6>${item.title}</h6>
                        <p class="text-muted">${item.type === 'movie' ? 'فيلم' : 'مسلسل'} • ${item.year}</p>
                        <div class="rating">
                            <i class="fas fa-star text-warning"></i>
                            <span>${item.rating}/10</span>
                        </div>
                    </div>
                    <div class="content-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="editContent(${item.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteContent(${item.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function loadAnalyticsData() {
            // Update stats with demo data for both dashboard and analytics modal
            const totalUsersCount = demoUsers.length;
            const totalMoviesCount = '150';
            const totalRevenueCount = '12,500';
            const totalSubscriptionsCount = demoUsers.filter(u => u.plan !== 'free').length;

            // Update dashboard stats
            if (document.getElementById('totalUsers')) {
                document.getElementById('totalUsers').textContent = totalUsersCount;
            }
            if (document.getElementById('totalMovies')) {
                document.getElementById('totalMovies').textContent = totalMoviesCount;
            }
            if (document.getElementById('totalRevenue')) {
                document.getElementById('totalRevenue').textContent = totalRevenueCount;
            }
            if (document.getElementById('totalSubscriptions')) {
                document.getElementById('totalSubscriptions').textContent = totalSubscriptionsCount;
            }

            // Update analytics modal stats
            if (document.getElementById('analyticsUsers')) {
                document.getElementById('analyticsUsers').textContent = totalUsersCount;
            }
            if (document.getElementById('analyticsMovies')) {
                document.getElementById('analyticsMovies').textContent = totalMoviesCount;
            }
            if (document.getElementById('analyticsRevenue')) {
                document.getElementById('analyticsRevenue').textContent = totalRevenueCount;
            }
            if (document.getElementById('analyticsSubscriptions')) {
                document.getElementById('analyticsSubscriptions').textContent = totalSubscriptionsCount;
            }
        }

        // Missing Admin Functions
        function editUser(userId) {
            if (!currentUser || currentUser.role !== 'admin') {
                showNotification('غير مصرح لك بالوصول لهذه الوظيفة', 'error');
                return;
            }
            showNotification(`تعديل المستخدم رقم ${userId}`, 'info');
        }

        function deleteUser(userId) {
            if (!currentUser || currentUser.role !== 'admin') {
                showNotification('غير مصرح لك بالوصول لهذه الوظيفة', 'error');
                return;
            }
            if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
                showNotification(`تم حذف المستخدم رقم ${userId}`, 'success');
            }
        }

        function editContent(contentId) {
            if (!currentUser || currentUser.role !== 'admin') {
                showNotification('غير مصرح لك بالوصول لهذه الوظيفة', 'error');
                return;
            }
            showNotification(`تعديل المحتوى رقم ${contentId}`, 'info');
        }

        function deleteContent(contentId) {
            if (!currentUser || currentUser.role !== 'admin') {
                showNotification('غير مصرح لك بالوصول لهذه الوظيفة', 'error');
                return;
            }
            if (confirm('هل أنت متأكد من حذف هذا المحتوى؟')) {
                showNotification(`تم حذف المحتوى رقم ${contentId}`, 'success');
            }
        }

        function removeFromFavorites(itemId) {
            if (!currentUser) {
                showNotification('يجب تسجيل الدخول أولاً', 'error');
                return;
            }

            if (!currentUser.favorites) {
                currentUser.favorites = [];
            }

            currentUser.favorites = currentUser.favorites.filter(item => item.id !== itemId);

            // Update storage
            if (localStorage.getItem('currentUser')) {
                localStorage.setItem('currentUser', JSON.stringify(currentUser));
            } else {
                sessionStorage.setItem('currentUser', JSON.stringify(currentUser));
            }

            showNotification('تم حذف العنصر من المفضلة', 'success');
            loadWatchlist(); // Refresh the watchlist
        }

        function watchMovie(movieId) {
            if (!currentUser) {
                showNotification('يجب تسجيل الدخول أولاً', 'error');
                return;
            }

            // Simulate opening movie player
            showNotification('جاري تحضير الفيلم للمشاهدة...', 'info');

            setTimeout(() => {
                // In a real app, this would redirect to the movie player
                window.open('watch-pro.html', '_blank');
            }, 1000);
        }

        // Initialize user session
        function initializeUserSession() {
            // Check for saved user session
            const savedUser = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');

            if (savedUser) {
                currentUser = JSON.parse(savedUser);
                updateUserInterface();
            }
        }

        // ===== Demo Data =====
        const demoMovies = [
            // Action Movies
            {
                id: 1,
                title: 'أفاتار: طريق الماء',
                poster_path: 'https://image.tmdb.org/t/p/w500/s16H6tpK2utvwDtzZ8Qy4qm5Emw.jpg',
                release_date: '2022-12-16',
                vote_average: 7.7,
                genre: 'خيال علمي، مغامرة',
                category: 'scifi'
            },
            {
                id: 2,
                title: 'الرجل العنكبوت: لا طريق للعودة',
                poster_path: 'https://image.tmdb.org/t/p/w500/1g0dhYtq4irTY1GPXvft6k4YLjm.jpg',
                release_date: '2021-12-17',
                vote_average: 8.4,
                genre: 'أكشن، مغامرة',
                category: 'action'
            },
            {
                id: 3,
                title: 'توب غان: مافريك',
                poster_path: 'https://image.tmdb.org/t/p/w500/62HCnUTziyWcpDaBO2i1DX17ljH.jpg',
                release_date: '2022-05-27',
                vote_average: 8.3,
                genre: 'أكشن، دراما',
                category: 'action'
            },
            {
                id: 4,
                title: 'دكتور سترينج في الكون المتعدد',
                poster_path: 'https://image.tmdb.org/t/p/w500/9Gtg2DzBhmYamXBS1hKAhiwbBKS.jpg',
                release_date: '2022-05-06',
                vote_average: 7.3,
                genre: 'فانتازيا، أكشن',
                category: 'action'
            },
            {
                id: 5,
                title: 'مينيونز: صعود جرو',
                poster_path: 'https://image.tmdb.org/t/p/w500/wKiOkZTN9lUUUNZLmtnwubZYONg.jpg',
                release_date: '2022-07-01',
                vote_average: 7.3,
                genre: 'رسوم متحركة، كوميديا',
                category: 'animation'
            },
            {
                id: 6,
                title: 'ثور: الحب والرعد',
                poster_path: 'https://image.tmdb.org/t/p/w500/pIkRyD18kl4FhoCNQuWxWu5cBLM.jpg',
                release_date: '2022-07-08',
                vote_average: 6.8,
                genre: 'أكشن، كوميديا',
                category: 'action'
            },
            {
                id: 7,
                title: 'الوحوش الرائعة: أسرار دمبلدور',
                poster_path: 'https://image.tmdb.org/t/p/w500/jrgifaYeUtTnaH7NF5Drkgjg2MB.jpg',
                release_date: '2022-04-15',
                vote_average: 6.7,
                genre: 'فانتازيا، مغامرة',
                category: 'scifi'
            },
            {
                id: 8,
                title: 'جوراسيك وورلد دومينيون',
                poster_path: 'https://image.tmdb.org/t/p/w500/kAVRgw7GgK1CfYEJq8ME6EvRIgU.jpg',
                release_date: '2022-06-10',
                vote_average: 7.0,
                genre: 'أكشن، مغامرة',
                category: 'action'
            },
            // Comedy Movies
            {
                id: 9,
                title: 'باربي',
                poster_path: 'https://image.tmdb.org/t/p/w500/iuFNMS8U5cb6xfzi51Dbkovj7vM.jpg',
                release_date: '2023-07-21',
                vote_average: 7.1,
                genre: 'كوميديا، فانتازيا',
                category: 'comedy'
            },
            {
                id: 10,
                title: 'جون ويك 4',
                poster_path: 'https://image.tmdb.org/t/p/w500/vZloFAK7NmvMGKE7VkF5UHaz0I.jpg',
                release_date: '2023-03-24',
                vote_average: 7.8,
                genre: 'أكشن، إثارة',
                category: 'action'
            },
            // Romance Movies
            {
                id: 11,
                title: 'تايتانيك',
                poster_path: 'https://image.tmdb.org/t/p/w500/9xjZS2rlVxm8SFx8kPC3aIGCOYQ.jpg',
                release_date: '1997-12-19',
                vote_average: 7.9,
                genre: 'رومانسي، دراما',
                category: 'romance'
            },
            {
                id: 12,
                title: 'لا لا لاند',
                poster_path: 'https://image.tmdb.org/t/p/w500/uDO8zWDhfWwoFdKS4fzkUJt0Rf0.jpg',
                release_date: '2016-12-25',
                vote_average: 8.0,
                genre: 'رومانسي، موسيقي',
                category: 'romance'
            },
            // Drama Movies
            {
                id: 13,
                title: 'أوبنهايمر',
                poster_path: 'https://image.tmdb.org/t/p/w500/8Gxv8gSFCU0XGDykEGv7zR1n2ua.jpg',
                release_date: '2023-07-21',
                vote_average: 8.1,
                genre: 'دراما، تاريخي',
                category: 'drama'
            },
            {
                id: 14,
                title: 'الجوكر',
                poster_path: 'https://image.tmdb.org/t/p/w500/udDclJoHjfjb8Ekgsd4FDteOkCU.jpg',
                release_date: '2019-10-04',
                vote_average: 8.2,
                genre: 'دراما، جريمة',
                category: 'drama'
            },
            {
                id: 15,
                title: 'دون: الجزء الثاني',
                poster_path: 'https://image.tmdb.org/t/p/w500/1pdfLvkbY9ohJlCjQH2CZjjYVvJ.jpg',
                release_date: '2024-03-01',
                vote_average: 8.5,
                genre: 'خيال علمي، دراما',
                category: 'scifi'
            }
        ];

        const demoSeries = [
            // Arabic Series
            {
                id: 1,
                name: 'بيت التنين',
                poster_path: 'https://image.tmdb.org/t/p/w500/z2yahl2uefxDCl0nogcRBstwruJ.jpg',
                first_air_date: '2022-08-21',
                vote_average: 8.5,
                genre: 'دراما، فانتازيا',
                category: 'arabic'
            },
            {
                id: 2,
                name: 'حلقات القوة',
                poster_path: 'https://image.tmdb.org/t/p/w500/mYLOqiStMxDK3fYZFirgrMt8z5d.jpg',
                first_air_date: '2022-09-02',
                vote_average: 7.3,
                genre: 'فانتازيا، دراما',
                category: 'american'
            },
            {
                id: 3,
                name: 'أشياء غريبة',
                poster_path: 'https://image.tmdb.org/t/p/w500/49WJfeN0moxb9IPfGn8AIqMGskD.jpg',
                first_air_date: '2016-07-15',
                vote_average: 8.7,
                genre: 'دراما، خيال علمي',
                category: 'american'
            },
            {
                id: 4,
                name: 'الدب',
                poster_path: 'https://image.tmdb.org/t/p/w500/zPIug5giU8oug6Xes5K1sTfQJxY.jpg',
                first_air_date: '2022-06-23',
                vote_average: 8.3,
                genre: 'كوميديا، دراما',
                category: 'american'
            },
            {
                id: 5,
                name: 'أوبي وان كينوبي',
                poster_path: 'https://image.tmdb.org/t/p/w500/qJRB789ceLryrLvOKrZqLKr2CGf.jpg',
                first_air_date: '2022-05-27',
                vote_average: 7.2,
                genre: 'خيال علمي، دراما',
                category: 'american'
            },
            {
                id: 6,
                name: 'مون نايت',
                poster_path: 'https://image.tmdb.org/t/p/w500/x6FsYvt33846IQnDSLxSCS3ZXNy.jpg',
                first_air_date: '2022-03-30',
                vote_average: 7.3,
                genre: 'أكشن، دراما',
                category: 'american'
            },
            {
                id: 7,
                name: 'يوفوريا',
                poster_path: 'https://image.tmdb.org/t/p/w500/jtnfNzqZwN4E32FGGxx1YZaBWWf.jpg',
                first_air_date: '2019-06-16',
                vote_average: 8.4,
                genre: 'دراما',
                category: 'american'
            },
            {
                id: 8,
                name: 'ذا بويز',
                poster_path: 'https://image.tmdb.org/t/p/w500/mY7SeH4HFFxW1hiI6cWuwCRKptN.jpg',
                first_air_date: '2019-07-26',
                vote_average: 8.7,
                genre: 'خيال علمي، كوميديا',
                category: 'american'
            },
            // Turkish Series
            {
                id: 9,
                name: 'قيامة أرطغرل',
                poster_path: 'https://image.tmdb.org/t/p/w500/eHC1Q2BzAoQmqiJJjMKNaPeRTAG.jpg',
                first_air_date: '2014-12-10',
                vote_average: 8.9,
                genre: 'دراما، تاريخي',
                category: 'turkish'
            },
            {
                id: 10,
                name: 'المؤسس عثمان',
                poster_path: 'https://image.tmdb.org/t/p/w500/9dWL2GJcSGlEKjNbNmNthxVoD4P.jpg',
                first_air_date: '2019-11-20',
                vote_average: 8.7,
                genre: 'دراما، تاريخي',
                category: 'turkish'
            },
            // Korean Series
            {
                id: 11,
                name: 'لعبة الحبار',
                poster_path: 'https://image.tmdb.org/t/p/w500/dDlEmu3EZ0Pgg93K2SVNLCjCSvE.jpg',
                first_air_date: '2021-09-17',
                vote_average: 8.0,
                genre: 'دراما، إثارة',
                category: 'korean'
            },
            {
                id: 12,
                name: 'مملكة',
                poster_path: 'https://image.tmdb.org/t/p/w500/qIe4aLTc8BlJ7Hus7BF2Rf7nnAr.jpg',
                first_air_date: '2019-01-25',
                vote_average: 8.3,
                genre: 'رعب، دراما',
                category: 'korean'
            },
            // Anime
            {
                id: 13,
                name: 'هجوم العمالقة',
                poster_path: 'https://image.tmdb.org/t/p/w500/hTP1DtLGFamjfu8WqjnuQdP1n4i.jpg',
                first_air_date: '2013-04-07',
                vote_average: 9.0,
                genre: 'أنمي، أكشن',
                category: 'anime'
            },
            {
                id: 14,
                name: 'ناروتو',
                poster_path: 'https://image.tmdb.org/t/p/w500/vauCEnR7CiyBDzRCeElKkCaXIYu.jpg',
                first_air_date: '2002-10-03',
                vote_average: 8.7,
                genre: 'أنمي، مغامرة',
                category: 'anime'
            }
        ];

        // ===== Utility Functions =====
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} position-fixed`;
            notification.style.cssText = `
                top: 100px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                animation: slideInRight 0.3s ease;
                box-shadow: 0 4px 15px rgba(0,0,0,0.3);
                border-radius: 1rem;
                border: none;
            `;

            const iconMap = {
                'success': 'check-circle',
                'error': 'exclamation-circle',
                'warning': 'exclamation-triangle',
                'info': 'info-circle'
            };

            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${iconMap[type]} me-2"></i>
                    <span>${message}</span>
                    <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOutRight 0.3s ease';
                    setTimeout(() => notification.remove(), 300);
                }
            }, 5000);
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.getFullYear();
        }

        // ===== Content Creation Functions =====
        function createMovieCard(item, type = 'movie') {
            const title = item.title || item.name;
            const releaseDate = item.release_date || item.first_air_date;
            const year = releaseDate ? formatDate(releaseDate) : 'غير محدد';
            const rating = item.vote_average ? item.vote_average.toFixed(1) : 'N/A';
            const qualityBadge = type === 'movie' ? 'HD' : 'مسلسل';
            const poster = item.poster_path || 'https://via.placeholder.com/300x450/333/fff?text=No+Image';

            return `
                <div class="movie-card" data-aos="fade-up" onclick="openMovieDetails(${item.id}, '${title}', '${type}')">
                    <div class="movie-poster">
                        <img src="${poster}" alt="${title}" loading="lazy" onerror="this.src='https://via.placeholder.com/300x450/333/fff?text=No+Image'">
                        <div class="quality-badge">${qualityBadge}</div>
                        <div class="movie-overlay">
                            <button class="play-btn" onclick="event.stopPropagation(); watchMovie(${item.id}, '${title}', '${type}')">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="movie-info">
                        <h3 class="movie-title">${title}</h3>
                        <div class="movie-meta">
                            <span class="movie-year">${year}</span>
                            <div class="movie-rating">
                                <i class="fas fa-star"></i>
                                <span>${rating}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // ===== Content Loading Functions =====
        function loadMovies() {
            const container = document.getElementById('latest-movies-grid');
            if (!container) return;
            container.innerHTML = demoMovies.slice(0, 6).map(movie => createMovieCard(movie, 'movie')).join('');
        }

        function loadSeries() {
            const container = document.getElementById('latest-series-grid');
            if (!container) return;
            container.innerHTML = demoSeries.slice(0, 6).map(series => createMovieCard(series, 'tv')).join('');
        }

        function loadTopRated() {
            const container = document.getElementById('top-rated-grid');
            if (!container) return;
            const topRated = [...demoMovies, ...demoSeries]
                .sort((a, b) => b.vote_average - a.vote_average)
                .slice(0, 6);
            container.innerHTML = topRated.map(item => {
                const type = item.title ? 'movie' : 'tv';
                return createMovieCard(item, type);
            }).join('');
        }

        // ===== Event Handlers =====
        function watchMovie(id, title, type) {
            showNotification(`بدء مشاهدة: ${title}`, 'success');
            window.location.href = `watch-pro.html?id=${id}&amp;title=${encodeURIComponent(title)}&amp;type=${type}`;
        }

        function watchSeries(id, title) {
            showNotification(`بدء مشاهدة: ${title}`, 'success');
            window.location.href = `watch-pro.html?id=${id}&amp;title=${encodeURIComponent(title)}&amp;type=series`;
        }

        function openMovieDetails(id, title, type) {
            showNotification(`فتح تفاصيل: ${title}`, 'info');
            window.location.href = `movie-details.html?id=${id}&amp;title=${encodeURIComponent(title)}&amp;type=${type}`;
        }

        function openSeriesDetails(id, title) {
            showNotification(`فتح تفاصيل: ${title}`, 'info');
            window.location.href = `movie-details.html?id=${id}&amp;title=${encodeURIComponent(title)}&amp;type=series`;
        }

        function addToFavorites(id, title, type = 'movie') {
            let favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
            const item = { id, title, type, addedAt: new Date().toISOString() };

            if (!favorites.find(fav => fav.id === id && fav.type === type)) {
                favorites.push(item);
                localStorage.setItem('favorites', JSON.stringify(favorites));
                showNotification(`تم إضافة "${title}" للمفضلة`, 'success');
            } else {
                showNotification(`"${title}" موجود بالفعل في المفضلة`, 'warning');
            }
        }

        function goToCategory(categoryUrl) {
            showNotification('جاري تحميل التصنيف...', 'info');
            window.location.href = categoryUrl;
        }



        function handleSearch(event) {
            event.preventDefault();
            const query = document.getElementById('searchInput').value.trim();

            if (!query) {
                showNotification('يرجى إدخال كلمة البحث', 'warning');
                return;
            }

            showNotification(`البحث عن: ${query}`, 'info');

            const searchResults = [
                ...demoMovies.filter(movie =>
                    movie.title.toLowerCase().includes(query.toLowerCase())
                ),
                ...demoSeries.filter(series =>
                    series.name.toLowerCase().includes(query.toLowerCase())
                )
            ];

            if (searchResults.length > 0) {
                displaySearchResults(searchResults, query);
            } else {
                showNotification('لم يتم العثور على نتائج', 'warning');
            }
        }

        function displaySearchResults(results, query) {
            showNotification(`تم العثور على ${results.length} نتيجة`, 'success');
        }

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        function subscribeNewsletter(event) {
            event.preventDefault();
            const email = event.target.querySelector('input[type="email"]').value;

            if (email) {
                showNotification(`تم الاشتراك بنجاح! سنرسل لك أحدث الأفلام على: ${email}`, 'success');
                event.target.reset();

                let subscribers = JSON.parse(localStorage.getItem('newsletter_subscribers') || '[]');
                if (!subscribers.includes(email)) {
                    subscribers.push(email);
                    localStorage.setItem('newsletter_subscribers', JSON.stringify(subscribers));
                }
            }
        }

        // ===== Initialization =====
        function initializeApp() {
            setTimeout(() => {
                const loadingScreen = document.getElementById('loadingScreen');
                if (loadingScreen) {
                    loadingScreen.classList.add('hidden');
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                    }, 500);
                }
            }, 2500);

            if (typeof AOS !== 'undefined') {
                AOS.init({
                    duration: 800,
                    easing: 'ease-in-out',
                    once: true,
                    offset: 100,
                    delay: 100
                });
            }

            if (typeof Swiper !== 'undefined') {
                const heroSwiper = new Swiper('.heroSwiper', {
                    loop: true,
                    autoplay: {
                        delay: 5000,
                        disableOnInteraction: false,
                    },
                    effect: 'fade',
                    fadeEffect: {
                        crossFade: true
                    },
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev',
                    },
                    pagination: {
                        el: '.swiper-pagination',
                        clickable: true,
                    },
                });
            }

            loadMovies();
            loadSeries();
            loadTopRated();
            initializeHeaderScroll();
            initializeBackToTop();
            initializeNavigation();
            initializeUserSession();

            console.log('CinemaHub Pro initialized successfully!');
        }

        function initializeHeaderScroll() {
            const header = document.getElementById('header');
            if (!header) return;

            window.addEventListener('scroll', () => {
                if (window.scrollY > 50) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            });
        }

        function initializeBackToTop() {
            const backToTopBtn = document.getElementById('backToTop');
            if (!backToTopBtn) return;

            window.addEventListener('scroll', () => {
                if (window.scrollY > 300) {
                    backToTopBtn.classList.add('show');
                } else {
                    backToTopBtn.classList.remove('show');
                }
            });
        }

        function initializeNavigation() {
            const navLinks = document.querySelectorAll('.nav-link[href^="#"]');
            const sections = document.querySelectorAll('section[id]');

            window.addEventListener('scroll', () => {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    if (window.scrollY >= (sectionTop - 200)) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === `#${current}`) {
                        link.classList.add('active');
                    }
                });
            });
        }

        function initializeUserSession() {
            // Check for saved user session
            const savedUser = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
            if (savedUser) {
                try {
                    currentUser = JSON.parse(savedUser);
                    updateUserInterface();

                    // Load admin data if user is admin
                    if (currentUser.role === 'admin') {
                        setTimeout(() => {
                            loadAnalyticsData();
                        }, 1000);
                    }
                } catch (error) {
                    console.error('Error parsing saved user data:', error);
                    localStorage.removeItem('currentUser');
                    sessionStorage.removeItem('currentUser');
                }
            }

        }

        function toggleUserDropdown() {
            const dropdown = document.getElementById('userDropdownMenu');
            const container = document.querySelector('.user-dropdown-container');

            if (dropdown && container) {
                dropdown.classList.toggle('show');
                container.classList.toggle('active');
            }
        }

        function showMoviesMenu() {
            event.preventDefault();

            const moviesMenu = document.getElementById('moviesMenu');
            const seriesMenu = document.getElementById('seriesMenu');

            // Close series menu
            if (seriesMenu) {
                seriesMenu.style.display = 'none';
            }

            // Toggle movies menu
            if (moviesMenu.style.display === 'none' || moviesMenu.style.display === '') {
                moviesMenu.style.display = 'block';
            } else {
                moviesMenu.style.display = 'none';
            }
        }

        function showSeriesMenu() {
            event.preventDefault();

            const moviesMenu = document.getElementById('moviesMenu');
            const seriesMenu = document.getElementById('seriesMenu');

            // Close movies menu
            if (moviesMenu) {
                moviesMenu.style.display = 'none';
            }

            // Toggle series menu
            if (seriesMenu.style.display === 'none' || seriesMenu.style.display === '') {
                seriesMenu.style.display = 'block';
            } else {
                seriesMenu.style.display = 'none';
            }
        }

        function initializeUserDropdown() {
            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                const container = document.querySelector('.user-dropdown-container');
                const dropdown = document.getElementById('userDropdownMenu');

                if (container && dropdown && !container.contains(e.target)) {
                    dropdown.classList.remove('show');
                    container.classList.remove('active');
                }
            });

            // Close dropdown when pressing Escape
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    const dropdown = document.getElementById('userDropdownMenu');
                    const container = document.querySelector('.user-dropdown-container');

                    if (dropdown && container) {
                        dropdown.classList.remove('show');
                        container.classList.remove('active');
                    }
                }
            });
        }

        function initializeNavDropdowns() {
            // Get all nav items with dropdowns
            const navItems = document.querySelectorAll('.nav-item');

            navItems.forEach(navItem => {
                const navLink = navItem.querySelector('.nav-link');
                const dropdownMenu = navItem.querySelector('.dropdown-menu');

                if (navLink && dropdownMenu) {
                    // Add click event for mobile/touch devices
                    navLink.addEventListener('click', function(e) {
                        // Only prevent default if it's a dropdown trigger
                        if (this.getAttribute('href') === '#') {
                            e.preventDefault();

                            // Close other dropdowns
                            navItems.forEach(item => {
                                if (item !== navItem) {
                                    item.classList.remove('active');
                                    const otherDropdown = item.querySelector('.dropdown-menu');
                                    if (otherDropdown) {
                                        otherDropdown.classList.remove('show');
                                    }
                                }
                            });

                            // Toggle current dropdown
                            navItem.classList.toggle('active');
                            dropdownMenu.classList.toggle('show');
                        }
                    });
                }
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.nav-item')) {
                    navItems.forEach(item => {
                        item.classList.remove('active');
                        const dropdown = item.querySelector('.dropdown-menu');
                        if (dropdown) {
                            dropdown.classList.remove('show');
                        }
                    });
                }
            });
        }

        // ===== Event Listeners =====
        document.addEventListener('DOMContentLoaded', () => {
            try {
                initializeApp();
                initializeHeaderScroll();

                // Initialize user dropdown
                initializeUserDropdown();

                // Initialize navigation dropdowns
                initializeNavDropdowns();

                // Close dropdowns when clicking outside
                document.addEventListener('click', function(e) {
                    if (!e.target.closest('.nav-item.dropdown')) {
                        const moviesMenu = document.getElementById('moviesMenu');
                        const seriesMenu = document.getElementById('seriesMenu');

                        if (moviesMenu) {
                            moviesMenu.style.display = 'none';
                        }
                        if (seriesMenu) {
                            seriesMenu.style.display = 'none';
                        }
                    }
                });

            } catch (error) {
                console.error('Error initializing app:', error);
                showNotification('حدث خطأ في تحميل الموقع', 'error');
            }
        });

        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                document.getElementById('searchInput').focus();
            }

            if (e.key === 'Escape') {
                document.getElementById('searchInput').blur();
            }

            if (e.key === 'Home' && !e.target.matches('input, textarea')) {
                e.preventDefault();
                scrollToTop();
            }
        });

        window.addEventListener('online', () => {
            showNotification('تم استعادة الاتصال بالإنترنت', 'success');
        });

        window.addEventListener('offline', () => {
            showNotification('تم فقدان الاتصال بالإنترنت', 'warning');
        });

        console.log('CinemaHub Pro script loaded successfully!');
    </script>


</body>
</html>
