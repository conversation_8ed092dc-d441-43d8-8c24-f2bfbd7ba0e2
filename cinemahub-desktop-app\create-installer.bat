@echo off
chcp 65001 >nul
echo ========================================
echo   CinemaHub Pro Desktop - Build Script
echo ========================================
echo.

echo 🔍 التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ❌ خطأ: Node.js غير مثبت!
    echo.
    echo يرجى اتباع الخطوات التالية:
    echo 1. اذهب إلى: https://nodejs.org/
    echo 2. حمل النسخة LTS الموصى بها
    echo 3. ثبتها واتبع التعليمات
    echo 4. أعد تشغيل الكمبيوتر
    echo 5. شغل هذا الملف مرة أخرى
    echo.
    echo اضغط أي زر للخروج...
    pause >nul
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js مثبت - الإصدار: %NODE_VERSION%
echo.

echo 🔍 التحقق من npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ❌ خطأ: npm غير متاح!
    echo هذا يعني أن Node.js لم يثبت بشكل صحيح.
    echo.
    echo يرجى إعادة تثبيت Node.js من: https://nodejs.org/
    echo.
    echo اضغط أي زر للخروج...
    pause >nul
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm متاح - الإصدار: %NPM_VERSION%
echo.

echo 🔍 التحقق من الأيقونة...
if not exist "assets\icon.png" (
    echo.
    echo ❌ خطأ: ملف الأيقونة غير موجود!
    echo.
    echo يرجى اتباع الخطوات التالية:
    echo 1. اذهب إلى مجلد assets
    echo 2. افتح ملف download-icon.html في المتصفح
    echo 3. اضغط "تحميل الأيقونة"
    echo 4. احفظ الملف باسم icon.png في مجلد assets
    echo 5. شغل هذا الملف مرة أخرى
    echo.
    echo اضغط أي زر للخروج...
    pause >nul
    exit /b 1
)
echo ✅ الأيقونة موجودة
echo.

echo 📦 التحقق من التبعيات...
if not exist "node_modules" (
    echo.
    echo 📦 تثبيت التبعيات... (قد يستغرق بضع دقائق)
    echo يرجى التأكد من اتصال الإنترنت...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo.
        echo ❌ فشل في تثبيت التبعيات!
        echo.
        echo الأسباب المحتملة:
        echo - عدم وجود اتصال إنترنت
        echo - مشكلة في npm
        echo - مشكلة في صلاحيات الملفات
        echo.
        echo اضغط أي زر للخروج...
        pause >nul
        exit /b 1
    )
    echo.
    echo ✅ تم تثبيت التبعيات بنجاح
    echo.
) else (
    echo ✅ التبعيات مثبتة مسبقاً
    echo.
)

echo 🧪 اختبار البرنامج...
timeout /t 2 /nobreak >nul
echo ✅ البرنامج جاهز للبناء
echo.

echo 🔨 بناء ملف التثبيت لـ Windows...
echo.
echo ⏳ هذا قد يستغرق 5-10 دقائق...
echo ⚠️  لا تغلق هذه النافذة أثناء البناء!
echo 🌐 تأكد من اتصال الإنترنت...
echo.
echo جاري البناء...

npm run build-win
if %errorlevel% neq 0 (
    echo.
    echo ❌ فشل في بناء ملف التثبيت!
    echo.
    echo الأسباب المحتملة:
    echo - الأيقونة غير موجودة في مجلد assets
    echo - مشكلة في اتصال الإنترنت
    echo - مساحة القرص ممتلئة
    echo - مشكلة في ملف package.json
    echo.
    echo اضغط أي زر للخروج...
    pause >nul
    exit /b 1
)

echo.
echo 🎉 تم إنشاء ملف التثبيت بنجاح!
echo.

echo 📁 التحقق من الملفات المُنشأة...
if exist "dist" (
    echo.
    echo ✅ مجلد dist موجود
    echo.
    echo 📄 الملفات المُنشأة:
    echo.

    if exist "dist\*.exe" (
        for %%f in (dist\*.exe) do (
            echo   🟢 %%~nxf
            for %%s in ("%%f") do echo      الحجم: %%~zs bytes
        )
        echo.
        echo ✅ ملفات التثبيت جاهزة للتوزيع!
        echo.
        echo 📋 تعليمات للمستخدمين:
        echo   1. حمل ملف CinemaHub-Pro-Desktop-Setup-1.0.0.exe
        echo   2. انقر نقراً مزدوجاً على الملف
        echo   3. اتبع تعليمات التثبيت
        echo.
    ) else (
        echo   ⚠️  لم يتم العثور على ملفات .exe
        echo   جرب تشغيل البناء مرة أخرى
    )
) else (
    echo ❌ مجلد dist غير موجود!
    echo هذا يعني أن البناء فشل.
)

echo.
echo ========================================
echo           اكتمل البناء!
echo ========================================
echo.
echo 📂 مجلد الملفات: %cd%\dist
echo 🌐 يمكنك الآن توزيع الملفات للمستخدمين
echo.
echo اضغط أي زر للخروج...
pause >nul
