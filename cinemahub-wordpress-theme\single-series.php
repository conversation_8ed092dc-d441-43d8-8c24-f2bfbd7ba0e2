<?php
/**
 * Single series template
 *
 * @package CinemaHub Pro
 */

get_header(); ?>

<?php while (have_posts()) : the_post(); ?>
    <?php
    $rating = get_post_meta(get_the_ID(), '_series_rating', true);
    $year = get_post_meta(get_the_ID(), '_series_year', true);
    $seasons = get_post_meta(get_the_ID(), '_series_seasons', true);
    $episodes = get_post_meta(get_the_ID(), '_series_episodes', true);
    $status = get_post_meta(get_the_ID(), '_series_status', true);
    $trailer_url = get_post_meta(get_the_ID(), '_series_trailer_url', true);
    $watch_url = get_post_meta(get_the_ID(), '_series_watch_url', true);
    ?>

    <div style="margin-top: 80px;">
        <!-- Series Hero Section -->
        <section class="series-hero" style="background: linear-gradient(rgba(15,23,42,0.8), rgba(15,23,42,0.9)), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'large'); ?>') center/cover; padding: 4rem 0; min-height: 60vh; display: flex; align-items: center;">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-4">
                        <div class="series-poster-large" style="text-align: center;">
                            <?php if (has_post_thumbnail()) : ?>
                                <?php the_post_thumbnail('movie-poster', array('style' => 'border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.5); max-width: 300px; width: 100%;')); ?>
                            <?php else : ?>
                                <img src="https://via.placeholder.com/300x400/1e293b/ffffff?text=<?php echo urlencode(get_the_title()); ?>" alt="<?php the_title(); ?>" style="border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.5); max-width: 300px; width: 100%;">
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="col-lg-8">
                        <div class="series-details" style="padding: 2rem;">
                            <h1 style="font-size: 2.5rem; font-weight: 800; margin-bottom: 1rem; color: white;"><?php the_title(); ?></h1>
                            
                            <div class="series-meta" style="display: flex; flex-wrap: wrap; gap: 1rem; margin-bottom: 2rem;">
                                <?php if ($rating) : ?>
                                    <div class="meta-item" style="background: var(--gradient-primary); padding: 0.5rem 1rem; border-radius: 25px; display: flex; align-items: center; gap: 0.5rem;">
                                        <i class="fas fa-star" style="color: #fbbf24;"></i>
                                        <span style="font-weight: 600;"><?php echo esc_html($rating); ?>/10</span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($year) : ?>
                                    <div class="meta-item" style="background: rgba(255,255,255,0.1); padding: 0.5rem 1rem; border-radius: 25px; display: flex; align-items: center; gap: 0.5rem;">
                                        <i class="fas fa-calendar"></i>
                                        <span><?php echo esc_html($year); ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($seasons) : ?>
                                    <div class="meta-item" style="background: rgba(255,255,255,0.1); padding: 0.5rem 1rem; border-radius: 25px; display: flex; align-items: center; gap: 0.5rem;">
                                        <i class="fas fa-list"></i>
                                        <span><?php echo esc_html($seasons); ?> موسم</span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($episodes) : ?>
                                    <div class="meta-item" style="background: rgba(255,255,255,0.1); padding: 0.5rem 1rem; border-radius: 25px; display: flex; align-items: center; gap: 0.5rem;">
                                        <i class="fas fa-play-circle"></i>
                                        <span><?php echo esc_html($episodes); ?> حلقة</span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($status) : ?>
                                    <div class="meta-item" style="background: <?php echo $status === 'completed' ? 'var(--success-color)' : ($status === 'ongoing' ? 'var(--warning-color)' : 'var(--danger-color)'); ?>; padding: 0.5rem 1rem; border-radius: 25px; display: flex; align-items: center; gap: 0.5rem;">
                                        <i class="fas fa-info-circle"></i>
                                        <span style="font-weight: 600;">
                                            <?php 
                                            echo $status === 'completed' ? 'مكتمل' : ($status === 'ongoing' ? 'مستمر' : 'ملغي');
                                            ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="series-description" style="color: var(--gray-300); line-height: 1.8; margin-bottom: 2rem; font-size: 1.1rem;">
                                <?php the_content(); ?>
                            </div>
                            
                            <div class="series-actions" style="display: flex; gap: 1rem; flex-wrap: wrap;">
                                <?php if ($watch_url) : ?>
                                    <a href="<?php echo esc_url($watch_url); ?>" class="btn-watch" style="background: var(--gradient-primary); color: white; padding: 1rem 2rem; border-radius: 50px; text-decoration: none; font-weight: 600; display: flex; align-items: center; gap: 0.5rem; transition: all 0.3s ease;">
                                        <i class="fas fa-play"></i>
                                        مشاهدة المسلسل
                                    </a>
                                <?php endif; ?>
                                
                                <?php if ($trailer_url) : ?>
                                    <a href="<?php echo esc_url($trailer_url); ?>" target="_blank" class="btn-trailer" style="background: rgba(255,255,255,0.1); color: white; padding: 1rem 2rem; border-radius: 50px; text-decoration: none; font-weight: 600; display: flex; align-items: center; gap: 0.5rem; transition: all 0.3s ease;">
                                        <i class="fas fa-video"></i>
                                        مشاهدة الإعلان
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Series Info Section -->
        <section style="padding: 3rem 0; background: var(--surface-color);">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="series-content" style="background: var(--dark-color); padding: 2rem; border-radius: 15px;">
                            <h3 style="margin-bottom: 1.5rem; color: var(--primary-color);">
                                <i class="fas fa-info-circle"></i>
                                تفاصيل المسلسل
                            </h3>
                            
                            <?php
                            $genres = get_the_terms(get_the_ID(), 'series_genre');
                            $countries = get_the_terms(get_the_ID(), 'country');
                            ?>
                            
                            <div class="series-info-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
                                <?php if ($genres && !is_wp_error($genres)) : ?>
                                    <div class="info-item">
                                        <strong style="color: var(--gray-400);">التصنيف:</strong>
                                        <div style="margin-top: 0.5rem;">
                                            <?php foreach ($genres as $genre) : ?>
                                                <span style="background: var(--gradient-primary); color: white; padding: 0.25rem 0.75rem; border-radius: 15px; font-size: 0.85rem; margin-left: 0.5rem; display: inline-block; margin-bottom: 0.25rem;">
                                                    <?php echo esc_html($genre->name); ?>
                                                </span>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($countries && !is_wp_error($countries)) : ?>
                                    <div class="info-item">
                                        <strong style="color: var(--gray-400);">البلد:</strong>
                                        <div style="margin-top: 0.5rem;">
                                            <?php foreach ($countries as $country) : ?>
                                                <span style="color: var(--light-color);"><?php echo esc_html($country->name); ?></span>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <?php if (get_the_excerpt()) : ?>
                                <div class="series-excerpt" style="color: var(--gray-300); line-height: 1.6;">
                                    <h4 style="color: var(--light-color); margin-bottom: 1rem;">ملخص المسلسل:</h4>
                                    <?php the_excerpt(); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <?php get_sidebar(); ?>
                    </div>
                </div>
            </div>
        </section>

        <!-- Related Series -->
        <section style="padding: 3rem 0;">
            <div class="container">
                <h3 style="margin-bottom: 2rem; text-align: center;">
                    <i class="fas fa-tv"></i>
                    مسلسلات مشابهة
                </h3>
                
                <div class="movies-grid">
                    <?php
                    $related_series = new WP_Query(array(
                        'post_type' => 'series',
                        'posts_per_page' => 4,
                        'post__not_in' => array(get_the_ID()),
                        'orderby' => 'rand'
                    ));
                    
                    if ($related_series->have_posts()) :
                        while ($related_series->have_posts()) : $related_series->the_post();
                            $rating = get_post_meta(get_the_ID(), '_series_rating', true);
                            $year = get_post_meta(get_the_ID(), '_series_year', true);
                            $seasons = get_post_meta(get_the_ID(), '_series_seasons', true);
                            ?>
                            <div class="movie-card">
                                <div class="movie-poster">
                                    <?php if (has_post_thumbnail()) : ?>
                                        <?php the_post_thumbnail('movie-poster', array('alt' => get_the_title())); ?>
                                    <?php else : ?>
                                        <img src="https://via.placeholder.com/300x400/1e293b/ffffff?text=<?php echo urlencode(get_the_title()); ?>" alt="<?php the_title(); ?>">
                                    <?php endif; ?>
                                    
                                    <div class="movie-overlay">
                                        <div class="play-btn">
                                            <i class="fas fa-play"></i>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="movie-info">
                                    <h3 class="movie-title">
                                        <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                    </h3>
                                    
                                    <div class="movie-meta">
                                        <?php if ($year) : ?>
                                            <span class="meta-item">
                                                <i class="fas fa-calendar"></i>
                                                <?php echo esc_html($year); ?>
                                            </span>
                                        <?php endif; ?>
                                        
                                        <?php if ($seasons) : ?>
                                            <span class="meta-item">
                                                <i class="fas fa-list"></i>
                                                <?php echo esc_html($seasons); ?> موسم
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <?php if ($rating) : ?>
                                        <div class="movie-rating">
                                            <i class="fas fa-star"></i>
                                            <span><?php echo esc_html($rating); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php
                        endwhile;
                        wp_reset_postdata();
                    endif;
                    ?>
                </div>
            </div>
        </section>
    </div>
<?php endwhile; ?>

<style>
.btn-watch:hover, .btn-trailer:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(220, 38, 38, 0.3);
}

@media (max-width: 768px) {
    .series-hero {
        text-align: center;
    }
    
    .series-details {
        padding: 1rem !important;
    }
    
    .series-details h1 {
        font-size: 2rem !important;
    }
    
    .series-actions {
        justify-content: center;
    }
}
</style>

<?php get_footer(); ?>
