@echo off
title CinemaHub Pro Desktop Builder

echo ========================================
echo   CinemaHub Pro Desktop Builder
echo ========================================
echo.

echo Step 1: Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed!
    echo Please download from: https://nodejs.org/
    echo.
    pause
    exit /b 1
)
echo OK: Node.js is installed

echo.
echo Step 2: Checking icon...
if not exist "assets\icon.png" (
    echo ERROR: Icon file missing!
    echo Please:
    echo 1. Open assets\download-icon.html
    echo 2. Click download button
    echo 3. Save as icon.png in assets folder
    echo.
    pause
    exit /b 1
)
echo OK: Icon file found

echo.
echo Step 3: Installing dependencies...
if not exist "node_modules" (
    echo Installing packages...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install packages!
        pause
        exit /b 1
    )
)
echo OK: Dependencies ready

echo.
echo Step 4: Building installer...
echo This may take 5-10 minutes...
echo Please wait and do not close this window!
echo.

npm run build-win
if %errorlevel% neq 0 (
    echo ERROR: Build failed!
    echo Check:
    echo - Internet connection
    echo - Icon file exists
    echo - Enough disk space
    pause
    exit /b 1
)

echo.
echo SUCCESS: Build completed!
echo.

if exist "dist\*.exe" (
    echo Installer files created:
    dir dist\*.exe /b
    echo.
    echo Files are ready in 'dist' folder!
) else (
    echo WARNING: No .exe files found in dist folder
)

echo.
echo ========================================
echo           BUILD COMPLETE!
echo ========================================
echo.
echo You can now distribute the files to users.
echo.
pause
