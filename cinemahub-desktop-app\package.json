{"name": "cinemahub-pro-desktop", "version": "1.0.0", "description": "CinemaHub Pro Desktop Application", "main": "main.js", "homepage": "./", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "pack": "electron-builder --dir"}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"axios": "^1.5.0", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "chokidar": "^3.5.3"}, "build": {"appId": "com.cinemahub.desktop", "productName": "CinemaHub Pro Desktop", "directories": {"output": "dist"}, "files": ["main.js", "src/**/*", "package.json", "!node_modules/**/*", "!dist/**/*", "!*.bat", "!*.md"], "extraFiles": [{"from": "assets/icon.png", "to": "icon.png"}], "win": {"target": {"target": "nsis", "arch": ["x64"]}, "icon": "assets/icon.png", "artifactName": "${productName}-Setup-${version}.${ext}"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "installerIcon": "assets/icon.png", "uninstallerIcon": "assets/icon.png"}}}