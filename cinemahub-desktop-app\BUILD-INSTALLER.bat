@echo off
title CinemaHub Pro - Build Installer

echo ==========================================
echo    CinemaHub Pro Desktop - Build Tool
echo ==========================================
echo.

echo Step 1: Checking system...

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not installed!
    echo.
    echo Please:
    echo 1. Go to https://nodejs.org/
    echo 2. Download LTS version
    echo 3. Install and restart computer
    echo 4. Run this file again
    echo.
    pause
    exit /b 1
)

echo Node.js: OK

REM Check npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm not available!
    pause
    exit /b 1
)

echo npm: OK
echo.

echo Step 2: Preparing build...

REM Clean old files
if exist "dist" (
    echo Removing old build files...
    rmdir /s /q dist 2>nul
)
mkdir dist 2>nul

echo Step 3: Installing packages...
echo This may take several minutes...
echo.

npm install
if %errorlevel% neq 0 (
    echo Installation failed. Trying alternative...
    npm install --legacy-peer-deps
    if %errorlevel% neq 0 (
        echo ERROR: Cannot install packages!
        echo Check your internet connection.
        pause
        exit /b 1
    )
)

echo Packages installed successfully!
echo.

echo Step 4: Building installer...
echo This will take 5-10 minutes...
echo Please be patient and do not close this window!
echo.

npm run build-win
if %errorlevel% neq 0 (
    echo Build failed! Trying alternative method...
    npx electron-builder --win
    if %errorlevel% neq 0 (
        echo ERROR: Build failed!
        echo.
        echo Try these solutions:
        echo - Check internet connection
        echo - Free up disk space (need 2GB+)
        echo - Run as administrator
        echo - Disable antivirus temporarily
        echo.
        pause
        exit /b 1
    )
)

echo.
echo SUCCESS! Build completed successfully!
echo.

REM Show results
if exist "dist\*.exe" (
    echo Installer files created:
    echo.
    for %%f in (dist\*.exe) do (
        echo   %%~nxf
    )
    echo.
    echo Location: %cd%\dist
    echo.
    echo Ready for distribution!
    echo Users can install by double-clicking the .exe file
    echo.
    echo Opening results folder...
    explorer dist
) else (
    echo WARNING: No installer files found!
    echo Check the dist folder manually.
    if exist "dist" explorer dist
)

echo.
echo ==========================================
echo           BUILD COMPLETE!
echo ==========================================
echo.
echo Press any key to exit...
pause >nul
