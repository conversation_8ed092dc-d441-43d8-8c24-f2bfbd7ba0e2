<?php
/**
 * 404 Error Page Template
 *
 * @package CinemaHub Pro
 */

get_header(); ?>

<div style="margin-top: 80px; padding: 4rem 0; min-height: 70vh; display: flex; align-items: center;">
    <div class="container">
        <div style="text-align: center; max-width: 600px; margin: 0 auto;">
            <!-- 404 Animation -->
            <div style="margin-bottom: 3rem;">
                <div style="font-size: 8rem; font-weight: 900; background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; line-height: 1; margin-bottom: 1rem; animation: pulse 2s infinite;">
                    404
                </div>
                <div style="font-size: 3rem; color: var(--primary-color); margin-bottom: 1rem;">
                    <i class="fas fa-film" style="animation: spin 3s linear infinite;"></i>
                </div>
            </div>

            <!-- Error Message -->
            <h1 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem; color: var(--light-color);">
                الصفحة غير موجودة
            </h1>
            
            <p style="font-size: 1.2rem; color: var(--gray-400); margin-bottom: 3rem; line-height: 1.6;">
                عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى مكان آخر.
                <br>
                ربما تم حذف الرابط أو كتابته بشكل خاطئ.
            </p>

            <!-- Search Form -->
            <div style="margin-bottom: 3rem;">
                <h3 style="color: var(--light-color); margin-bottom: 1rem;">جرب البحث:</h3>
                <form role="search" method="get" action="<?php echo home_url('/'); ?>" style="position: relative; max-width: 400px; margin: 0 auto;">
                    <input type="search" name="s" placeholder="ابحث عن فيلم أو مسلسل..." style="width: 100%; padding: 1rem 3rem 1rem 1rem; border: 2px solid var(--border-color); border-radius: 50px; background: var(--surface-color); color: var(--light-color); font-size: 1rem; outline: none; transition: all 0.3s ease;">
                    <button type="submit" style="position: absolute; left: 1rem; top: 50%; transform: translateY(-50%); background: var(--gradient-primary); border: none; color: white; font-size: 1rem; cursor: pointer; padding: 0.75rem; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>

            <!-- Quick Links -->
            <div style="margin-bottom: 3rem;">
                <h3 style="color: var(--light-color); margin-bottom: 1.5rem;">أو انتقل إلى:</h3>
                <div style="display: flex; flex-wrap: wrap; gap: 1rem; justify-content: center;">
                    <a href="<?php echo home_url('/'); ?>" style="background: var(--gradient-primary); color: white; padding: 1rem 2rem; border-radius: 50px; text-decoration: none; font-weight: 600; display: flex; align-items: center; gap: 0.5rem; transition: all 0.3s ease;">
                        <i class="fas fa-home"></i>
                        الصفحة الرئيسية
                    </a>
                    
                    <a href="<?php echo get_post_type_archive_link('movie'); ?>" style="background: rgba(255,255,255,0.1); color: var(--light-color); padding: 1rem 2rem; border-radius: 50px; text-decoration: none; font-weight: 600; display: flex; align-items: center; gap: 0.5rem; transition: all 0.3s ease; border: 2px solid var(--border-color);">
                        <i class="fas fa-film"></i>
                        الأفلام
                    </a>
                    
                    <a href="<?php echo get_post_type_archive_link('series'); ?>" style="background: rgba(255,255,255,0.1); color: var(--light-color); padding: 1rem 2rem; border-radius: 50px; text-decoration: none; font-weight: 600; display: flex; align-items: center; gap: 0.5rem; transition: all 0.3s ease; border: 2px solid var(--border-color);">
                        <i class="fas fa-tv"></i>
                        المسلسلات
                    </a>
                </div>
            </div>

            <!-- Popular Content -->
            <div style="margin-top: 4rem;">
                <h3 style="color: var(--light-color); margin-bottom: 2rem;">المحتوى الشائع:</h3>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem; max-width: 800px; margin: 0 auto;">
                    <?php
                    // Get popular movies
                    $popular_content = new WP_Query(array(
                        'post_type' => array('movie', 'series'),
                        'posts_per_page' => 4,
                        'meta_key' => 'views',
                        'orderby' => 'meta_value_num',
                        'order' => 'DESC'
                    ));
                    
                    if ($popular_content->have_posts()) :
                        while ($popular_content->have_posts()) : $popular_content->the_post();
                            $post_type = get_post_type();
                            $rating = get_post_meta(get_the_ID(), '_' . $post_type . '_rating', true);
                            $year = get_post_meta(get_the_ID(), '_' . $post_type . '_year', true);
                            ?>
                            <div style="background: var(--dark-color); border-radius: 15px; overflow: hidden; transition: all 0.3s ease; border: 2px solid transparent;">
                                <div style="position: relative; overflow: hidden;">
                                    <?php if (has_post_thumbnail()) : ?>
                                        <a href="<?php the_permalink(); ?>">
                                            <?php the_post_thumbnail('medium', array('style' => 'width: 100%; height: 250px; object-fit: cover; transition: transform 0.3s ease;')); ?>
                                        </a>
                                    <?php else : ?>
                                        <a href="<?php the_permalink(); ?>">
                                            <div style="width: 100%; height: 250px; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">
                                                <i class="fas fa-<?php echo $post_type === 'series' ? 'tv' : 'film'; ?>"></i>
                                            </div>
                                        </a>
                                    <?php endif; ?>
                                    
                                    <div style="position: absolute; top: 10px; right: 10px; background: var(--gradient-primary); color: white; padding: 0.25rem 0.75rem; border-radius: 15px; font-size: 0.8rem; font-weight: 600;">
                                        <?php echo $post_type === 'series' ? 'مسلسل' : 'فيلم'; ?>
                                    </div>
                                </div>
                                
                                <div style="padding: 1.5rem;">
                                    <h4 style="margin: 0 0 0.5rem 0; font-size: 1rem; font-weight: 600; line-height: 1.3;">
                                        <a href="<?php the_permalink(); ?>" style="color: var(--light-color); text-decoration: none;">
                                            <?php the_title(); ?>
                                        </a>
                                    </h4>
                                    
                                    <div style="display: flex; align-items: center; justify-content: space-between; color: var(--gray-400); font-size: 0.9rem;">
                                        <?php if ($year) : ?>
                                            <span><i class="fas fa-calendar"></i> <?php echo esc_html($year); ?></span>
                                        <?php endif; ?>
                                        
                                        <?php if ($rating) : ?>
                                            <span style="color: var(--warning-color);"><i class="fas fa-star"></i> <?php echo esc_html($rating); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <?php
                        endwhile;
                        wp_reset_postdata();
                    else :
                        // Default content if no posts exist
                        for ($i = 1; $i <= 4; $i++) :
                            ?>
                            <div style="background: var(--dark-color); border-radius: 15px; overflow: hidden; transition: all 0.3s ease;">
                                <div style="position: relative;">
                                    <div style="width: 100%; height: 250px; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">
                                        <i class="fas fa-film"></i>
                                    </div>
                                    <div style="position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 0.25rem 0.75rem; border-radius: 15px; font-size: 0.8rem;">
                                        فيلم
                                    </div>
                                </div>
                                <div style="padding: 1.5rem;">
                                    <h4 style="margin: 0 0 0.5rem 0; color: var(--light-color);">فيلم شائع <?php echo $i; ?></h4>
                                    <div style="display: flex; align-items: center; justify-content: space-between; color: var(--gray-400); font-size: 0.9rem;">
                                        <span><i class="fas fa-calendar"></i> 2024</span>
                                        <span style="color: var(--warning-color);"><i class="fas fa-star"></i> 8.<?php echo $i; ?></span>
                                    </div>
                                </div>
                            </div>
                            <?php
                        endfor;
                    endif;
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.quick-links a:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(220, 38, 38, 0.3);
}

.popular-content-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-5px);
}

.popular-content-item:hover img {
    transform: scale(1.05);
}

.search-form input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

@media (max-width: 768px) {
    .quick-links {
        flex-direction: column;
        align-items: center;
    }
    
    .quick-links a {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
    
    .popular-content-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)) !important;
    }
}
</style>

<?php get_footer(); ?>
