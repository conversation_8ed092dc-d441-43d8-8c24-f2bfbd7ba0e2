/**
 * CinemaHub Pro Desktop Application
 * Sync Manager - Synchronizes with website code
 * 
 * @version 1.0.0
 * <AUTHOR> Team
 */

const fs = require('fs-extra');
const path = require('path');
const chokidar = require('chokidar');
const axios = require('axios');

class SyncManager {
    constructor() {
        this.isWatching = false;
        this.watcher = null;
        this.syncInProgress = false;
        this.websiteCodePath = null;
        this.lastSyncTime = null;
        this.changedFiles = new Set();
        this.syncQueue = [];
        
        this.init();
    }

    /**
     * Initialize sync manager
     */
    async init() {
        try {
            // Load sync settings
            await this.loadSyncSettings();
            
            // Start watching if auto-sync is enabled
            const autoSync = await this.getSetting('auto_sync');
            if (autoSync === 'true') {
                await this.startWatching();
            }
            
            console.log('Sync Manager initialized');
        } catch (error) {
            console.error('Failed to initialize Sync Manager:', error);
        }
    }

    /**
     * Load sync settings
     */
    async loadSyncSettings() {
        try {
            // Get website code path from settings or default
            this.websiteCodePath = await this.getSetting('website_code_path') || 
                                   path.join(process.cwd(), '../');
            
            this.lastSyncTime = await this.getSetting('last_sync');
            
            console.log('Website code path:', this.websiteCodePath);
        } catch (error) {
            console.error('Failed to load sync settings:', error);
        }
    }

    /**
     * Start watching for file changes
     */
    async startWatching() {
        if (this.isWatching || !this.websiteCodePath) {
            return;
        }

        try {
            // Watch for changes in website files
            this.watcher = chokidar.watch([
                path.join(this.websiteCodePath, 'index-pro.html'),
                path.join(this.websiteCodePath, 'cinemahub-wordpress-theme/**/*'),
                path.join(this.websiteCodePath, '**/*.css'),
                path.join(this.websiteCodePath, '**/*.js'),
                path.join(this.websiteCodePath, '**/*.php')
            ], {
                ignored: [
                    '**/node_modules/**',
                    '**/.git/**',
                    '**/dist/**',
                    '**/build/**',
                    '**/*.log'
                ],
                persistent: true,
                ignoreInitial: true
            });

            this.watcher
                .on('change', (filePath) => this.onFileChanged(filePath))
                .on('add', (filePath) => this.onFileAdded(filePath))
                .on('unlink', (filePath) => this.onFileRemoved(filePath))
                .on('error', (error) => this.onWatchError(error));

            this.isWatching = true;
            console.log('Started watching for file changes');
            
            // Notify UI
            this.notifyUI('watch-started');
        } catch (error) {
            console.error('Failed to start watching:', error);
            throw error;
        }
    }

    /**
     * Stop watching for file changes
     */
    async stopWatching() {
        if (this.watcher) {
            await this.watcher.close();
            this.watcher = null;
        }
        
        this.isWatching = false;
        console.log('Stopped watching for file changes');
        
        // Notify UI
        this.notifyUI('watch-stopped');
    }

    /**
     * Handle file changed
     */
    onFileChanged(filePath) {
        console.log('File changed:', filePath);
        this.changedFiles.add(filePath);
        this.queueSync(filePath, 'changed');
        
        // Notify UI
        this.notifyUI('file-changed', { filePath });
    }

    /**
     * Handle file added
     */
    onFileAdded(filePath) {
        console.log('File added:', filePath);
        this.changedFiles.add(filePath);
        this.queueSync(filePath, 'added');
        
        // Notify UI
        this.notifyUI('file-added', { filePath });
    }

    /**
     * Handle file removed
     */
    onFileRemoved(filePath) {
        console.log('File removed:', filePath);
        this.changedFiles.add(filePath);
        this.queueSync(filePath, 'removed');
        
        // Notify UI
        this.notifyUI('file-removed', { filePath });
    }

    /**
     * Handle watch error
     */
    onWatchError(error) {
        console.error('File watcher error:', error);
        
        // Notify UI
        this.notifyUI('watch-error', { error: error.message });
    }

    /**
     * Queue file for sync
     */
    queueSync(filePath, action) {
        const syncItem = {
            filePath,
            action,
            timestamp: new Date().toISOString()
        };

        this.syncQueue.push(syncItem);
        
        // Auto-sync if enabled and not already syncing
        const autoSync = this.getSetting('auto_sync');
        if (autoSync === 'true' && !this.syncInProgress) {
            // Debounce sync - wait 5 seconds for more changes
            clearTimeout(this.syncTimeout);
            this.syncTimeout = setTimeout(() => {
                this.performSync();
            }, 5000);
        }
    }

    /**
     * Perform sync operation
     */
    async performSync() {
        if (this.syncInProgress || this.syncQueue.length === 0) {
            return;
        }

        this.syncInProgress = true;
        
        try {
            console.log('Starting sync operation...');
            
            // Notify UI
            this.notifyUI('sync-started');
            
            const startTime = new Date();
            let syncedFiles = 0;
            let errors = [];

            // Process sync queue
            for (const item of this.syncQueue) {
                try {
                    await this.syncFile(item);
                    syncedFiles++;
                } catch (error) {
                    console.error(`Failed to sync ${item.filePath}:`, error);
                    errors.push(`${item.filePath}: ${error.message}`);
                }
            }

            const endTime = new Date();
            const duration = endTime - startTime;

            // Update database with sync results
            await this.updateDatabase();
            
            // Log sync operation
            await this.logSyncOperation('success', syncedFiles, errors, startTime, endTime);
            
            // Clear sync queue
            this.syncQueue = [];
            this.changedFiles.clear();
            
            // Update last sync time
            this.lastSyncTime = endTime.toISOString();
            await this.setSetting('last_sync', this.lastSyncTime);
            
            console.log(`Sync completed: ${syncedFiles} files synced in ${duration}ms`);
            
            // Notify UI
            this.notifyUI('sync-completed', {
                filesCount: syncedFiles,
                duration,
                errors: errors.length
            });
            
        } catch (error) {
            console.error('Sync operation failed:', error);
            
            // Log failed sync
            await this.logSyncOperation('failed', 0, [error.message], new Date(), new Date());
            
            // Notify UI
            this.notifyUI('sync-failed', { error: error.message });
            
        } finally {
            this.syncInProgress = false;
        }
    }

    /**
     * Sync individual file
     */
    async syncFile(syncItem) {
        const { filePath, action } = syncItem;
        
        switch (action) {
            case 'changed':
            case 'added':
                await this.uploadFile(filePath);
                break;
            case 'removed':
                await this.removeFile(filePath);
                break;
        }
    }

    /**
     * Upload file to server
     */
    async uploadFile(filePath) {
        try {
            // Read file content
            const content = await fs.readFile(filePath, 'utf8');
            const relativePath = path.relative(this.websiteCodePath, filePath);
            
            // Get server settings
            const serverUrl = await this.getSetting('server_url');
            const username = await this.getSetting('server_username');
            const password = await this.getSetting('server_password');
            
            if (!serverUrl) {
                throw new Error('Server URL not configured');
            }

            // Upload via API
            const response = await axios.post(`${serverUrl}/api/upload`, {
                path: relativePath,
                content: content,
                timestamp: new Date().toISOString()
            }, {
                auth: username && password ? { username, password } : undefined,
                timeout: 30000
            });

            if (response.status !== 200) {
                throw new Error(`Upload failed: ${response.statusText}`);
            }

            console.log(`Uploaded: ${relativePath}`);
            
        } catch (error) {
            console.error(`Failed to upload ${filePath}:`, error);
            throw error;
        }
    }

    /**
     * Remove file from server
     */
    async removeFile(filePath) {
        try {
            const relativePath = path.relative(this.websiteCodePath, filePath);
            
            // Get server settings
            const serverUrl = await this.getSetting('server_url');
            const username = await this.getSetting('server_username');
            const password = await this.getSetting('server_password');
            
            if (!serverUrl) {
                throw new Error('Server URL not configured');
            }

            // Remove via API
            const response = await axios.delete(`${serverUrl}/api/file`, {
                data: { path: relativePath },
                auth: username && password ? { username, password } : undefined,
                timeout: 30000
            });

            if (response.status !== 200) {
                throw new Error(`Remove failed: ${response.statusText}`);
            }

            console.log(`Removed: ${relativePath}`);
            
        } catch (error) {
            console.error(`Failed to remove ${filePath}:`, error);
            throw error;
        }
    }

    /**
     * Update database with latest content
     */
    async updateDatabase() {
        try {
            // This would update the database with any changes from the website files
            // For example, parsing HTML files for movie/series data
            
            const indexPath = path.join(this.websiteCodePath, 'index-pro.html');
            if (await fs.pathExists(indexPath)) {
                // Parse index.html for any embedded data
                // This is a placeholder - implement based on your needs
                console.log('Database updated from website files');
            }
            
        } catch (error) {
            console.error('Failed to update database:', error);
        }
    }

    /**
     * Log sync operation
     */
    async logSyncOperation(status, filesCount, errors, startTime, endTime) {
        try {
            // This would log to the database
            const logData = {
                status,
                files_synced: filesCount,
                errors: errors.join('\n'),
                started_at: startTime.toISOString(),
                completed_at: endTime.toISOString()
            };
            
            console.log('Sync operation logged:', logData);
            
        } catch (error) {
            console.error('Failed to log sync operation:', error);
        }
    }

    /**
     * Get sync status
     */
    getSyncStatus() {
        return {
            isWatching: this.isWatching,
            syncInProgress: this.syncInProgress,
            lastSyncTime: this.lastSyncTime,
            queuedFiles: this.syncQueue.length,
            changedFiles: Array.from(this.changedFiles)
        };
    }

    /**
     * Manual sync trigger
     */
    async triggerSync() {
        if (this.syncInProgress) {
            throw new Error('Sync already in progress');
        }

        // Add all changed files to queue if not already there
        for (const filePath of this.changedFiles) {
            if (!this.syncQueue.find(item => item.filePath === filePath)) {
                this.queueSync(filePath, 'changed');
            }
        }

        await this.performSync();
    }

    /**
     * Set website code path
     */
    async setWebsiteCodePath(newPath) {
        if (!await fs.pathExists(newPath)) {
            throw new Error('Path does not exist');
        }

        // Stop current watching
        await this.stopWatching();
        
        // Update path
        this.websiteCodePath = newPath;
        await this.setSetting('website_code_path', newPath);
        
        // Restart watching if it was active
        if (this.isWatching) {
            await this.startWatching();
        }
    }

    /**
     * Export website files
     */
    async exportWebsite(exportPath) {
        try {
            // Copy all website files to export location
            await fs.copy(this.websiteCodePath, exportPath, {
                filter: (src) => {
                    // Exclude certain files/folders
                    const relativePath = path.relative(this.websiteCodePath, src);
                    return !relativePath.includes('node_modules') &&
                           !relativePath.includes('.git') &&
                           !relativePath.includes('cinemahub-desktop-app');
                }
            });
            
            console.log('Website exported to:', exportPath);
            return true;
            
        } catch (error) {
            console.error('Failed to export website:', error);
            throw error;
        }
    }

    /**
     * Create backup
     */
    async createBackup(backupPath) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupDir = path.join(backupPath, `cinemahub-backup-${timestamp}`);
            
            await this.exportWebsite(backupDir);
            
            console.log('Backup created:', backupDir);
            return backupDir;
            
        } catch (error) {
            console.error('Failed to create backup:', error);
            throw error;
        }
    }

    /**
     * Notify UI of events
     */
    notifyUI(event, data = {}) {
        // Send to renderer process
        if (typeof window !== 'undefined' && window.app) {
            window.app.onSyncEvent(event, data);
        }
    }

    /**
     * Helper methods for settings
     */
    async getSetting(key) {
        // This would get from database or electron store
        const { ipcRenderer } = require('electron');
        return await ipcRenderer.invoke('store-get', key);
    }

    async setSetting(key, value) {
        // This would set in database or electron store
        const { ipcRenderer } = require('electron');
        return await ipcRenderer.invoke('store-set', key, value);
    }

    /**
     * Cleanup
     */
    async cleanup() {
        await this.stopWatching();
        clearTimeout(this.syncTimeout);
        console.log('Sync Manager cleaned up');
    }
}

// Export singleton instance
const syncManager = new SyncManager();
module.exports = syncManager;
