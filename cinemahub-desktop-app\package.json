{"name": "cinemahub-pro-desktop", "version": "1.0.0", "description": "CinemaHub Pro - برنامج إدارة الأفلام والمسلسلات لسطح المكتب", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never"}, "keywords": ["cinema", "movies", "series", "desktop", "admin", "management"], "author": "CinemaHub Team", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"axios": "^1.5.0", "express": "^4.18.2", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "chokidar": "^3.5.3", "node-cron": "^3.0.2", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "electron-store": "^8.1.0", "electron-updater": "^6.1.4"}, "build": {"appId": "com.cinemahub.desktop", "productName": "CinemaHub Pro Desktop", "directories": {"output": "dist"}, "files": ["**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64"]}], "icon": "assets/icon.ico", "publisherName": "CinemaHub Team", "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "nsis": {"oneClick": false, "allowElevation": true, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "CinemaHub Pro", "installerIcon": "assets/icon.ico", "uninstallerIcon": "assets/icon.ico", "installerHeaderIcon": "assets/icon.ico", "deleteAppDataOnUninstall": false, "runAfterFinish": true, "menuCategory": "Entertainment", "artifactName": "${productName}-Setup-${version}.${ext}", "perMachine": false, "installerLanguages": ["en", "ar"], "language": "2052"}, "portable": {"artifactName": "${productName}-${version}-Portable.${ext}"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.entertainment", "hardenedRuntime": true, "gatekeeperAssess": false, "artifactName": "${productName}-${version}-${arch}.${ext}"}, "dmg": {"title": "${productName} ${version}", "icon": "assets/icon.icns", "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}], "window": {"width": 540, "height": 380}, "artifactName": "${productName}-${version}.${ext}"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}, {"target": "tar.gz", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "AudioVideo", "synopsis": "Professional desktop application for managing movie and TV series websites", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "appImage": {"artifactName": "${productName}-${version}-${arch}.${ext}"}, "deb": {"packageCategory": "video", "priority": "optional", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "rpm": {"packageCategory": "Applications/Multimedia", "artifactName": "${productName}-${version}-${arch}.${ext}"}}, "homepage": "https://cinemahub-pro.com", "repository": {"type": "git", "url": "https://github.com/cinemahub/desktop-app.git"}, "bugs": {"url": "https://github.com/cinemahub/desktop-app/issues"}}