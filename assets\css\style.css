/* ===== CSS Variables ===== */
:root {
    --primary-color: #e50914;
    --secondary-color: #221f1f;
    --dark-color: #141414;
    --light-color: #ffffff;
    --gray-color: #757575;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;

    --gradient-primary: linear-gradient(135deg, #e50914 0%, #b20710 100%);
    --gradient-dark: linear-gradient(135deg, #141414 0%, #000000 100%);
    --gradient-overlay: linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.8) 100%);

    --font-family: 'Cairo', sans-serif;
    --font-size-base: 14px;
    --font-size-small: 12px;
    --font-size-large: 16px;
    --font-size-xl: 18px;
    --font-size-xxl: 20px;
    --border-radius: 6px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
    --container-max-width: 1200px;
    --spacing-small: 8px;
    --spacing-medium: 16px;
    --spacing-large: 24px;
}

/* ===== Global Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--dark-color);
    color: var(--light-color);
    line-height: 1.5;
    overflow-x: hidden;
    font-size: var(--font-size-base);
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    font-size: 16px; /* Base font size */
}

/* تحسين عرض النصوص */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* تحسين أحجام العناصر للشاشات الصغيرة */
@media (max-width: 1200px) {
    html {
        font-size: 15px;
    }
}

@media (max-width: 992px) {
    html {
        font-size: 14px;
    }
}

@media (max-width: 768px) {
    html {
        font-size: 13px;
    }
}

@media (max-width: 576px) {
    html {
        font-size: 12px;
    }
}

/* ===== Loading Screen ===== */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-dark);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loading-spinner {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.loading-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.loading-logo-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    animation: loadingPulse 2s ease-in-out infinite;
    box-shadow: 0 8px 32px rgba(229, 9, 20, 0.4);
}

.loading-logo-icon .fa-film {
    font-size: 40px;
    color: var(--light-color);
    z-index: 2;
}

.loading-logo-icon .logo-play {
    position: absolute;
    top: -8px;
    right: -8px;
    font-size: 24px !important;
    color: #ffd700;
    animation: playPulse 1.5s ease-in-out infinite;
}

.loading-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    line-height: 1.2;
}

.loading-text .brand-text {
    font-size: 2rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 5px;
}

.loading-text .brand-subtitle {
    font-size: 1rem;
    color: var(--gray-color);
    font-weight: 400;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(229, 9, 20, 0.3);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes loadingPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 8px 32px rgba(229, 9, 20, 0.4);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 12px 40px rgba(229, 9, 20, 0.6);
    }
}

/* ===== Header Styles ===== */
.header {
    background: rgba(20, 20, 20, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: var(--transition);
}

.header.scrolled {
    background: rgba(20, 20, 20, 0.98);
    box-shadow: var(--box-shadow);
}

.navbar {
    padding: 0.5rem 0;
}

.navbar-brand {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--light-color) !important;
    transition: var(--transition);
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: var(--gradient-primary);
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
    animation: logoGlow 3s ease-in-out infinite alternate;
}

.logo-icon .fa-film {
    font-size: 20px;
    color: var(--light-color);
    z-index: 2;
}

.logo-play {
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 16px !important;
    color: #ffd700;
    animation: playPulse 2s ease-in-out infinite;
    z-index: 3;
}

.logo-text {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.brand-text {
    font-size: var(--font-size-xl);
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    letter-spacing: -0.5px;
}

.brand-subtitle {
    font-size: var(--font-size-small);
    color: var(--gray-color);
    font-weight: 400;
    margin-top: -2px;
}

/* Logo Animations */
@keyframes logoGlow {
    0% {
        box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
    }
    100% {
        box-shadow: 0 6px 25px rgba(229, 9, 20, 0.6);
    }
}

@keyframes playPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
}

@keyframes logoEntrance {
    0% {
        opacity: 0;
        transform: translateY(-20px) scale(0.8);
    }
    50% {
        opacity: 0.7;
        transform: translateY(-5px) scale(1.1);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* تطبيق تأثير الدخول على اللوجو */
.logo-container {
    animation: logoEntrance 1s ease-out;
}

/* تأثير إضافي عند hover على النافبار */
.navbar-brand:hover .logo-icon {
    transform: rotate(5deg);
}

.navbar-brand:hover .logo-play {
    animation-duration: 0.5s;
}

.navbar-nav .nav-link {
    color: var(--light-color) !important;
    font-weight: 500;
    margin: 0 6px;
    padding: 6px 12px !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
    font-size: var(--font-size-base);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    background: var(--gradient-primary);
    transform: translateY(-2px);
}

.navbar-nav .nav-link i {
    margin-left: 6px;
    font-size: var(--font-size-small);
}

.dropdown-menu {
    background: rgba(34, 31, 31, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.dropdown-item {
    color: var(--light-color) !important;
    padding: 8px 16px;
    transition: var(--transition);
    font-size: var(--font-size-small);
}

.dropdown-item:hover {
    background: var(--primary-color);
    color: var(--light-color) !important;
}

/* ===== Search Form ===== */
.search-form {
    margin-right: 20px;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--light-color);
    border-radius: 20px;
    padding: 8px 40px 8px 16px;
    width: 250px;
    transition: var(--transition);
    font-size: var(--font-size-small);
}

.search-input:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
    color: var(--light-color);
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-btn {
    position: absolute;
    left: 8px;
    background: transparent;
    border: none;
    color: var(--light-color);
    padding: 6px;
    border-radius: 50%;
    transition: var(--transition);
    font-size: var(--font-size-small);
}

.search-btn:hover {
    background: var(--primary-color);
    color: var(--light-color);
}

/* ===== Hero Section ===== */
.hero-section {
    height: 50vh;
    position: relative;
    margin-top: 70px;
    overflow: hidden;
}

.hero-slider {
    height: 100%;
}

.hero-slide {
    height: 100%;
    background-size: cover;
    background-position: center;
    position: relative;
    display: flex;
    align-items: center;
}

.hero-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-overlay);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    color: var(--light-color);
    max-width: 500px;
    padding: 0 var(--spacing-large);
}

.hero-title {
    font-size: var(--font-size-xxl);
    font-weight: 600;
    margin-bottom: var(--spacing-medium);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    line-height: 1.3;
}

.hero-description {
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-large);
    opacity: 0.9;
    line-height: 1.4;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-small);
    flex-wrap: wrap;
}

.btn-hero {
    padding: 8px 20px;
    border-radius: 20px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: var(--transition);
    font-size: var(--font-size-small);
}

.btn-hero.primary {
    background: var(--gradient-primary);
    color: var(--light-color);
}

.btn-hero.secondary {
    background: rgba(255, 255, 255, 0.2);
    color: var(--light-color);
    backdrop-filter: blur(10px);
}

.btn-hero:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

/* ===== Swiper Navigation ===== */
.swiper-button-next,
.swiper-button-prev {
    color: var(--light-color);
    background: rgba(0, 0, 0, 0.5);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    transition: var(--transition);
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
    background: var(--primary-color);
}

.swiper-pagination-bullet {
    background: rgba(255, 255, 255, 0.5);
    opacity: 1;
}

.swiper-pagination-bullet-active {
    background: var(--primary-color);
}

/* ===== Main Content ===== */
.main-content {
    padding: var(--spacing-large) 0;
}

.content-section {
    margin-bottom: var(--spacing-large);
}

.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-medium);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-medium);
    padding-bottom: var(--spacing-small);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section-title {
    font-size: var(--font-size-large);
    font-weight: 600;
    color: var(--light-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-small);
}

.section-title i {
    color: var(--primary-color);
}

.view-all-btn {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: var(--transition);
    font-size: var(--font-size-small);
}

.view-all-btn:hover {
    color: var(--light-color);
    transform: translateX(-5px);
}

/* ===== Movies Grid ===== */
.movies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: var(--spacing-medium);
    padding: var(--spacing-medium) 0;
}

.movie-card {
    background: var(--secondary-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
    cursor: pointer;
}

.movie-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.3);
}

.movie-poster {
    position: relative;
    overflow: hidden;
    aspect-ratio: 2/3;
}

.movie-poster img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.movie-card:hover .movie-poster img {
    transform: scale(1.1);
}

.movie-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-overlay);
    opacity: 0;
    transition: var(--transition);
    display: flex;
    justify-content: center;
    align-items: center;
}

.movie-card:hover .movie-overlay {
    opacity: 1;
}

.play-btn {
    background: var(--primary-color);
    color: var(--light-color);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: var(--font-size-base);
    display: flex;
    justify-content: center;
    align-items: center;
    transition: var(--transition);
}

.play-btn:hover {
    transform: scale(1.1);
    background: #ff0a16;
}

.movie-info {
    padding: var(--spacing-small);
}

.movie-title {
    font-size: var(--font-size-small);
    font-weight: 500;
    margin-bottom: 4px;
    color: var(--light-color);
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.movie-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 11px;
    color: var(--gray-color);
}

.movie-year {
    background: rgba(255, 255, 255, 0.1);
    padding: 1px 6px;
    border-radius: 8px;
    font-size: 10px;
}

.movie-rating {
    display: flex;
    align-items: center;
    gap: 2px;
}

.movie-rating i {
    color: #ffd700;
    font-size: 10px;
}

/* ===== Quality Badge ===== */
.quality-badge {
    position: absolute;
    top: 6px;
    right: 6px;
    background: var(--primary-color);
    color: var(--light-color);
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
    z-index: 2;
}

/* ===== Footer ===== */
.footer {
    background: var(--gradient-dark);
    padding: var(--spacing-large) 0 var(--spacing-medium);
    margin-top: var(--spacing-large);
}

.footer-widget {
    margin-bottom: var(--spacing-medium);
}

.footer-widget h5,
.footer-widget h6 {
    color: var(--light-color);
    margin-bottom: var(--spacing-small);
    font-weight: 500;
    font-size: var(--font-size-base);
}

.footer-widget p {
    color: var(--gray-color);
    line-height: 1.5;
    font-size: var(--font-size-small);
}

.footer-logo {
    margin-bottom: var(--spacing-medium);
}

.footer-logo-icon {
    background: rgba(229, 9, 20, 0.1);
    border: 1px solid rgba(229, 9, 20, 0.3);
    animation: none; /* إزالة التأثير المتوهج في الفوتر */
}

.footer-logo-icon:hover {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
    animation: logoGlow 2s ease-in-out infinite alternate;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-link {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--light-color);
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition);
}

.social-link:hover {
    background: var(--primary-color);
    color: var(--light-color);
    transform: translateY(-3px);
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: var(--gray-color);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--primary-color);
}

.newsletter-form .input-group {
    margin-top: 15px;
}

.newsletter-form .form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--light-color);
}

.newsletter-form .form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    box-shadow: none;
    color: var(--light-color);
}

.newsletter-form .btn-primary {
    background: var(--gradient-primary);
    border: none;
}

.footer-divider {
    border-color: rgba(255, 255, 255, 0.1);
    margin: 40px 0 20px;
}

.footer-bottom {
    text-align: center;
}

.copyright {
    color: var(--gray-color);
    margin: 0;
}

.footer-bottom-links {
    list-style: none;
    display: flex;
    justify-content: center;
    gap: 30px;
    margin: 0;
    padding: 0;
}

.footer-bottom-links a {
    color: var(--gray-color);
    text-decoration: none;
    transition: var(--transition);
}

.footer-bottom-links a:hover {
    color: var(--primary-color);
}

/* ===== Back to Top Button ===== */
.back-to-top {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    color: var(--light-color);
    border: none;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(229, 9, 20, 0.4);
}

/* ===== Advanced Search Styles ===== */
.advanced-search {
    background: rgba(34, 31, 31, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 30px;
    margin: 20px 0;
    display: none;
}

.search-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    color: var(--light-color);
    margin-bottom: 8px;
    font-weight: 500;
}

.filter-group select,
.filter-group input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--light-color);
    padding: 10px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.filter-group select:focus,
.filter-group input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
    outline: none;
}

/* ===== Movie Detail Modal ===== */
.movie-modal {
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
}

.movie-modal .modal-content {
    background: var(--secondary-color);
    border: none;
    border-radius: var(--border-radius);
}

.movie-detail-header {
    position: relative;
    height: 400px;
    background-size: cover;
    background-position: center;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.movie-detail-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-overlay);
}

.movie-detail-content {
    position: relative;
    z-index: 2;
    padding: 30px;
    color: var(--light-color);
}

.movie-detail-info {
    padding: 30px;
}

.movie-servers {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin: 20px 0;
}

.server-btn {
    background: rgba(255, 255, 255, 0.1);
    color: var(--light-color);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.server-btn:hover,
.server-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

/* ===== Loading Animation ===== */
.lazy-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.lazy-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(229, 9, 20, 0.3);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* ===== Notification Styles ===== */
.notification {
    position: fixed;
    top: 100px;
    right: 20px;
    background: var(--secondary-color);
    color: var(--light-color);
    padding: 15px 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    z-index: 1050;
    transform: translateX(400px);
    transition: var(--transition);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.error {
    border-left: 4px solid var(--danger-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    :root {
        --font-size-base: 13px;
        --font-size-small: 11px;
        --font-size-large: 15px;
        --font-size-xl: 16px;
        --font-size-xxl: 18px;
        --spacing-small: 6px;
        --spacing-medium: 12px;
        --spacing-large: 18px;
    }

    .hero-section {
        height: 40vh;
        margin-top: 60px;
    }

    .hero-title {
        font-size: var(--font-size-xl);
    }

    .hero-description {
        font-size: var(--font-size-small);
    }

    .search-input {
        width: 180px;
        padding: 6px 30px 6px 12px;
    }

    .movies-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: var(--spacing-small);
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-small);
    }

    .section-title {
        font-size: var(--font-size-base);
    }

    .footer-bottom-links {
        flex-direction: column;
        gap: var(--spacing-small);
    }

    .search-filters {
        grid-template-columns: 1fr;
    }

    .container {
        padding: 0 var(--spacing-small);
    }

    .navbar {
        padding: 0.3rem 0;
    }

    .logo-container {
        gap: 8px;
    }

    .logo-icon {
        width: 35px;
        height: 35px;
        border-radius: 8px;
    }

    .logo-icon .fa-film {
        font-size: 16px;
    }

    .logo-play {
        font-size: 12px !important;
        top: -3px;
        right: -3px;
    }

    .brand-text {
        font-size: var(--font-size-base);
    }

    .brand-subtitle {
        font-size: 10px;
    }
}

@media (max-width: 576px) {
    :root {
        --font-size-base: 12px;
        --font-size-small: 10px;
        --font-size-large: 14px;
        --font-size-xl: 15px;
        --font-size-xxl: 16px;
        --spacing-small: 4px;
        --spacing-medium: 8px;
        --spacing-large: 12px;
    }

    .hero-content {
        padding: 0 var(--spacing-medium);
    }

    .hero-buttons {
        flex-direction: column;
        gap: var(--spacing-small);
    }

    .movies-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-small);
    }

    .search-form {
        margin-right: 0;
        margin-top: var(--spacing-small);
    }

    .search-input {
        width: 100%;
        font-size: 11px;
    }

    .movie-servers {
        justify-content: center;
    }

    .advanced-search {
        padding: var(--spacing-medium);
    }

    .movie-title {
        font-size: 11px;
        -webkit-line-clamp: 1;
        line-clamp: 1;
    }

    .movie-info {
        padding: 6px;
    }

    .quality-badge {
        font-size: 8px;
        padding: 1px 4px;
    }
}

/* ===== PWA Styles ===== */
.pwa-mode {
    padding-top: 0;
}

.pwa-mode .header {
    position: relative;
}

.offline {
    filter: grayscale(0.5);
}

.offline::before {
    content: 'وضع عدم الاتصال';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: var(--warning-color);
    color: var(--dark-color);
    text-align: center;
    padding: 5px;
    font-size: 0.9rem;
    font-weight: 600;
    z-index: 10000;
}

.update-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--secondary-color);
    color: var(--light-color);
    padding: 15px 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    z-index: 1050;
    border-left: 4px solid var(--info-color);
    animation: slideInRight 0.3s ease;
}

.update-content {
    display: flex;
    align-items: center;
    gap: 15px;
}

.update-content i {
    color: var(--info-color);
    animation: spin 2s linear infinite;
}

.update-content button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: var(--transition);
}

.update-content button:hover {
    background: #ff0a16;
}

.update-content button:last-child {
    background: transparent;
    color: var(--gray-color);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===== Custom Scrollbar ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--dark-color);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #ff0a16;
}

/* ===== Selection Styles ===== */
::selection {
    background: var(--primary-color);
    color: var(--light-color);
}

::-moz-selection {
    background: var(--primary-color);
    color: var(--light-color);
}

/* ===== Focus Styles ===== */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

button:focus,
.btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
}

/* ===== Print Styles ===== */
@media print {
    .header,
    .footer,
    .back-to-top,
    .pwa-install-button {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .movie-card {
        break-inside: avoid;
        margin-bottom: 20px;
    }
}

/* ===== High Contrast Mode ===== */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #ff0000;
        --secondary-color: #000000;
        --dark-color: #000000;
        --light-color: #ffffff;
        --gray-color: #808080;
    }

    .movie-card {
        border: 2px solid var(--light-color);
    }
}

/* ===== Reduced Motion ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .hero-slider {
        scroll-behavior: auto;
    }
}

/* ===== Dark Mode Support ===== */
@media (prefers-color-scheme: light) {
    .light-mode {
        --primary-color: #e50914;
        --secondary-color: #f8f9fa;
        --dark-color: #ffffff;
        --light-color: #000000;
        --gray-color: #6c757d;
    }
}

/* ===== Utility Classes for Better Control ===== */
.text-xs { font-size: 10px !important; }
.text-sm { font-size: 12px !important; }
.text-base { font-size: 14px !important; }
.text-lg { font-size: 16px !important; }
.text-xl { font-size: 18px !important; }

.p-xs { padding: 4px !important; }
.p-sm { padding: 8px !important; }
.p-md { padding: 12px !important; }
.p-lg { padding: 16px !important; }

.m-xs { margin: 4px !important; }
.m-sm { margin: 8px !important; }
.m-md { margin: 12px !important; }
.m-lg { margin: 16px !important; }

.gap-xs { gap: 4px !important; }
.gap-sm { gap: 8px !important; }
.gap-md { gap: 12px !important; }
.gap-lg { gap: 16px !important; }

/* تحسين عرض المحتوى على الشاشات الصغيرة */
.compact-mode {
    --font-size-base: 12px;
    --font-size-small: 10px;
    --font-size-large: 14px;
    --spacing-small: 4px;
    --spacing-medium: 8px;
    --spacing-large: 12px;
}

.compact-mode .movies-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 6px;
}

.compact-mode .movie-title {
    font-size: 10px;
    -webkit-line-clamp: 1;
    line-clamp: 1;
}

.compact-mode .movie-info {
    padding: 4px;
}

.compact-mode .section-title {
    font-size: 14px;
}

.compact-mode .hero-section {
    height: 30vh;
}

/* تحسين للشاشات الكبيرة جداً */
@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }

    .movies-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
}

/* تحسين للشاشات فائقة الصغر */
@media (max-width: 320px) {
    .movies-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 4px;
    }

    .movie-title {
        font-size: 9px;
    }

    .search-input {
        font-size: 10px;
        padding: 4px 25px 4px 8px;
    }

    .logo-container {
        gap: 6px;
    }

    .logo-icon {
        width: 30px;
        height: 30px;
        border-radius: 6px;
    }

    .logo-icon .fa-film {
        font-size: 14px;
    }

    .logo-play {
        font-size: 10px !important;
        top: -2px;
        right: -2px;
    }

    .brand-text {
        font-size: 12px;
    }

    .brand-subtitle {
        display: none; /* إخفاء العنوان الفرعي في الشاشات الصغيرة جداً */
    }

    .hero-title {
        font-size: 14px;
    }
}

/* ===== Demo Notice ===== */
.demo-notice {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(34, 31, 31, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid var(--primary-color);
    border-radius: var(--border-radius);
    padding: 20px;
    max-width: 500px;
    z-index: 10000;
    animation: slideDown 0.5s ease;
    box-shadow: 0 8px 32px rgba(229, 9, 20, 0.3);
}

.demo-content {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.demo-content i {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-top: 5px;
}

.demo-text h4 {
    color: var(--light-color);
    margin: 0 0 10px 0;
    font-size: var(--font-size-large);
}

.demo-text p {
    color: var(--gray-color);
    margin: 0;
    font-size: var(--font-size-small);
    line-height: 1.5;
}

.demo-close {
    background: none;
    border: none;
    color: var(--gray-color);
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: var(--transition);
    margin-left: auto;
}

.demo-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--light-color);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@media (max-width: 768px) {
    .demo-notice {
        left: 10px;
        right: 10px;
        transform: none;
        max-width: none;
        padding: 15px;
    }

    .demo-content {
        flex-direction: column;
        gap: 10px;
    }

    .demo-text h4 {
        font-size: var(--font-size-base);
    }

    .demo-text p {
        font-size: 11px;
    }
}

/* ===== Dynamic Text Size Classes ===== */
.large-text {
    --font-size-base: 16px;
    --font-size-small: 14px;
    --font-size-large: 18px;
    --font-size-xl: 20px;
    --font-size-xxl: 24px;
}

.medium-text {
    --font-size-base: 14px;
    --font-size-small: 12px;
    --font-size-large: 16px;
    --font-size-xl: 18px;
    --font-size-xxl: 20px;
}

.compact-card {
    transform: scale(0.95);
}

.compact-card .movie-title {
    font-size: 10px !important;
    -webkit-line-clamp: 1 !important;
    line-clamp: 1 !important;
}

.compact-card .movie-info {
    padding: 4px !important;
}

.compact-card .quality-badge {
    font-size: 8px !important;
    padding: 1px 4px !important;
}

/* تحسين عرض النصوص العربية */
.arabic-text {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    text-align: right;
    direction: rtl;
}

/* تحسين القراءة */
.readable-text {
    line-height: 1.6;
    letter-spacing: 0.5px;
    word-spacing: 1px;
}

/* تحسين الأداء للشاشات الصغيرة */
@media (max-width: 480px) {
    .movies-grid {
        grid-template-columns: repeat(auto-fill, minmax(90px, 1fr)) !important;
        gap: 4px !important;
    }

    .movie-card {
        border-radius: 4px;
    }

    .movie-title {
        font-size: 9px !important;
        line-height: 1.2 !important;
    }

    .section-title {
        font-size: 13px !important;
    }

    .hero-section {
        height: 35vh !important;
    }

    .navbar-brand .brand-text {
        font-size: 13px !important;
    }

    .search-input {
        font-size: 11px !important;
        padding: 5px 25px 5px 10px !important;
    }
}

/* ===== Font Size Control ===== */
.font-size-control {
    position: fixed;
    bottom: 30px;
    right: 30px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    z-index: 1000;
    opacity: 0.8;
    transition: var(--transition);
}

.font-size-control:hover {
    opacity: 1;
}

.font-size-btn {
    width: 40px;
    height: 40px;
    background: rgba(34, 31, 31, 0.9);
    color: var(--light-color);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    position: relative;
}

.font-size-btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: scale(1.1);
}

.font-size-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.font-size-btn i {
    font-size: 8px;
    margin-bottom: 2px;
}

.size-indicator {
    font-size: 10px;
    font-weight: bold;
    line-height: 1;
}

/* إخفاء أزرار التحكم على الشاشات الصغيرة جداً */
@media (max-width: 480px) {
    .font-size-control {
        bottom: 80px;
        right: 15px;
        gap: 3px;
    }

    .font-size-btn {
        width: 32px;
        height: 32px;
        font-size: 8px;
    }

    .font-size-btn i {
        font-size: 6px;
    }

    .size-indicator {
        font-size: 8px;
    }
}

@media (max-width: 320px) {
    .font-size-control {
        display: none;
    }
}
